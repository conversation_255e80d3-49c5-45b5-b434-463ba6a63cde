{% if section.settings.subheading != blank %}
  <p class="subheading" data-animation="subheading" data-animation-group="{{ section.id }}">
    {{ section.settings.subheading }}
  </p>
{% endif %}

{% if section.settings.heading != blank %}
  {% capture heading_tag %}{% render 'get-heading-tag' %}{% endcapture %}
  <{{ heading_tag }}
    class="heading {{ section.settings.heading_size }}"
    data-animation="heading"
    data-animation-group="{{ section.id }}"
  >
    {{ section.settings.heading }}
  </{{ heading_tag }}>
{% endif %}

{% if section.settings.content != blank %}
  <div
    class="prose  inline-block"
    data-animation="paragraph"
    data-animation-group="{{ section.id }}"
  >
    {{ section.settings.content }}
  </div>
{% endif %}

{% if button != false and section.settings.button_text != blank %}
  <div class="button-wrapper">
    <a
      href="{{ section.settings.button_url | default: section.settings.link | default: '#' }}"
      class="button {{ section.settings.button_style }}"
      style="{% render 'button-vars', background: section.settings.button_background_color, text: section.settings.button_text_color %}"
      data-animation="button"
      data-animation-group="{{ section.id }}"
    >
      {{ section.settings.button_text }}
    </a>
  </div>
{% endif %}
