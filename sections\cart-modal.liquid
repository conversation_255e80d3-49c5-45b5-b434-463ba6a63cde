<style>
  #shopify-section-{{ section.id }} .button-view-cart {
    {% render 'button-vars',
      background: section.settings.view_cart_background_color,
      text: section.settings.view_cart_text_color
    %}
  }

  #shopify-section-{{ section.id }} .button-checkout {
    {% render 'button-vars',
      background: section.settings.checkout_background_color,
      text: section.settings.checkout_text_color
    %}
  }
</style>

<cart-modal position="right" class="modal modal-drawer modal-drawer--right cart-modal" id="cart-modal">
  <div class="h-full" slot="content" tabindex="-1">
    {% if cart != empty %}
      <cart-form>
        <div class="flex flex-col h-full">
          <div
            class="flex flex-col grow pb-6 overflow-y-auto"
            id="CartModal-ScrollBody"
            data-preserve-scroll
          >
            <div class="modal-header px-[--cart-modal-px]">
              <div class="flex items-center">
                <h2 class="h4">
                  {{ 'sections.cart.title' | t }}
                </h2>

                <button
                  class="modal-close ml-auto relative -right-2"
                  data-button-close
                  aria-label="{{ 'accessibility.close_modal' | t }}"
                >
                  {% render 'icon-times' %}
                </button>
              </div>

              {% render 'free-shipping-indicator', class: 'mt-4' %}
            </div>

            <div class="">
              {% for item in cart.items %}
                {% assign index = item.index | plus: 1 %}
                {% render 'cart-modal-item',
                  item: item,
                  class: 'pt-6 px-[--cart-modal-px] mb-6 first:border-t-0 border-t border-separator',
                  index: index
                %}
              {% endfor %}
            </div>

            {% render 'cart-modal-product-recommendations' %}
          </div>

          <div class="mt-0 px-[--cart-modal-px] pt-6 sm:pt-8 pb-6 sm:pb-10 border-t">
            <div class="h6 leading-none flex items-center justify-between gap-4">
              <div>
                {{ 'sections.cart.total' | t }}
              </div>

              <div class="cart-total">{{ cart.total_price | money_with_currency }}</div>
            </div>

            {% if section.settings.show_tax_and_shipping_message %}
              <div class="text-xs md:text-sm text-foreground/75 mt-4">
                {% render 'cart-policy-text' %}
              </div>
            {% endif %}

            {% if section.settings.show_cart_note or section.settings.show_shipping_estimator %}
              <div class="flex flex-wrap gap-x-6 gap-y-4 my-4 md:my-6">
                {% if section.settings.show_cart_note %}
                  <modal-trigger class="contents" target="#cart-modal-add-order-note">
                    <button class="flex gap-3 text-xs md:text-sm hover:underline underline-offset-4">
                      <div class="icon-xs md:icon-sm">
                        {% render 'icon-pencil' %}
                      </div>
                      <span class="order-note-button-label">
                        {% if cart.note != blank %}
                          {{ 'sections.cart.edit_note' | t }}
                        {% else %}
                          {{ 'sections.cart.add_note' | t }}
                        {% endif %}
                      </span>
                    </button>
                  </modal-trigger>
                {% endif %}

                {% if section.settings.show_shipping_estimator %}
                  <modal-trigger class="contents" target="#cart-modal-estimate-shipping">
                    <button class="flex gap-3 text-xs md:text-sm hover:underline underline-offset-4">
                      <div class="icon-xs md:icon-sm">
                        {% render 'icon-truck' %}
                      </div>
                      <span>{{ 'sections.cart.estimate_shipping' | t }}</span>
                    </button>
                  </modal-trigger>
                {% endif %}
              </div>
            {% endif %}

            <div class="cart-modal-buttons flex flex-wrap gap-2 md:gap-3 mt-6">
              {% if section.settings.show_view_cart_button or section.settings.show_checkout_button == false %}
                <a
                  data-instant
                  href="{{ routes.cart_url }}"
                  class="button button-secondary button-view-cart min-w-fit max-sm:text-xs flex-1"
                >
                  {{ 'sections.cart.view_cart' | t }}
                </a>
              {% endif %}

              {% if section.settings.show_checkout_button %}
                {% form 'cart', cart, data-form-button-loading: true, class: 'contents' %}
                  <button
                    type="submit"
                    name="checkout"
                    class="button button-primary button-checkout min-w-fit max-sm:text-xs flex-1"
                  >
                    {{ 'sections.cart.checkout' | t }}
                  </button>
                {% endform %}
              {% endif %}
            </div>
          </div>
        </div>
      </cart-form>

      <order-note-drawer
        class="modal modal--absolute modal-drawer modal-drawer--bottom cart-modal-child"
        position="bottom"
        id="cart-modal-add-order-note"
        animation="fade-in"
      >
        <div slot="content" tabindex="-1" class="px-8 md:px-12 py-8 md:py-10">
          <div class="flex justify-between items-center mb-6 md:mb-8">
            <div class="h5">{{ 'sections.cart.note' | t }}</div>
            <button class="modal-close" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
              {% render 'icon-times' %}
            </button>
          </div>
          <form data-form-button-loading>
            <div class="form-floating">
              <textarea
                class="input"
                name="note"
                id="CartModal-Note"
                placeholder="{{ 'sections.cart.note' | t }}"
                rows="4"
              >{{ cart.note }}</textarea>
              <label for="CartModal-Note">{{ 'sections.cart.note' | t }}</label>
            </div>
            <button type="submit" class="button w-full mt-6 md:mt-8">
              {{ 'sections.cart.save_note_button' | t }}
            </button>
            <div class="message message-danger text-danger mt-6 hidden">
              {{ 'sections.cart.save_note_failed' | t }}
            </div>
          </form>
        </div>
      </order-note-drawer>

      <modal-drawer
        class="modal modal--absolute modal-drawer modal-drawer--bottom cart-modal-child"
        position="bottom"
        id="cart-modal-estimate-shipping"
        animation="fade-in"
      >
        <div slot="content" tabindex="-1" class="p-8 md:p-12">
          <div class="flex justify-between items-center mb-6 md:mb-8">
            <div class="heading text-lg md:text-h5">
              {{ 'sections.shipping_estimator.title' | t }}
            </div>
            <button class="modal-close" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
              {% render 'icon-times' %}
            </button>
          </div>

          {% render 'shipping-estimator' %}
        </div>
      </modal-drawer>
    {% else %}
      <button
        class="modal-close absolute right-6 top-6 md:right-8 md:top-8"
        data-button-close
        aria-label="{{ 'accessibility.close_modal' | t }}"
      >
        {% render 'icon-times' %}
      </button>

      <div class="text-center h-full flex flex-col items-center justify-center">
        <p class="text-h4">{{ 'sections.cart.empty' | t }}</p>
        <a class="button button-primary mt-8" href="{{ settings.cart_empty_button_link }}">
          {{ 'general.continue_shopping' | t }}
        </a>
      </div>
    {% endif %}
  </div>
</cart-modal>

{% schema %}
{
  "name": "t:sections.cart-modal.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_tax_and_shipping_message",
      "label": "t:sections.cart-modal.settings.show_tax_and_shipping_message.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_cart_note",
      "label": "t:sections.cart-modal.settings.show_cart_note.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_shipping_estimator",
      "label": "t:sections.cart-modal.settings.show_shipping_estimator.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_view_cart_button",
      "label": "t:sections.cart-modal.settings.show_view_cart_button.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_checkout_button",
      "label": "t:sections.cart-modal.settings.show_checkout_button.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.cart-modal.headers.product_recommendations"
    },
    {
      "type": "text",
      "id": "product_recommendations_heading",
      "label": "t:sections.cart-modal.settings.product_recommendations_heading.label",
      "default": "You may also like"
    },
    {
      "type": "product_list",
      "id": "product_recommendations",
      "label": "t:sections.cart-modal.settings.product_recommendations.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.button_colors"
    },
    {
      "type": "color",
      "id": "view_cart_background_color",
      "label": "t:sections.cart-modal.settings.view_cart_background_color.label"
    },
    {
      "type": "color",
      "id": "view_cart_text_color",
      "label": "t:sections.cart-modal.settings.view_cart_text_color.label"
    },
    {
      "type": "color",
      "id": "checkout_background_color",
      "label": "t:sections.cart-modal.settings.checkout_background_color.label"
    },
    {
      "type": "color",
      "id": "checkout_text_color",
      "label": "t:sections.cart-modal.settings.checkout_text_color.label"
    }
  ]
}
{% endschema %}
