{% liquid
  assign section_classes = ''

  if section.settings.remove_horizontal_space
    assign section_classes = section_classes | append: ' !px-0 '
  endif

  if section.settings.remove_vertical_space
    assign section_classes = section_classes | append: ' !py-0 '
  endif
%}

{% render 'section-bg-number-vars' %}

<div {% render 'section-attrs', class: section_classes %}>
  <div class="section-body">
    {{ section.settings.custom_liquid }}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.custom-liquid.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_horizontal_space",
      "label": "t:sections.custom-liquid.settings.remove_horizontal_space.label"
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_space",
      "label": "t:sections.custom-liquid.settings.remove_vertical_space.label"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    }
  ],
  "disabled_on": {
    "groups": [
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.custom-liquid.presets.name"
    }
  ]
}
{% endschema %}
