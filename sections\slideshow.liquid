{% liquid
  assign controls = section.settings.controls
  if section.settings.full_width == false
    assign is_block = true
  endif

  if section.settings.full_width
    assign image_sizes = '100vw'
  else
    capture image_sizes
      echo 'min('
      echo settings.page_width
      echo 'px, 100vw)'
    endcapture
  endif

  assign first_slide = section.blocks | first

  if first_slide and first_slide.settings.background_color != blank and first_slide.settings.background_color.rgba != '0 0 0 / 0.0'
    assign first_slide_bg = first_slide.settings.background_color
  else
    assign first_slide_bg = null
  endif
%}

<style>
  #shopify-section-{{ section.id }} {
    --slideshow-target-height: {% render 'rfs', value: section.settings.height, base_value: 560, breakpoint: 768 %};

    {% if section.settings.background_color != blank and section.settings.background_color.rgba != '0 0 0 / 0.0' %}
      --slideshow-background: {{ section.settings.background_color.rgb }};
    {% else %}
      --slideshow-background: 45 45 45;
    {% endif %}
  }
</style>

<div
  class="slideshow-element-wrapper relative {% if is_block %} slideshow-wrapper--block {% endif %}"
  {% if section.settings.enable_transparent_header %}
    enable-transparent-header
  {% endif %}
>
  {% if is_block %}
    <slideshow-element-background
      class="block absolute inset-0 bg-[rgb(var(--slideshow-background))] transition-colors duration-700 no-scroll-expand"
      {% if first_slide_bg %}
        style="background-color: {{ first_slide_bg }};"
      {% endif %}
    >
    </slideshow-element-background>
  {% endif %}

  <slideshow-element
    class="slideshow-element"
    speed="{{ section.settings.speed | times: 1000 }}"
    autoplay="{{ section.settings.autoplay }}"
    {% if section.settings.use_original_media_size %}
      adaptive-height
    {% endif %}
  >
    {% for block in section.blocks %}
      {% capture slide_background_style %}
        {% assign overlay_opacity = block.settings.overlay_opacity | divided_by: 100.0 %}
        {% render 'media-overlay-vars',
          type: block.settings.overlay_type,
          content_position: block.settings.desktop_content_position,
          color: block.settings.overlay_color,
          opacity: overlay_opacity,
          prefix: '--slide-background-overlay'
        %}
        {% render 'media-overlay-vars',
          type: block.settings.overlay_type,
          content_position: block.settings.mobile_content_position,
          color: block.settings.overlay_color,
          opacity: overlay_opacity,
          prefix: '--slide-background-mobile-overlay'
        %}
      {% endcapture %}

      {% liquid
        if forloop.first
          assign loading = 'eager'
          assign fetchpriority = 'high'
        else
          assign loading = 'lazy'
          assign fetchpriority = 'auto'
        endif
      %}

      <slideshow-slide
        class="
          slideshow-slide
          {% if forloop.first %}active slide-loading{% endif %}
          {% if forloop.index == 2 %}preload{% endif %}
        "
        style="--slide-content-max-width: {{ block.settings.content_width_maximum }}px;"
        {{ block.shopify_attributes }}
        background-animation="{{ section.settings.background_animation }}"
        content-animation="{{ section.settings.content_animation }}"
        {% if block.settings.background_color != blank and block.settings.background_color.rgba != '0 0 0 / 0.0' %}
          block-background="{{ block.settings.background_color }}"
        {% endif %}
      >
        {% if block.settings.video != blank %}
          <div
            class="slide-background"
            style="{{ slide_background_style }}"
          >
            {% if block.settings.mobile_video %}
              <div
                class="h-full max-md:hidden {% unless section.settings.use_original_media_size %} fit-cover {% endunless %}"
                style="--aspect-ratio: {{ block.settings.video.aspect_ratio }}"
              >
                {{
                  block.settings.video
                  | video_tag:
                    muted: true,
                    loop: true,
                    width: block.settings.video.sources[0].width,
                    height: block.settings.video.sources[0].height,
                    class: 'max-md:hidden',
                    image_size: '480x'
                }}
              </div>
              <div
                class="h-full md:hidden {% unless section.settings.use_original_media_size %} fit-cover {% endunless %}"
                style="--aspect-ratio: {{ block.settings.mobile_video.aspect_ratio }}"
              >
                {{
                  block.settings.mobile_video
                  | video_tag:
                    muted: true,
                    loop: true,
                    width: block.settings.mobile_video.sources[0].width,
                    height: block.settings.mobile_video.sources[0].height,
                    class: 'md:hidden',
                    image_size: '360x'
                }}
              </div>
            {% else %}
              <div
                class="h-full {% unless section.settings.use_original_media_size %} fit-cover {% endunless %}"
                style="--aspect-ratio: {{ block.settings.video.aspect_ratio }}"
              >
                {{
                  block.settings.video
                  | video_tag:
                    muted: true,
                    loop: true,
                    width: block.settings.video.sources[0].width,
                    height: block.settings.video.sources[0].height,
                    image_size: '480x'
                }}
              </div>
            {% endif %}
          </div>
        {% else %}
          <div
            class="slide-background"
            style="{{ slide_background_style }}"
          >
            {% if block.settings.image %}
              {% capture image %}
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    widths: '768, 1024, 1280, 1536, 1792, 2048, 2560, 3072, 3840',
                    sizes: image_sizes,
                    loading: loading,
                    fetchpriority: fetchpriority
                }}
              {% endcapture %}

              {% if block.settings.mobile_image %}
                <lqip-element class="image-loader md:hidden">
                  <picture>
                    <source
                      srcset="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="
                      media="(min-width: 768px)"
                    >
                    {{
                      block.settings.mobile_image
                      | image_url: width: 1536
                      | image_tag:
                        widths: '420, 840, 1072, 1304, 1536',
                        sizes: image_sizes,
                        loading: loading,
                        fetchpriority: fetchpriority
                    }}
                  </picture>
                </lqip-element>
                <lqip-element class="image-loader max-md:hidden">
                  <picture>
                    <source
                      srcset="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="
                      media="not all and (min-width: 768px)"
                    >
                    {{ image }}
                  </picture>
                </lqip-element>
              {% else %}
                <lqip-element class="image-loader absolute inset-0">{{ image }}</lqip-element>
              {% endif %}

            {% else %}
              {% render 'placeholder', type: 'lifestyle-1', class: 'placeholder--dark' %}
            {% endif %}
          </div>
        {% endif %}

        {% assign slide_content_tag = 'div' %}
        {% assign slide_content_href = '' %}

        {% if block.settings.button_text == blank and block.settings.button_link != blank %}
          {% assign slide_content_tag = 'a' %}
          {% assign slide_content_href = 'href="' | append: block.settings.button_link | append: '"' %}
        {% endif %}

        <{{ slide_content_tag }} {{ slide_content_href }} class="slide-content" data-instant>
          <div class="container">
            <div
              class="
                slide-content-inner
                mobile:content-align-{{ block.settings.mobile_content_position }}
                desktop:content-align-{{ block.settings.desktop_content_position }}
                desktop:content-text-{{ block.settings.desktop_text_alignment }}
                trim-margins
              "
              style="
                --color-foreground: {{ block.settings.color_text.rgb }};
                --color-headings: {{ block.settings.color_text.rgb }};
              "
            >
              {%- unless block.settings.subheading == empty -%}
                <div class="overflow-hidden mb-2 md:mb-4" data-slide-animation-element>
                  <div class="subheading text-sm md:text-base">
                    {{ block.settings.subheading }}
                  </div>
                </div>
              {%- endunless -%}

              {%- unless block.settings.heading == empty -%}
                <split-lines class="{{ block.settings.heading_size }}" data-slide-animation-element>
                  {% capture heading_tag %}{% render 'get-heading-tag', target: block %}{% endcapture %}
                  <{{ heading_tag }}>{{ block.settings.heading }}</{{ heading_tag }}>
                </split-lines>
              {%- endunless -%}

              {%- unless block.settings.text == empty -%}
                <split-lines
                  class="
                    mt-4 md:mt-6 leading-relaxed text-base md:text-lg
                    {% if block.settings.hide_text_on_mobile %} max-md:hidden {% endif %}
                  "
                  data-slide-animation-element
                >
                  <div class="">
                    {{ block.settings.text | newline_to_br }}
                  </div>
                </split-lines>
              {%- endunless -%}

              {%- unless block.settings.button_text == empty -%}
                <div class="slide-buttons">
                  <div
                    class="pb-2 -mb-2"
                    data-slide-animation-element
                  >
                    {% render 'button', target: block %}
                  </div>

                  {%- unless block.settings.button2_text == empty -%}
                    <div
                      class="pb-2 -mb-2"
                      data-slide-animation-element
                    >
                      {% render 'button',
                        defaults: false,
                        link: block.settings.button2_link,
                        text: block.settings.button2_text,
                        style: block.settings.button2_style,
                        text_color: block.settings.button2_text_color,
                        background_color: block.settings.button2_background_color,
                        size: block.settings.button_size
                      %}
                    </div>
                  {%- endunless -%}
                </div>
              {%- endunless -%}
            </div>
          </div>
        </{{ slide_content_tag }}>
      </slideshow-slide>
    {% endfor %}

    {% if controls == 'arrows' %}
      <slideshow-control-arrows class="slideshow-controls container" data-slideshow-controls>
        <div class="slide-counter" data-slide-counter>
          <svg viewBox="0 0 48 48">
            <circle cx="24" cy="24" r="22.5" fill="none" stroke="#fff7" stroke-width="2px"></circle>

            <circle
              cx="24"
              cy="24"
              r="22.5"
              fill="none"
              stroke="#fff"
              stroke-width="2px"
              pathLength="100"
              stroke-dasharray="100"
              stroke-dashoffset="100"
              transform="rotate(-90, 24, 24)"></circle>
          </svg>

          <span data-slider-counter-text></span>
        </div>

        <button
          class="button button-circle button-prev"
          data-slider-button-prev
          aria-label="{{ 'accessibility.previous' | t }}"
        >
          {% render 'icon-chevron' %}
        </button>

        <button
          class="button button-circle button-next"
          data-slider-button-next
          aria-label="{{ 'accessibility.next' | t }}"
        >
          {% render 'icon-chevron' %}
        </button>
      </slideshow-control-arrows>
    {% elsif controls == 'dots' %}
      <div class="slideshow-controls justify-center container" data-slideshow-controls>
        <slideshow-control-dots item-count="{{ section.blocks.size }}"></slideshow-control-dots>
      </div>
    {% endif %}
  </slideshow-element>
</div>

{% schema %}
{
  "name": "t:sections.slideshow.name",
  "class": "section-slideshow",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_transparent_header",
      "label": "t:sections.all.enable_transparent_header.label",
      "info": "t:sections.all.enable_transparent_header.info",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "use_original_media_size",
      "label": "t:sections.slideshow.settings.use_original_media_size.label",
      "default": false,
      "info": "t:sections.slideshow.settings.use_original_media_size.info"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.slideshow.settings.autoplay.label",
      "default": true
    },
    {
      "type": "range",
      "id": "speed",
      "min": 4,
      "max": 20,
      "step": 1,
      "unit": "s",
      "label": "t:sections.slideshow.settings.speed.label",
      "default": 5
    },
    {
      "type": "range",
      "id": "height",
      "label": "t:sections.slideshow.settings.height.label",
      "min": 560,
      "max": 1200,
      "step": 20,
      "unit": "px",
      "default": 700,
      "info": "t:sections.slideshow.settings.height.info"
    },
    {
      "type": "select",
      "id": "controls",
      "label": "t:sections.slideshow.settings.controls.label",
      "options": [
        {
          "value": "arrows",
          "label": "t:sections.slideshow.settings.controls.options__0.label"
        },
        {
          "value": "dots",
          "label": "t:sections.slideshow.settings.controls.options__1.label"
        }
      ],
      "default": "arrows"
    },
    {
      "type": "header",
      "content": "t:sections.slideshow.headers.animation"
    },
    {
      "type": "select",
      "id": "background_animation",
      "label": "t:sections.slideshow.settings.background_animation.label",
      "options": [
        {
          "value": "Fade",
          "label": "t:sections.slideshow.settings.background_animation.options__0.label"
        },
        {
          "value": "ZoomOut",
          "label": "t:sections.slideshow.settings.background_animation.options__1.label"
        },
        {
          "value": "Clip",
          "label": "t:sections.slideshow.settings.background_animation.options__2.label"
        }
      ],
      "default": "ZoomOut"
    },
    {
      "type": "select",
      "id": "content_animation",
      "label": "t:sections.slideshow.settings.content_animation.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.slideshow.settings.content_animation.options__0.label"
        },
        {
          "value": "FadeIn",
          "label": "t:sections.slideshow.settings.content_animation.options__1.label"
        },
        {
          "value": "AppearLineByLine",
          "label": "t:sections.slideshow.settings.content_animation.options__2.label"
        },
        {
          "value": "ClipLineByLine",
          "label": "t:sections.slideshow.settings.content_animation.options__3.label"
        }
      ],
      "default": "AppearLineByLine"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color",
      "default": "#262626",
      "info": "t:sections.slideshow.settings.background_color.info"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.slideshow.blocks.slide.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.slideshow.blocks.slide.settings.mobile_image.label"
        },
        {
          "type": "video",
          "id": "video",
          "label": "t:sections.slideshow.blocks.slide.settings.video.label",
          "info": "t:sections.slideshow.blocks.slide.settings.video.info"
        },
        {
          "type": "video",
          "id": "mobile_video",
          "label": "t:sections.slideshow.blocks.slide.settings.mobile_video.label"
        },
        {
          "type": "range",
          "id": "content_width_maximum",
          "min": 360,
          "max": 960,
          "step": 20,
          "unit": "px",
          "label": "t:sections.slideshow.blocks.slide.settings.content_width_maximum.label",
          "default": 640
        },
        {
          "type": "select",
          "id": "desktop_content_position",
          "label": "t:sections.slideshow.blocks.slide.settings.desktop_content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.position.bottom_right.label"
            }
          ],
          "default": "middle-center"
        },
        {
          "type": "select",
          "id": "mobile_content_position",
          "label": "t:sections.slideshow.blocks.slide.settings.mobile_content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.position.bottom_right.label"
            }
          ],
          "default": "middle-center"
        },
        {
          "type": "select",
          "id": "desktop_text_alignment",
          "label": "t:sections.slideshow.blocks.slide.settings.desktop_text_alignment.label",
          "options": [
            {
              "value": "default",
              "label": "t:sections.slideshow.blocks.slide.settings.desktop_text_alignment.options__0.label"
            },
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.desktop_text_alignment.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.desktop_text_alignment.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.desktop_text_alignment.options__3.label"
            }
          ],
          "default": "default"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.content"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "t:sections.slideshow.blocks.slide.settings.subheading.label",
          "default": "Add a tagline"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.slideshow.blocks.slide.settings.heading.label",
          "default": "Slide title"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:sections.all.heading_size.label",
          "options": [
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__0.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__6.label"
            }
          ],
          "default": "h0"
        },
        {
          "type": "textarea",
          "id": "text",
          "label": "t:sections.slideshow.blocks.slide.settings.text.label"
        },
        {
          "type": "checkbox",
          "id": "hide_text_on_mobile",
          "label": "t:sections.slideshow.blocks.slide.settings.hide_text_on_mobile.label"
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "t:sections.all.button_size.label",
          "options": [
            {
              "value": "text-xs md:text-sm",
              "label": "t:sections.all.button_size.options__0.label"
            },
            {
              "value": "text-sm md:text-base",
              "label": "t:sections.all.button_size.options__1.label"
            },
            {
              "value": "text-base md:text-lg",
              "label": "t:sections.all.button_size.options__2.label"
            },
            {
              "value": "text-lg md:text-h5",
              "label": "t:sections.all.button_size.options__3.label"
            }
          ],
          "default": "text-sm md:text-base"
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.blocks.slide.settings.header.button1"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.slideshow.blocks.slide.settings.button_link.label"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "t:sections.slideshow.blocks.slide.settings.button_text.label",
          "default": "Button label",
          "info": "t:sections.slideshow.blocks.slide.settings.button_text.info"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "color",
          "label": "t:sections.slideshow.blocks.slide.settings.button_background_color.label",
          "id": "button_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.slideshow.blocks.slide.settings.button_text_color.label",
          "id": "button_text_color"
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.blocks.slide.settings.header.button2"
        },
        {
          "type": "url",
          "id": "button2_link",
          "label": "t:sections.slideshow.blocks.slide.settings.button_link.label"
        },
        {
          "type": "text",
          "id": "button2_text",
          "label": "t:sections.slideshow.blocks.slide.settings.button_text.label"
        },
        {
          "type": "select",
          "id": "button2_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "color",
          "label": "t:sections.slideshow.blocks.slide.settings.button_background_color.label",
          "id": "button2_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.slideshow.blocks.slide.settings.button_text_color.label",
          "id": "button2_text_color"
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.headers.colors"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "t:sections.all.background_color.label",
          "info": "t:sections.slideshow.blocks.slide.settings.background_color.info"
        },
        {
          "type": "color",
          "id": "color_text",
          "label": "t:sections.slideshow.blocks.slide.settings.color_text.label",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "t:sections.all.overlay_color.label",
          "default": "#000000"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.overlay"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "t:sections.all.overlay_opacity.label",
          "default": 30
        },
        {
          "type": "select",
          "id": "overlay_type",
          "label": "t:sections.all.overlay_type.label",
          "options": [
            {
              "value": "solid",
              "label": "t:sections.all.overlay_type.options__0.label"
            },
            {
              "value": "gradient",
              "label": "t:sections.all.overlay_type.options__1.label"
            }
          ],
          "default": "solid"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.seo"
        },
        {
          "type": "select",
          "id": "heading_html_tag",
          "label": "t:sections.all.heading_html_tag.label",
          "options": [
            {
              "value": "h1",
              "label": "h1"
            },
            {
              "value": "h2",
              "label": "h2"
            },
            {
              "value": "h3",
              "label": "h3"
            },
            {
              "value": "h4",
              "label": "h4"
            },
            {
              "value": "h5",
              "label": "h5"
            },
            {
              "value": "h6",
              "label": "h6"
            },
            {
              "value": "p",
              "label": "p"
            },
            {
              "value": "span",
              "label": "span"
            },
            {
              "value": "div",
              "label": "div"
            }
          ],
          "default": "p",
          "info": "t:sections.all.heading_html_tag.info"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.slideshow.presets__0.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
