/*! instant.page v5.2.0 - (C) 2019-2023 <PERSON> - https://instant.page/license */let G=null,$t,Pt,Dt,xt=65,ut,q,Ct=new Set;const Ht=1111;ae();function ae(){if(!document.createElement("link").relList.supports("prefetch"))return;const l="instantVaryAccept"in document.body.dataset||"Shopify"in window,g=navigator.userAgent.indexOf("Chrome/");if(g>-1&&(G=parseInt(navigator.userAgent.substring(g+7))),l&&G&&G<110)return;const A="instantMousedownShortcut"in document.body.dataset;$t="instantAllowQueryString"in document.body.dataset,Pt="instantAllowExternalLinks"in document.body.dataset,Dt="instantWhitelist"in document.body.dataset;const O={capture:!0,passive:!0};let N=!1,$=!1,P=!1;if("instantIntensity"in document.body.dataset){const y=document.body.dataset.instantIntensity;if(y.startsWith("mousedown"))N=!0,y=="mousedown-only"&&($=!0);else if(y.startsWith("viewport")){const b=navigator.connection&&navigator.connection.saveData,k=navigator.connection&&navigator.connection.effectiveType&&navigator.connection.effectiveType.includes("2g");!b&&!k&&(y=="viewport"?document.documentElement.clientWidth*document.documentElement.clientHeight<45e4&&(P=!0):y=="viewport-all"&&(P=!0))}else{const b=parseInt(y);isNaN(b)||(xt=b)}}if($||document.addEventListener("touchstart",oe,O),N?A||document.addEventListener("mousedown",ue,O):document.addEventListener("mouseover",ie,O),A&&document.addEventListener("mousedown",se,O),P){let y=window.requestIdleCallback;y||(y=b=>{b()}),y(function(){const k=new IntersectionObserver(I=>{I.forEach(W=>{if(W.isIntersecting){const V=W.target;k.unobserve(V),X(V.href)}})});document.querySelectorAll("a").forEach(I=>{J(I)&&k.observe(I)})},{timeout:1500})}}function oe(i){ut=performance.now();const l=i.target.closest("a");J(l)&&X(l.href,"high")}function ie(i){if(performance.now()-ut<Ht||!("closest"in i.target))return;const l=i.target.closest("a");J(l)&&(l.addEventListener("mouseout",ce,{passive:!0}),q=setTimeout(()=>{X(l.href,"high"),q=void 0},xt))}function ue(i){const l=i.target.closest("a");J(l)&&X(l.href,"high")}function ce(i){i.relatedTarget&&i.target.closest("a")==i.relatedTarget.closest("a")||q&&(clearTimeout(q),q=void 0)}function se(i){if(performance.now()-ut<Ht)return;const l=i.target.closest("a");if(i.which>1||i.metaKey||i.ctrlKey||!l)return;l.addEventListener("click",function(A){A.detail!=1337&&A.preventDefault()},{capture:!0,passive:!1,once:!0});const g=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,detail:1337});l.dispatchEvent(g)}function J(i){if(!(!i||!i.href)&&!(Dt&&!("instant"in i.dataset))&&!(i.origin!=location.origin&&(!(Pt||"instant"in i.dataset)||!G))&&["http:","https:"].includes(i.protocol)&&!(i.protocol=="http:"&&location.protocol=="https:")&&!(!$t&&i.search&&!("instant"in i.dataset))&&!(i.hash&&i.pathname+i.search==location.pathname+location.search)&&!("noInstant"in i.dataset))return!0}function X(i,l="auto"){if(Ct.has(i))return;const g=document.createElement("link");g.rel="prefetch",g.href=i,g.fetchPriority=l,g.as="document",document.head.appendChild(g),Ct.add(i)}/*! (c) Andrea Giammarchi @webreflection ISC */(function(){var i=function(e,t){var r=function(s){for(var o=0,c=s.length;o<c;o++)n(s[o])},n=function(s){var o=s.target,c=s.attributeName,v=s.oldValue;o.attributeChangedCallback(c,v,o.getAttribute(c))};return function(a,s){var o=a.constructor.observedAttributes;return o&&e(s).then(function(){new t(r).observe(a,{attributes:!0,attributeOldValue:!0,attributeFilter:o});for(var c=0,v=o.length;c<v;c++)a.hasAttribute(o[c])&&n({target:a,attributeName:o[c],oldValue:null})}),a}};function l(e,t){if(e){if(typeof e=="string")return g(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,t)}}function g(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function A(e,t){var r=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=l(e))||t&&e&&typeof e.length=="number"){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(v){throw v},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s=!0,o=!1,c;return{s:function(){r=r.call(e)},n:function(){var v=r.next();return s=v.done,v},e:function(v){o=!0,c=v},f:function(){try{!s&&r.return!=null&&r.return()}finally{if(o)throw c}}}}/*! (c) Andrea Giammarchi - ISC */var O=!0,N=!1,$="querySelectorAll",P=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:document,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:MutationObserver,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:["*"],s=function v(L,M,p,u,f,d){var w=A(L),C;try{for(w.s();!(C=w.n()).done;){var h=C.value;(d||$ in h)&&(f?p.has(h)||(p.add(h),u.delete(h),t(h,f)):u.has(h)||(u.add(h),p.delete(h),t(h,f)),d||v(h[$](M),M,p,u,f,O))}}catch(it){w.e(it)}finally{w.f()}},o=new n(function(v){if(a.length){var L=a.join(","),M=new Set,p=new Set,u=A(v),f;try{for(u.s();!(f=u.n()).done;){var d=f.value,w=d.addedNodes,C=d.removedNodes;s(C,L,M,p,N,N),s(w,L,M,p,O,N)}}catch(h){u.e(h)}finally{u.f()}}}),c=o.observe;return(o.observe=function(v){return c.call(o,v,{subtree:O,childList:O})})(r),o},y="querySelectorAll",b=self,k=b.document,I=b.Element,W=b.MutationObserver,V=b.Set,qt=b.WeakMap,ct=function(t){return y in t},st=[].filter,Z=function(e){var t=new qt,r=function(u){for(var f=0,d=u.length;f<d;f++)t.delete(u[f])},n=function(){for(var u=L.takeRecords(),f=0,d=u.length;f<d;f++)o(st.call(u[f].removedNodes,ct),!1),o(st.call(u[f].addedNodes,ct),!0)},a=function(u){return u.matches||u.webkitMatchesSelector||u.msMatchesSelector},s=function(u,f){var d;if(f)for(var w,C=a(u),h=0,it=c.length;h<it;h++)C.call(u,w=c[h])&&(t.has(u)||t.set(u,new V),d=t.get(u),d.has(w)||(d.add(w),e.handle(u,f,w)));else t.has(u)&&(d=t.get(u),t.delete(u),d.forEach(function(ne){e.handle(u,f,ne)}))},o=function(u){for(var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,d=0,w=u.length;d<w;d++)s(u[d],f)},c=e.query,v=e.root||k,L=P(s,v,W,c),M=I.prototype.attachShadow;return M&&(I.prototype.attachShadow=function(p){var u=M.call(this,p);return L.observe(u),u}),c.length&&o(v[y](c)),{drop:r,flush:n,observer:L,parse:o}},m=self,E=m.document,T=m.Map,ft=m.MutationObserver,j=m.Object,lt=m.Set,Wt=m.WeakMap,vt=m.Element,Vt=m.HTMLElement,dt=m.Node,ht=m.Error,pt=m.TypeError,jt=m.Reflect,R=j.defineProperty,Rt=j.keys,Ut=j.getOwnPropertyNames,D=j.setPrototypeOf,x=!self.customElements,gt=function(t){for(var r=Rt(t),n=[],a=new lt,s=r.length,o=0;o<s;o++){n[o]=t[r[o]];try{delete t[r[o]]}catch{a.add(o)}}return function(){for(var c=0;c<s;c++)a.has(c)||(t[r[c]]=n[c])}};if(x){var _=function(){var t=this.constructor;if(!tt.has(t))throw new pt("Illegal constructor");var r=tt.get(t);if(F)return mt(F,r);var n=yt.call(E,r);return mt(D(n,t.prototype),r)},yt=E.createElement,tt=new T,U=new T,bt=new T,H=new T,wt=[],Ft=function(t,r,n){var a=bt.get(n);if(r&&!a.isPrototypeOf(t)){var s=gt(t);F=D(t,a);try{new a.constructor}finally{F=null,s()}}var o="".concat(r?"":"dis","connectedCallback");o in a&&t[o]()},Qt=Z({query:wt,handle:Ft}),Bt=Qt.parse,F=null,et=function(t){if(!U.has(t)){var r,n=new Promise(function(a){r=a});U.set(t,{$:n,_:r})}return U.get(t).$},mt=i(et,ft);self.customElements={define:function(t,r){if(H.has(t))throw new ht('the name "'.concat(t,'" has already been used with this registry'));tt.set(r,t),bt.set(t,r.prototype),H.set(t,r),wt.push(t),et(t).then(function(){Bt(E.querySelectorAll(t))}),U.get(t)._(r)},get:function(t){return H.get(t)},whenDefined:et},R(_.prototype=Vt.prototype,"constructor",{value:_}),self.HTMLElement=_,E.createElement=function(e,t){var r=t&&t.is,n=r?H.get(r):H.get(e);return n?new n:yt.call(E,e)},"isConnected"in dt.prototype||R(dt.prototype,"isConnected",{configurable:!0,get:function(){return!(this.ownerDocument.compareDocumentPosition(this)&this.DOCUMENT_POSITION_DISCONNECTED)}})}else if(x=!self.customElements.get("extends-br"),x)try{var Et=function e(){return self.Reflect.construct(HTMLBRElement,[],e)};Et.prototype=HTMLLIElement.prototype;var St="extends-br";self.customElements.define("extends-br",Et,{extends:"br"}),x=E.createElement("br",{is:St}).outerHTML.indexOf(St)<0;var Ot=self.customElements,Kt=Ot.get,Yt=Ot.whenDefined;self.customElements.whenDefined=function(e){var t=this;return Yt.call(this,e).then(function(r){return r||Kt.call(t,e)})}}catch{}if(x){var At=function(t){var r=rt.get(t);Nt(r.querySelectorAll(this),t.isConnected)},S=self.customElements,Tt=E.createElement,zt=S.define,Gt=S.get,Jt=S.upgrade,Xt=jt||{construct:function(t){return t.call(this)}},Zt=Xt.construct,rt=new Wt,nt=new lt,Q=new T,B=new T,Lt=new T,K=new T,Mt=[],Y=[],It=function(t){return K.get(t)||Gt.call(S,t)},_t=function(t,r,n){var a=Lt.get(n);if(r&&!a.isPrototypeOf(t)){var s=gt(t);z=D(t,a);try{new a.constructor}finally{z=null,s()}}var o="".concat(r?"":"dis","connectedCallback");o in a&&t[o]()},te=Z({query:Y,handle:_t}),Nt=te.parse,ee=Z({query:Mt,handle:function(t,r){rt.has(t)&&(r?nt.add(t):nt.delete(t),Y.length&&At.call(Y,t))}}),re=ee.parse,kt=vt.prototype.attachShadow;kt&&(vt.prototype.attachShadow=function(e){var t=kt.call(this,e);return rt.set(this,t),t});var at=function(t){if(!B.has(t)){var r,n=new Promise(function(a){r=a});B.set(t,{$:n,_:r})}return B.get(t).$},ot=i(at,ft),z=null;Ut(self).filter(function(e){return/^HTML.*Element$/.test(e)}).forEach(function(e){var t=self[e];function r(){var n=this.constructor;if(!Q.has(n))throw new pt("Illegal constructor");var a=Q.get(n),s=a.is,o=a.tag;if(s){if(z)return ot(z,s);var c=Tt.call(E,o);return c.setAttribute("is",s),ot(D(c,n.prototype),s)}else return Zt.call(this,t,[],n)}R(r.prototype=t.prototype,"constructor",{value:r}),R(self,e,{value:r})}),E.createElement=function(e,t){var r=t&&t.is;if(r){var n=K.get(r);if(n&&Q.get(n).tag===e)return new n}var a=Tt.call(E,e);return r&&a.setAttribute("is",r),a},S.get=It,S.whenDefined=at,S.upgrade=function(e){var t=e.getAttribute("is");if(t){var r=K.get(t);if(r){ot(D(e,r.prototype),t);return}}Jt.call(S,e)},S.define=function(e,t,r){if(It(e))throw new ht("'".concat(e,"' has already been defined as a custom element"));var n,a=r&&r.extends;Q.set(t,a?{is:e,tag:a}:{is:"",tag:e}),a?(n="".concat(a,'[is="').concat(e,'"]'),Lt.set(n,t.prototype),K.set(e,t),Y.push(n)):(zt.apply(S,arguments),Mt.push(n=e)),at(e).then(function(){a?(Nt(E.querySelectorAll(n)),nt.forEach(At,[n])):re(E.querySelectorAll(n))}),B.get(e)._(t)}}})();
