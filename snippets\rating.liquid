{% liquid
  assign rating = rating | default: 0 | plus: 0
  assign rating_round = rating | round: 1
  assign rating_max = rating_max | default: 5
  assign rating_decimal = 0
  assign decimal = rating | modulo: 1
  if decimal >= 0.3 and decimal <= 0.7
    assign rating_decimal = 0.5
  elsif decimal > 0.7
    assign rating_decimal = 1
  endif
%}

<div class="flex items-center">
  <div
    class="rating"
    role="img"
    aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: rating_round, rating_max: rating_max }}"
  >
    <div
      aria-hidden="true"
      class="rating-star"
      style="
        --rating: {{ rating | floor }};
        --rating-max: {{ rating_max }};
        --rating-decimal: {{ rating_decimal }};
      "
    ></div>
  </div>

  {% if show_rating_text != false %}
    <div class="rating-text">
      <span aria-hidden="true">
        {% if show_rating_value and rating > 0 %}
          {{ rating | round: 1 }}
        {% endif %}
        ({{ rating_count | default: 0 }})
      </span>
    </div>
    <span class="visually-hidden">
      {{- rating_count | default: 0 }}
      {{ 'accessibility.total_reviews' | t -}}
    </span>
  {% endif %}
</div>
