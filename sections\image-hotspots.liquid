{% liquid
  assign full_width = section.settings.full_width
%}

{% if full_width %}
  {% assign image_sizes = '100vw' %}
{% else %}
  {%- capture image_sizes -%}
  min({{ section.settings.image_max_width }}px, {% render 'image-sizes-full-width', full_width: full_width %})
  {%- endcapture -%}
{% endif %}

<style>
  #shopify-section-{{ section.id }} {
    {% render 'apply-color-var',
      var: '--hotspot-background-color',
      color: section.settings.dot_background_color
    %}

    {% render 'apply-color-var',
      var: '--hotspot-icon-color',
      color: section.settings.dot_icon_color
    %}

    --image-max-width: {{ section.settings.image_max_width }}px;
  }

  #shopify-section-{{ section.id }} .hotspot-popover {
    {% render 'apply-color-var',
      var: '--color-background',
      color: section.settings.popover_background_color,
      default: 'var(--color-modal-background)'
    %}

    {% render 'apply-color-var',
      var: '--color-foreground',
      color: section.settings.popover_text_color,
      default: 'var(--color-modal-foreground)'
    %}

    --color-headings: var(--color-foreground);
  }
</style>

{% render 'section-bg-number-vars', full_width: true %}

<div {% render 'section-attrs', type: 'section--full-width' %}>
  {% render 'section-header' %}

  <div class="section-body">
    <div class="relative max-sm:bleed-margin {% if full_width %} sm:bleed-margin {% else %} max-w-[--image-max-width] mx-auto {% endif %}">
      <div
        class="media {% if full_width == false %} sm:rounded-block sm:shadow-block {% endif %}"
        {% if section.settings.full_width == false %}
          data-animation="block"
          data-animation-group="{{ section.id }}"
        {% endif %}
      >
        {% if section.settings.image %}
          {% if section.settings.image_mobile %}
            {% assign image_class = 'max-md:hidden' %}
            <lqip-element class="image-loader md:hidden">
              {{
                section.settings.image_mobile
                | image_url: width: 1536
                | image_tag:
                  widths: '420, 840, 1072, 1304, 1536',
                  loading: 'lazy',
                  sizes: image_sizes,
                  class: 'md:hidden'
              }}
            </lqip-element>
          {% endif %}

          <lqip-element class="image-loader {{ image_class }}">
            {{
              section.settings.image
              | image_url: width: 3840
              | image_tag:
                widths: '768, 1024, 1280, 1536, 1792, 2048, 2560, 3072, 3840',
                loading: 'lazy',
                sizes: image_sizes,
                class: image_class
            }}
          </lqip-element>
        {% else %}
          {% render 'placeholder', type: 'lifestyle-1', class: 'placeholder--dark' %}
        {% endif %}
      </div>

      {% for block in section.blocks %}
        <dropdown-element
          placement="top-center"
          max-width="384"
          interaction-handler="click"
          animation="scale"
        >
          <details>
            <summary
              aria-label="{{ 'accessibility.read_more' | t }}"
              class="image-hotspot"
              style="
                --hotspot-x: {{ block.settings.x }}%; --hotspot-y: {{ block.settings.y }}%;
                {% if section.settings.image_mobile %}
                  --hotspot-x-mobile: {{ block.settings.x_mobile }}%; --hotspot-y-mobile: {{ block.settings.y_mobile }}%;
                {% endif %}
              "
              {{ block.shopify_attributes }}
            >
              {% render 'icon-times' %}
            </summary>
            <div
              class="
                hotspot-popover color p-6 md:p-8 rounded-block absolute left-0 top-0
                w-max shadow-floating-modal trim-margins z-20
              "
            >
              {% if block.settings.heading != blank %}
                <p class="h6 mb-2 md:mb-3">
                  {{ block.settings.heading }}
                </p>
              {% endif %}

              {% if block.settings.content != blank %}
                <div class="prose max-md:text-sm">
                  {{ block.settings.content }}
                </div>
              {% endif %}
            </div>
          </details>
        </dropdown-element>
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.image-hotspots.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-hotspots.settings.image.label"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "t:sections.image-hotspots.settings.image_mobile.label"
    },
    {
      "type": "range",
      "id": "image_max_width",
      "min": 480,
      "max": 1920,
      "step": 20,
      "unit": "px",
      "label": "t:sections.image-hotspots.settings.image_max_width.label",
      "default": 1920,
      "info": "t:sections.image-hotspots.settings.image_max_width.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.heading_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.heading_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.heading_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.heading_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "label": "t:sections.image-hotspots.settings.dot_background_color.label",
      "id": "dot_background_color"
    },
    {
      "type": "color",
      "label": "t:sections.image-hotspots.settings.dot_icon_color.label",
      "id": "dot_icon_color"
    },
    {
      "type": "color",
      "label": "t:sections.image-hotspots.settings.popover_background_color.label",
      "id": "popover_background_color"
    },
    {
      "type": "color",
      "label": "t:sections.image-hotspots.settings.popover_text_color.label",
      "id": "popover_text_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "blocks": [
    {
      "type": "hotspot",
      "name": "t:sections.image-hotspots.blocks.hotspot.name",
      "settings": [
        {
          "type": "range",
          "id": "x",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.x.label",
          "default": 50
        },
        {
          "type": "range",
          "id": "y",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.y.label",
          "default": 50
        },
        {
          "type": "range",
          "id": "x_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.x_mobile.label",
          "default": 50
        },
        {
          "type": "range",
          "id": "y_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.y_mobile.label",
          "default": 50
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.heading.label",
          "default": "Product feature"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.image-hotspots.blocks.hotspot.settings.content.label",
          "default": "<p>Describe the product feature in more detail.</p>"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.image-hotspots.presets__0.name",
      "blocks": [
        {
          "type": "hotspot",
          "settings": {
            "x": 20,
            "y": 50
          }
        },
        {
          "type": "hotspot",
          "settings": {
            "x": 60,
            "y": 50
          }
        }
      ]
    }
  ]
}
{% endschema %}
