{"sections": {"main": {"type": "main-page", "disabled": true, "settings": {"full_width": true, "content_width": "normal", "content_text_size": "prose md:prose-lg", "heading_size": "h0", "heading_alignment": "text-center", "background_color": "", "text_color": "", "heading_color": ""}}, "custom_liquid_qg7GDY": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "<style>\n    .dd-wrapper * {\n        box-sizing: border-box;\n        margin: 0;\n        padding: 0;\n    }\n\n    .dd-wrapper {\n        line-height: 1.6;\n        color: #f0f0f0; /* Sv<PERSON>tlý text pro tmavé pozadí */\n        max-width: 1800px;\n        margin: 0 auto;\n        padding: 20px;\n        background-color: #1d1d1d; /* Tmavé pozadí celé sekce */\n    }\n\n    .dd-faq-header {\n        text-align: center;\n        margin-bottom: 40px;\n        position: relative;\n    }\n\n    .dd-faq-header h1 {\n        font-size: 2.5rem;\n        margin-bottom: 1rem;\n        color: #ffffff; /* Bílý nadpis pro lepší kontrast */\n    }\n\n    .dd-faq-container {\n        width: 100%;\n        margin: 0 auto;\n    }\n\n    .dd-faq-item {\n        margin-bottom: 1rem;\n        border: 1px solid #333333; /* Tmav<PERSON><PERSON>, ale viditelný okraj */\n        border-radius: 24px;\n        overflow: hidden;\n        background-color: #282828; /* Tmav<PERSON><PERSON> pozad<PERSON> pro jednotlivé FAQ polož<PERSON> */\n    }\n\n    .dd-faq-question {\n        background-color: #282828; /* <PERSON><PERSON><PERSON><PERSON> jako poz<PERSON> itemu */\n        padding: 15px 20px;\n        cursor: pointer;\n        font-weight: 600;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        transition: background-color 0.3s;\n        color: #f0f0f0; /* Světlý text otázky */\n    }\n    /* Zaoblení horních rohů question elementu */\n    .dd-faq-item:not(.active) .dd-faq-question {\n        border-radius: 24px; /* Zaoblení pro nezapnuté položky */\n    }\n    /* Zaoblení pouze horních rohů, pokud je položka aktivní (spodní část je otevřená) */\n    .dd-faq-item.active .dd-faq-question {\n        border-top-left-radius: 24px;\n        border-top-right-radius: 24px;\n    }\n\n    .dd-faq-question:hover {\n        background-color: #3a3a3a; /* Světlejší tmavé pozadí při najetí myší */\n    }\n\n    .dd-faq-question::after {\n        content: '+';\n        font-size: 1.5rem;\n        transition: transform 0.3s ease;\n        color: #f0f0f0; /* Barva znaménka */\n    }\n\n    .dd-faq-item.active .dd-faq-question::after {\n        content: '−';\n    }\n\n    .dd-faq-answer {\n        padding: 0;\n        max-height: 0;\n        overflow: hidden;\n        transition: max-height 0.5s ease, padding 0.5s ease;\n        background-color: #282828; /* Stejné pozadí jako otázka */\n        color: #d0d0d0; /* Mírně tmavší světlý text pro odpovědi */\n        /* Spodní zaoblení pro odpověď, pokud je aktivní */\n        border-bottom-left-radius: 24px;\n        border-bottom-right-radius: 24px;\n    }\n\n    .dd-faq-item.active .dd-faq-answer {\n        padding: 20px;\n        max-height: 1000px;\n        border-top: 1px solid #333333; /* Oddělovací linka */\n    }\n\n    .dd-faq-answer p, .dd-faq-answer ul, .dd-faq-answer ol, .dd-faq-answer h3 {\n        margin-bottom: 1rem;\n    }\n\n    .dd-faq-answer ul, .dd-faq-answer ol {\n        padding-left: 1.5rem;\n    }\n\n    .dd-faq-answer h3 {\n        margin-top: 1rem;\n        font-size: 1.2rem;\n        color: #f0f0f0; /* Světlá barva pro podnadpisy */\n    }\n\n    .dd-faq-answer a {\n        color: #8ab4f8; /* Světlejší modrá pro odkazy na tmavém pozadí */\n        text-decoration: none;\n    }\n\n    .dd-faq-answer a:hover {\n        text-decoration: underline;\n    }\n\n    .dd-contact-section {\n        text-align: center;\n        margin-top: 40px;\n    }\n\n    .dd-contact-button {\n        display: inline-block;\n        padding: 12px 24px;\n        background-color: #4a4a4a; /* Tmavě šedé pozadí tlačítka */\n        color: white; /* Bílý text tlačítka */\n        text-decoration: none;\n        border-radius: 24px;\n        font-weight: 600;\n        transition: background-color 0.3s;\n    }\n\n    .dd-contact-button:hover {\n        background-color: #6a6a6a; /* Světlejší šedá při najetí myší */\n    }\n\n    @media (max-width: 768px) {\n        .dd-faq-header h1 {\n            font-size: 2rem;\n        }\n\n        .dd-faq-question {\n            padding: 12px 15px;\n            font-size: 0.95rem;\n        }\n\n        .dd-faq-item.active .dd-faq-answer {\n            padding: 15px;\n        }\n    }\n</style>\n\n<div class=\"dd-wrapper\">\n    <div class=\"dd-faq-header\">\n        <h1>Frequently Asked Questions</h1>\n    </div>\n\n    <div class=\"dd-faq-container\">\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">How do I use the products I purchased?</div>\n            <div class=\"dd-faq-answer\">\n                <p>Each product page contains detailed, step-by-step instructions on how to install and use the specific item(s). If anything is still unclear, don't hesitate to <a href=\"/pages/contact\" target=\"_blank\" title=\"Support & Booking\">contact me</a>.</p>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">What is Develop Device, and how can it help beginners?</div>\n            <div class=\"dd-faq-answer\">\n                <p>Develop Device is a platform designed to support music producers at all levels. It offers:</p>\n                <ul>\n                    <li><strong>Simplified workflows:</strong> Breaking down complex audio techniques into manageable steps.</li>\n                    <li><strong>Accelerated learning:</strong> Tools to quickly master professional concepts.</li>\n                    <li><strong>A wide range of presets:</strong> For tools like Superior Drummer 3 and Fractal Audio Axe-FX III.</li>\n                    <li><strong>Cabinet IRs:</strong> Detailed speaker cabinet simulations to enhance your tone.</li>\n                    <li><strong>Scalable tools:</strong> Products that grow with you, offering advanced features as your skills develop.</li>\n                    <li><strong>DAW templates:</strong> Pre-made templates for digital audio workstations to streamline production.</li>\n                    <li><strong>Personalized support:</strong> I am here to guide you on your music production journey.</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">How can I book mixing and mastering services?</div>\n            <div class=\"dd-faq-answer\">\n                <p>To book my professional mixing and mastering services:</p>\n                <ul>\n                    <li>Visit my website.</li>\n                    <li>Look for the \"Services\" section.</li>\n                    <li>Follow the instructions to schedule your session.</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">What do I need to know about plugin requirements?</div>\n            <div class=\"dd-faq-answer\">\n                <p>Understanding plugin requirements is crucial for using my products:</p>\n                <ul>\n                    <li>Each product listing includes detailed information about required plugins.</li>\n                    <li>Review all product details carefully before purchasing.</li>\n                    <li>I don't offer refunds or exchanges if you buy a product without the necessary plugins.</li>\n                    <li>I primarily use 64-bit VST3 plugin versions.</li>\n                    <li>Keep your software up-to-date for the best experience.</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">What are the requirements for using presets for Superior Drummer 3?</div>\n            <div class=\"dd-faq-answer\">\n                <p>To fully utilize my presets for Superior Drummer 3, ensure that you:</p>\n                <ul>\n                    <li>Have a complete installation of the Superior Drummer 3 CORE library.</li>\n                    <li>Use the specified SDX/EZX expansion packs listed in each product description.</li>\n                    <li>Verify your system meets hardware requirements, particularly a minimum of 16 GB of RAM (recommended).</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">How can I reaccess my purchased files?</div>\n            <div class=\"dd-faq-answer\">\n                <p>If you've lost your purchased files:</p>\n                <ol>\n                    <li>Log into your user account on my website.</li>\n                    <li>Go to your order details.</li>\n                    <li>Redownload your files from there.</li>\n                    <li>You have up to five download attempts.</li>\n                    <li>If you encounter any issues, please contact me for assistance.</li>\n                </ol>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">What are the hardware requirements for using Develop Device products?</div>\n            <div class=\"dd-faq-answer\">\n                <p>For the best experience with my products:</p>\n                <ul>\n                    <li>Compatible with both Windows and macOS systems.</li>\n                    <li>Recommended minimum: 16GB of RAM.</li>\n                    <li>For Superior Drummer 3 users: Many presets require 8GB of RAM or more.</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">When will I receive my files after purchase?</div>\n            <div class=\"dd-faq-answer\">\n                <ul>\n                    <li>You'll receive download links immediately after payment.</li>\n                    <li>Exceptions may apply for certain products – check individual item descriptions for details.</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"dd-faq-item\">\n            <div class=\"dd-faq-question\">What is your refund policy?</div>\n            <div class=\"dd-faq-answer\">\n                <p>Due to the digital nature of my products:</p>\n                <ul>\n                    <li>I cannot offer returns, refunds, or exchanges once a purchase is completed.</li>\n                    <li>Please ensure you meet all requirements before buying.</li>\n                </ul>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"dd-contact-section\">\n        <a href=\"/pages/contact\" class=\"dd-contact-button\">Contact me</a>\n    </div>\n</div>\n\n<script>\n    document.addEventListener('DOMContentLoaded', function() {\n        const faqItems = document.querySelectorAll('.dd-faq-item');\n\n        faqItems.forEach(item => {\n            const question = item.querySelector('.dd-faq-question');\n\n            question.addEventListener('click', () => {\n                // Close all other items\n                faqItems.forEach(otherItem => {\n                    if (otherItem !== item && otherItem.classList.contains('active')) {\n                        otherItem.classList.remove('active');\n                    }\n                });\n\n                // Toggle current item\n                item.classList.toggle('active');\n            });\n        });\n    });\n</script>", "background_color": "rgba(0,0,0,0)", "text_color": "rgba(0,0,0,0)"}}}, "order": ["main", "custom_liquid_qg7GDY"]}