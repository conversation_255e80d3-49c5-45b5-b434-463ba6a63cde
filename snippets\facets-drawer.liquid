<facets-drawer-modal
  class="modal modal-drawer modal-drawer--left modal-drawer--mobile-bottom facets-drawer-modal"
  id="facets-drawer"
  position="bottom"
  md:position="left"
  data-preserve-state
>
  <button slot="activator" type="button" class="button button-light button-filter gap-3 md:gap-4">
    {{ 'products.facets.filters_drawer_button' | t }}
    <div class="icon-xs md:icon-sm">
      {% render 'icon-filters' %}
    </div>
  </button>

  <div slot="content" tabindex="-1" class="h-full">
    <div class="flex flex-col relative max-md:max-h-[90vh] h-full">
      <div class="flex items-center border-b mx-6 lg:mx-8 pt-6 lg:pt-8 pb-4 lg:pb-6">
        <h4 class="h5">{{ 'products.facets.filters_drawer_title' | t }}</h4>

        <button class="modal-close ml-auto relative -right-2" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
          {% render 'icon-times' %}
        </button>
      </div>

      <div class="grow overflow-y-auto custom-scrollbar px-6 lg:px-8">
        {% render 'facets', results: results, filter_type: 'vertical', scope: 'drawer', manual: true %}
      </div>

      <button class="button button-primary m-4 md:m-6" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
        {{ 'products.facets.apply' | t }}
      </button>
    </div>
  </div>
</facets-drawer-modal>
