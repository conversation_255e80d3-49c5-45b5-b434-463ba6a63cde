{% render 'section-bg-number-vars' %}

<div {% render 'section-attrs' %}>
  {% if shop.password_message != blank %}
    <div class="text-center mb-12 md:mb-16">
      <div class="max-w-xl inline-block leading-relaxed">
        {{ shop.password_message }}
      </div>
    </div>
  {% endif %}

  {% render 'section-header', button: false, class: 'text-center' %}

  <div class="section-body trim-margins text-center">
    {% if section.settings.show_email_signup_form %}
      <div class="inline-flex max-w-xl w-full">
        {% render 'newsletter-signup-form',
          button_text: section.settings.button_text,
          button_style: section.settings.button_style,
          button_background_color: section.settings.button_background_color,
          button_text_color: section.settings.button_text_color
        %}
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.password-page.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "header",
      "content": "t:sections.password-page.headers.email_signup"
    },
    {
      "type": "checkbox",
      "id": "show_email_signup_form",
      "label": "t:sections.password-page.settings.show_email_signup_form.label",
      "default": true
    },
    {
      "type": "text",
      "id": "button_text",
      "default": "Subscribe",
      "label": "t:sections.email-signup.settings.button_text.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:sections.all.button_style.label",
      "options": [
        {
          "value": "",
          "label": "t:sections.all.button_style.options.filled"
        },
        {
          "value": "button-outline",
          "label": "t:sections.all.button_style.options.outline"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    }
  ]
}
{% endschema %}
