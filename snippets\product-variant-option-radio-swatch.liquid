{% liquid
  if option_value.swatch.image
    assign swatch_image_url = option_value.swatch.image | image_url: width: 128
    assign swatch_color = 'url(' | append: swatch_image_url | append: ')'
  elsif option_value.swatch.color
    assign swatch_color = option_value.swatch.color
  else
    capture swatch_color
      render 'get-color-from-name', name: option_value
    endcapture
  endif
  assign swatch_brightness = swatch_color | color_brightness
%}

<div class="color-swatch-selector">
  <input
    type="radio"
    id="{{ section.id }}-{{ option.position }}-{{ index }}"
    name="{{ section.id }}-{{ option.name }}"
    value="{{ option_value | escape }}"
    form="{{ product_form_id }}"
    {% if option_value.selected %}
      checked
    {% endif %}
    class="peer visually-hidden {% unless option_value.available %} option-unavailable {% endunless %}"
    data-variant-id="{{ option_value.variant.id }}"
    data-product-url="{{ option_value.product_url }}"
    data-option-value-id="{{ option_value.id }}"
  >
  <label
    for="{{ section.id }}-{{ option.position }}-{{ index }}"
    class="
      block
      {% if swatch_color == blank or swatch_brightness > 240 and settings.color_swatches_enhance_visibility %}
        has-swatch-border
      {% endif %}
    "
    title="{{ option_value | escape }}"
    {% if swatch_color != blank %}
      style="--swatch-color: {{ swatch_color }};"
    {% endif %}
  >
    <div class="inner"></div>
  </label>
</div>
