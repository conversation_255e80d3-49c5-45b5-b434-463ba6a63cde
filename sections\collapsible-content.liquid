{% liquid
  assign background_rgba = section.settings.background_color.rgba | default: '0 0 0 / 0.0'
  assign accordion_background_rgba = section.settings.accordion_background_color.rgba | default: '0 0 0 / 0.0'
%}

<style>
  #shopify-section-{{ section.id }} .faq-block {
    {% if section.settings.accordion_background_color != blank and section.settings.accordion_background_color.rgba != '0 0 0 / 0.0' %}
      --color-background: {{ section.settings.accordion_background_color.rgb }};
    {% else %}
      --color-background: var(--color-base-background);
    {% endif %}

    {% if section.settings.accordion_text_color != blank and section.settings.accordion_text_color.rgba != '0 0 0 / 0.0' %}
      --color-foreground: {{ section.settings.accordion_text_color.rgb }};
    {% else %}
      --color-foreground: var(--color-base-foreground);
    {% endif %}

    {% if section.settings.accordion_title_color != blank and section.settings.accordion_title_color.rgba != '0 0 0 / 0.0' %}
      --collapsible-content-title-color: {{ section.settings.accordion_title_color.rgb }};
    {% else %}
      --collapsible-content-title-color: var(--color-base-foreground);
    {% endif %}
  }
</style>

{% render 'section-bg-number-vars' %}

<div {% render 'section-attrs' %}>
  <div
    data-animation-auto-order
    class="
      section-body grid gap-y-8 md:gap-y-12 gap-x-16
      {% if section.settings.text_position == 'left' or section.settings.text_position == 'right' %}
        xl:grid-cols-2
      {% elsif section.settings.text_position == 'center' %}
        max-w-3xl mx-auto text-center prose-align
      {% endif %}
    "
  >
    <div class="section-content-spacing {% if section.settings.text_position == 'right' %} xl:col-start-2 xl:row-start-1 {% endif %}">
      {% render 'section-content' %}
    </div>

    <div
      data-animation="block"
      data-animation-group="{{ section.id }}"
      class="
        faq-block flex flex-col gap-6 sm:gap-8 color rounded-block text-left
        {% if background_rgba != accordion_background_rgba %}
          rfs:p-12
        {% endif %}
      "
    >
      {% for block in section.blocks %}
        <smooth-collapse class="group" {{ block.shopify_attributes }}>
          <details
            {% if block.settings.open %}
              open
            {% endif %}
          >
            <summary
              class="
                flex justify-between
                font-bold text-base md:text-lg
                py-4 -my-4 text-[rgb(var(--collapsible-content-title-color))]
              "
            >
              {% capture heading_tag %}{% render 'get-heading-tag', target: block %}{% endcapture %}
              <{{ heading_tag }}>{{ block.settings.title }}</{{ heading_tag }}>

              <div class="collapse-chevron w-4 ml-6 md:ml-8 mt-2">
                {% render 'icon-chevron' %}
              </div>
            </summary>
            <div>
              <div class="prose pt-4  max-w-none max-md:text-sm">
                {% if block.settings.content_from_page != blank %}
                  {{ block.settings.content_from_page.content }}
                {% else %}
                  {{ block.settings.content }}
                {% endif %}
              </div>
            </div>
          </details>
        </smooth-collapse>

        {% if forloop.last == false %}
          <hr>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.collapsible-content.name",
  "blocks": [
    {
      "type": "block",
      "name": "t:sections.collapsible-content.blocks.block.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.collapsible-content.blocks.block.settings.title.label",
          "default": "Collapsible row"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.collapsible-content.blocks.block.settings.content.label",
          "default": "<p>Provide answers to common questions your audience might have. It can help reduce customer support inquiries and improve their overall experience.</p>"
        },
        {
          "type": "page",
          "id": "content_from_page",
          "label": "t:sections.collapsible-content.blocks.block.settings.content_from_page.label"
        },
        {
          "type": "checkbox",
          "id": "open",
          "label": "t:sections.collapsible-content.blocks.block.settings.open.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.seo"
        },
        {
          "type": "select",
          "id": "heading_html_tag",
          "label": "t:sections.all.heading_html_tag.label",
          "options": [
            {
              "value": "h1",
              "label": "h1"
            },
            {
              "value": "h2",
              "label": "h2"
            },
            {
              "value": "h3",
              "label": "h3"
            },
            {
              "value": "h4",
              "label": "h4"
            },
            {
              "value": "h5",
              "label": "h5"
            },
            {
              "value": "h6",
              "label": "h6"
            },
            {
              "value": "p",
              "label": "p"
            },
            {
              "value": "span",
              "label": "span"
            },
            {
              "value": "div",
              "label": "div"
            }
          ],
          "default": "p",
          "info": "t:sections.all.heading_html_tag.info"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label",
      "default": "Collapsible content"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label",
      "default": "<p>Provide answers to common questions your audience might have. It can help reduce customer support inquiries and improve their overall experience.</p>"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "t:sections.collapsible-content.settings.text_position.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collapsible-content.settings.text_position.options__0.label"
        },
        {
          "value": "center",
          "label": "t:sections.collapsible-content.settings.text_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.collapsible-content.settings.text_position.options__2.label"
        }
      ],
      "default": "left"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "label": "t:sections.collapsible-content.settings.accordion_background_color.label",
      "id": "accordion_background_color"
    },
    {
      "type": "color",
      "label": "t:sections.collapsible-content.settings.accordion_title_color.label",
      "id": "accordion_title_color"
    },
    {
      "type": "color",
      "label": "t:sections.collapsible-content.settings.accordion_text_color.label",
      "id": "accordion_text_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.collapsible-content.presets__0.name",
      "blocks": [
        {
          "type": "block"
        },
        {
          "type": "block"
        },
        {
          "type": "block"
        }
      ]
    }
  ]
}
{% endschema %}
