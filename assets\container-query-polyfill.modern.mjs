function b(){return b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},b.apply(this,arguments)}function Jt(e,t){const n=t.width,r=t.height,u=t.inlineSize,o=t.blockSize;switch(e){case 1:return n!=null?{type:3,value:n,unit:"px"}:{type:1};case 3:return u!=null?{type:3,value:u,unit:"px"}:{type:1};case 2:return r!=null?{type:3,value:r,unit:"px"}:{type:1};case 4:return o!=null?{type:3,value:o,unit:"px"}:{type:1};case 5:return n!=null&&r!=null&&r>0?{type:2,value:n/r}:{type:1};case 6:return n!=null&&r!=null?{type:4,value:r>=n?"portrait":"landscape"}:{type:1}}}function Qe(e,t){switch(e.type){case 1:case 2:case 3:case 4:return _(e,t);case 5:{const n=t.sizeFeatures.get(e.feature);return n??{type:1}}case 6:return e.value}}function At(e){return{type:5,value:e}}function it(e,t,n){return At(function(r,u,o){switch(o){case 1:return r===u;case 2:return r>u;case 3:return r>=u;case 4:return r<u;case 5:return r<=u}}(e,t,n))}function De(e,t,n){return e==null?t:t==null?e:n(e,t)}function se(e,t){switch(e){case"cqw":return t.cqw;case"cqh":return t.cqh;case"cqi":return t.writingAxis===0?t.cqw:t.cqh;case"cqb":return t.writingAxis===1?t.cqw:t.cqh;case"cqmin":return De(se("cqi",t),se("cqb",t),Math.min);case"cqmax":return De(se("cqi",t),se("cqb",t),Math.max)}}function Kt(e,{treeContext:t}){switch(e.unit){case"px":return e.value;case"rem":return e.value*t.rootFontSize;case"em":return e.value*t.fontSize;case"cqw":case"cqh":case"cqi":case"cqb":case"cqmin":case"cqmax":return De(e.value,se(e.unit,t),(n,r)=>n*r)}return null}function at(e,t){switch(e.type){case 2:return e.value===0?0:null;case 3:return Kt(e,t)}return null}function _(e,t){switch(e.type){case 4:return function(n,r){const u=Qe(n.left,r),o=Qe(n.right,r),a=n.operator;if(u.type===4&&o.type===4||u.type===5&&o.type===5)return function(i,p,h){return h===1?At(i.value===p.value):{type:1}}(u,o,a);if(u.type===3||o.type===3){const i=at(u,r),p=at(o,r);if(i!=null&&p!=null)return it(i,p,a)}else if(u.type===2&&o.type===2)return it(u.value,o.value,a);return{type:1}}(e,t);case 2:return function(n,r){const u=_(n.left,r);return u.type!==5||u.value!==!0?u:_(n.right,r)}(e,t);case 3:return function(n,r){const u=_(n.left,r);return u.type===5&&u.value===!0?u:_(n.right,r)}(e,t);case 1:{const n=_(e.value,t);return n.type===5?{type:5,value:!n.value}:{type:1}}case 5:return ft(Qe(e,t));case 6:return ft(e.value)}}function ft(e){switch(e.type){case 5:return e;case 2:case 3:return{type:5,value:e.value>0}}return{type:1}}const ye=Array.from({length:4},()=>Math.floor(256*Math.random()).toString(16)).join(""),Et=G("container"),fe=G("container-type"),pe=G("container-name"),ge=`data-cqs-${ye}`,we=`data-cqc-${ye}`,le=G("cqw"),ce=G("cqh"),ke=G("cqi"),Ae=G("cqb");function G(e){return`--cq-${e}-${ye}`}const l=Symbol();function zt(e,t){const n={value:t,errorIndices:[],index:-1,at(r){const u=n.index+r;return u>=e.length?t:e[u]},consume:r=>(n.index+=r,n.value=n.at(0),n.value),reconsume(){n.index-=1},error(){n.errorIndices.push(n.index)}};return n}function L(e){return zt(e,{type:0})}function*Ge(e){const t=[];let n=!1;for(const s of e){const f=s.codePointAt(0);n&&f!==10&&(n=!1,t.push(10)),f===0||f>=55296&&f<=57343?t.push(65533):f===13?n=!0:t.push(f)}const r=zt(t,-1),{at:u,consume:o,error:a,reconsume:i}=r;function p(){return String.fromCodePoint(r.value)}function h(){return{type:13,value:p()}}function E(){for(;W(u(1));)o(1)}function $(){for(;r.value!==-1;)if(o(1),u(0)===42&&u(1)===47)return void o(1);a()}function j(){const[s,f]=function(){let S=0,w="",q=u(1);for(q!==43&&q!==45||(o(1),w+=p());T(u(1));)o(1),w+=p();if(u(1)===46&&T(u(2)))for(S=1,o(1),w+=p();T(u(1));)o(1),w+=p();if(q=u(1),q===69||q===101){const x=u(2);if(T(x))for(S=1,o(1),w+=p();T(u(1));)o(1),w+=p();else if((x===45||x===43)&&T(u(3)))for(S=1,o(1),w+=p(),o(1),w+=p();T(u(1));)o(1),w+=p()}return[w,S]}(),d=u(1);return m(d,u(1),u(2))?{type:15,value:s,flag:f,unit:y()}:d===37?(o(1),{type:16,value:s}):{type:17,value:s,flag:f}}function C(){const s=y();let f=u(1);if(s.toLowerCase()==="url"&&f===40){for(o(1);W(u(1))&&W(u(2));)o(1);f=u(1);const d=u(2);return f===34||f===39?{type:23,value:s}:!W(f)||d!==34&&d!==39?function(){let S="";for(E();;){const q=o(1);if(q===41)return{type:20,value:S};if(q===-1)return a(),{type:20,value:S};if(W(q)){E();const x=u(1);return x===41||x===-1?(o(1),q===-1&&a(),{type:20,value:S}):(g(),{type:21})}if(q===34||q===39||q===40||(w=q)>=0&&w<=8||w===11||w>=14&&w<=31||w===127)return a(),g(),{type:21};if(q===92){if(!Z(q,u(1)))return a(),{type:21};S+=F()}else S+=p()}var w}():{type:23,value:s}}return f===40?(o(1),{type:23,value:s}):{type:24,value:s}}function R(s){let f="";for(;;){const d=o(1);if(d===-1||d===s)return d===-1&&a(),{type:2,value:f};if(Ee(d))return a(),i(),{type:3};if(d===92){const S=u(1);if(S===-1)continue;Ee(S)?o(1):f+=F()}else f+=p()}}function F(){const s=o(1);if(pt(s)){const f=[s];for(let S=0;S<5;S++){const w=u(1);if(!pt(w))break;f.push(w),o(1)}W(u(1))&&o(1);let d=parseInt(String.fromCodePoint(...f),16);return(d===0||d>=55296&&d<=57343||d>1114111)&&(d=65533),String.fromCodePoint(d)}return s===-1?(a(),String.fromCodePoint(65533)):p()}function m(s,f,d){return s===45?be(f)||f===45||Z(f,d):!!be(s)}function c(s,f,d){return s===43||s===45?T(f)||f===46&&T(d):!(s!==46||!T(f))||!!T(s)}function y(){let s="";for(;;){const f=o(1);if(yt(f))s+=p();else{if(!Z(f,u(1)))return i(),s;s+=F()}}}function g(){for(;;){const s=o(1);if(s===-1)return;Z(s,u(1))&&F()}}for(;;){const s=o(1);if(s===47&&u(1)===42)o(2),$();else if(W(s))E(),yield{type:1};else if(s===34)yield R(s);else if(s===35){const f=u(1);yt(f)||Z(f,u(2))?yield{type:14,flag:m(u(1),u(2),u(3))?1:0,value:y()}:yield h()}else if(s===39)yield R(s);else if(s===40)yield{type:4};else if(s===41)yield{type:5};else if(s===43)c(s,u(1),u(2))?(i(),yield j()):yield h();else if(s===44)yield{type:6};else if(s===45){const f=u(1),d=u(2);c(s,f,d)?(i(),yield j()):f===45&&d===62?(o(2),yield{type:19}):m(s,f,d)?(i(),yield C()):yield h()}else if(s===46)c(s,u(1),u(2))?(i(),yield j()):yield h();else if(s===58)yield{type:7};else if(s===59)yield{type:8};else if(s===60)u(1)===33&&u(2)===45&&u(3)===45?yield{type:18}:yield h();else if(s===64)m(u(1),u(2),u(3))?yield{type:22,value:y()}:yield h();else if(s===91)yield{type:9};else if(s===92)Z(s,u(1))?(i(),yield C()):(a(),yield h());else if(s===93)yield{type:10};else if(s===123)yield{type:11};else if(s===125)yield{type:12};else if(T(s))i(),yield j();else if(be(s))i(),yield C();else{if(s===-1)return yield{type:0},r.errorIndices;yield{type:13,value:p()}}}}function T(e){return e>=48&&e<=57}function pt(e){return T(e)||e>=65&&e<=70||e>=97&&e<=102}function Ee(e){return e===10||e===13||e===12}function W(e){return Ee(e)||e===9||e===32}function be(e){return e>=65&&e<=90||e>=97&&e<=122||e>=128||e===95}function Z(e,t){return e===92&&!Ee(t)}function yt(e){return be(e)||T(e)||e===45}const Xt={11:12,9:10,4:5};function ae(e,t){const n=function(r,u){const o=[];for(;;)switch(r.consume(1).type){case 1:break;case 0:return{type:3,value:o};case 18:case 19:if(u!==!1){r.reconsume();const a=We(r);a!==l&&o.push(a)}break;case 22:r.reconsume(),o.push(jt(r));break;default:{r.reconsume();const a=We(r);a!==l&&o.push(a);break}}}(L(e),t===!0);return b({},n,{value:n.value.map(r=>r.type===26?function(u,o){return u.value.value.type===0?b({},u,{value:b({},u.value,{value:Zt(u.value.value.value)})}):u}(r):r)})}function Lt(e){const t=L(e),n=[];for(;;){if(t.consume(1).type===0)return n;t.reconsume(),n.push(V(t))}}function Zt(e){return function(t){const n=[],r=[];for(;;){const u=t.consume(1);switch(u.type){case 1:case 8:break;case 0:return{type:1,value:[...r,...n]};case 22:t.reconsume(),n.push(jt(t));break;case 24:{const o=[u];let a=t.at(1);for(;a.type!==8&&a.type!==0;)o.push(V(t)),a=t.at(1);const i=Mt(L(o));i!==l&&r.push(i);break}case 13:if(u.value==="&"){t.reconsume();const o=We(t);o!==l&&n.push(o);break}default:{t.error(),t.reconsume();let o=t.at(1);for(;o.type!==8&&o.type!==0;)V(t),o=t.at(1);break}}}}(L(e))}function z(e){for(;e.at(1).type===1;)e.consume(1)}function jt(e){let t=e.consume(1);if(t.type!==22)throw new Error(`Unexpected type ${t.type}`);const n=t.value,r=[];for(;;)switch(t=e.consume(1),t.type){case 8:return{type:25,name:n,prelude:r,value:null};case 0:return e.error(),{type:25,name:n,prelude:r,value:null};case 11:return{type:25,name:n,prelude:r,value:Ye(e)};case 28:if(t.source.type===11)return{type:25,name:n,prelude:r,value:t};default:e.reconsume(),r.push(V(e))}}function We(e){let t=e.value;const n=[];for(;;)switch(t=e.consume(1),t.type){case 0:return e.error(),l;case 11:return{type:26,prelude:n,value:Ye(e)};case 28:if(t.source.type===11)return{type:26,prelude:n,value:t};default:e.reconsume(),n.push(V(e))}}function Mt(e){const t=e.consume(1);if(t.type!==24)throw new Error(`Unexpected type ${t.type}`);const n=t.value,r=[];let u=!1;if(z(e),e.at(1).type!==7)return e.error(),l;for(e.consume(1),z(e);e.at(1).type!==0;)r.push(V(e));const o=r[r.length-2],a=r[r.length-1];return o&&o.type===13&&o.value==="!"&&a.type===24&&a.value.toLowerCase()==="important"&&(u=!0,r.splice(r.length-2)),{type:29,name:n,value:r,important:u}}function V(e){const t=e.consume(1);switch(t.type){case 11:case 9:case 4:return Ye(e);case 23:return function(n){let r=n.value;if(r.type!==23)throw new Error(`Unexpected type ${r.type}`);const u=r.value,o=[];for(;;)switch(r=n.consume(1),r.type){case 5:return{type:27,name:u,value:o};case 0:return n.error(),{type:27,name:u,value:o};default:n.reconsume(),o.push(V(n))}}(e);default:return t}}function Ye(e){let t=e.value;const n=t,r=Xt[n.type];if(!r)throw new Error(`Unexpected type ${t.type}`);const u=[];for(;;)switch(t=e.consume(1),t.type){case r:return{type:28,source:n,value:{type:0,value:u}};case 0:return e.error(),{type:28,source:n,value:{type:0,value:u}};default:e.reconsume(),u.push(V(e))}}function Y(e){return z(e),e.at(1).type===0}const en={11:["{","}"],9:["[","]"],4:["(",")"]};function H(e,t){switch(e.type){case 25:return`@${CSS.escape(e.name)} ${e.prelude.map(n=>H(n)).join("")}${e.value?H(e.value):";"}`;case 26:return`${e.prelude.map(n=>H(n)).join("")}${H(e.value)}`;case 28:{const[n,r]=en[e.source.type];return`${n}${Tt(e.value)}${r}`}case 27:return`${CSS.escape(e.name)}(${e.value.map(n=>H(n)).join("")})`;case 29:return`${CSS.escape(e.name)}:${e.value.map(n=>H(n)).join("")}${e.important?" !important":""}`;case 1:return" ";case 8:return";";case 7:return":";case 14:return"#"+CSS.escape(e.value);case 24:return CSS.escape(e.value);case 15:return e.value+CSS.escape(e.unit);case 13:case 17:return e.value;case 2:return`"${CSS.escape(e.value)}"`;case 6:return",";case 20:return"url("+CSS.escape(e.value)+")";case 22:return"@"+CSS.escape(e.value);case 16:return e.value+"%";default:throw new Error(`Unsupported token ${e.type}`)}}function Tt(e,t){return e.value.map(n=>{let r=H(n);return n.type===29&&e.type!==0&&(r+=";"),r}).join("")}function ht(e){return H(e)}function vt(e){const t=e.at(1);return t.type===13&&t.value==="="&&(e.consume(1),!0)}function de(e,t){const n=[];for(;;){const r=e.at(1);if(r.type===0||t&&r.type===7||r.type===13&&(r.value===">"||r.value==="<"||r.value==="="))break;n.push(e.consume(1))}return n}function dt(e){z(e);const t=e.consume(1);return t.type!==13?l:t.value===">"?vt(e)?3:2:t.value==="<"?vt(e)?5:4:t.value==="="?1:l}function mt(e){return e===4||e===5}function gt(e){return e===2||e===3}function oe(e,t,n){const r=function(o){z(o);const a=o.consume(1);return z(o),a.type!==24||o.at(1).type!==0?l:a.value}(L(e));if(r===l)return l;let u=r.toLowerCase();return u=n?n(u):u,t.has(u)?u:l}function ie(e){return{type:13,value:e}}function wt(e,t){return{type:29,name:e,value:t,important:!1}}function B(e){return{type:24,value:e}}function Te(e,t){return{type:27,name:e,value:t}}function He(e){return Te("var",[B(e)])}function _e(e,t){z(e);let n=!1,r=e.at(1);if(r.type===24){if(r.value.toLowerCase()!=="not")return l;e.consume(1),z(e),n=!0}let u=function(a){const i=a.consume(1);switch(i.type){case 28:{if(i.source.type!==4)return l;const p=_e(L(i.value.value),null);return p!==l?p:{type:4,value:i}}case 27:return{type:4,value:i};default:return l}}(e);if(u===l)return l;u=n?{type:1,value:u}:u,z(e),r=e.at(1);const o=r.type===24?r.value.toLowerCase():null;if(o!==null){if(e.consume(1),z(e),o!=="and"&&o!=="or"||t!==null&&o!==t)return l;const a=_e(e,o);return a===l?l:{type:o==="and"?2:3,left:u,right:a}}return Y(e)?u:l}function Pt(e){return _e(e,null)}function Se(e){switch(e.type){case 1:return[B("not"),{type:1},...Se(e.value)];case 2:case 3:return[...Se(e.left),{type:1},B(e.type===2?"and":"or"),{type:1},...Se(e.right)];case 4:return[e.value]}}const Nt={width:1,height:2,"inline-size":3,"block-size":4,"aspect-ratio":5,orientation:6},tn=new Set(Object.keys(Nt)),nn=new Set(["none","and","not","or","normal","auto"]),rn=new Set(["initial","inherit","revert","revert-layer","unset"]),un=new Set(["size","inline-size"]);function Ot(e,t,n,r){const u=n();if(u===l)return l;let o=[u,null];z(e);const a=e.at(1);if(a.type===13){if(a.value!==t)return l;e.consume(1),z(e);const i=r();z(e),i!==l&&(o=[u,i])}return Y(e)?o:l}function bt(e){const t=e.consume(1);return t.type===17?parseInt(t.value):l}function St(e){const t=L(e);z(t);const n=t.consume(1);let r=l;switch(n.type){case 17:t.reconsume(),r=function(u){const o=Ot(u,"/",()=>bt(u),()=>bt(u));return o===l?l:{type:2,value:o[0]/(o[1]!==null?o[1]:1)}}(t);break;case 15:r={type:3,value:parseInt(n.value),unit:n.unit.toLowerCase()};break;case 24:{const u=n.value.toLowerCase();switch(u){case"landscape":case"portrait":r={type:4,value:u}}}}return r===l?l:Y(t)?{type:6,value:r}:l}function on(e){return!Pe(e=e.toLowerCase())&&!nn.has(e)}function ze(e,t){const n=[];for(;;){z(e);const r=e.at(1);if(r.type!==24||!t(r.value))return n;e.consume(1),n.push(r.value)}}function Ft(e){const t=[];for(;;){z(e);const n=e.at(1);if(n.type!==24)break;const r=n.value;if(!on(r))break;e.consume(1),t.push(r)}return t}function Pe(e){return rn.has(e)}function Je(e){return e.map(t=>"cq-"+t)}function Ke(e){const t=ze(e,n=>Pe(n));return t.length===1?Je(t):l}function Xe(e,t){const n=ze(e,u=>u==="none");if(n.length===1)return Je(n);if(n.length!==0)return l;if(t){const u=Ke(e);if(u!==l)return u}const r=Ft(e);return r.length>0&&(!t||Y(e))?r:l}function Ze(e,t){if(t){const n=Ke(e);if(n!==l)return n}return function(n){const r=ze(n,o=>o==="normal");if(r.length===1)return Je(r);if(r.length!==0)return l;const u=ze(n,o=>un.has(o));return u.length>0&&Y(n)?u:l}(e)}function Ut(e){const t=L(e),n=Ke(t);if(n!==l)return[n,n];const r=Ot(t,"/",()=>Xe(t,!1),()=>Ze(t,!1));return r!==l&&Y(t)?[r[0],r[1]||[]]:l}function sn(e){const t=L(e),n=Ft(t);if(!n||n.length>1)return l;const r=Pt(t);if(r===l)return l;const u={features:new Set},o=xe(r,u);return Y(t)?{name:n.length>0?n[0]:null,condition:o,features:u.features}:l}function xe(e,t){switch(e.type){case 1:return{type:1,value:xe(e.value,t)};case 2:case 3:return{type:e.type===2?2:3,left:xe(e.left,t),right:xe(e.right,t)};case 4:if(e.value.type===28){const n=function(r,u){const o=function(i,p){const h=de(i,!0),E=i.at(1);if(E.type===0){const m=oe(h,p);return m!==l&&p.has(m)?{type:1,feature:m}:l}if(E.type===7){i.consume(1);const m=de(i,!1);let c=1;const y=oe(h,p,g=>g.startsWith("min-")?(c=3,g.substring(4)):g.startsWith("max-")?(c=5,g.substring(4)):g);return y!==l?{type:2,feature:y,bounds:[null,[c,m]]}:l}const $=dt(i);if($===l)return l;const j=de(i,!1);if(i.at(1).type===0){const m=oe(h,p);if(m!==l)return{type:2,feature:m,bounds:[null,[$,j]]};const c=oe(j,p);return c!==l?{type:2,feature:c,bounds:[[$,h],null]}:l}const C=dt(i);if(C===l||!(gt($)&&gt(C)||mt($)&&mt(C)))return l;const R=de(i,!1),F=oe(j,p);return F!==l?{type:2,feature:F,bounds:[[$,h],[C,R]]}:l}(r,tn);if(o===l)return l;const a=Nt[o.feature];if(a==null)return l;if(u.features.add(a),o.type===1)return{type:5,feature:a};{const i={type:5,feature:a};let p=l;if(o.bounds[0]!==null){const h=St(o.bounds[0][1]);if(h===l)return l;p={type:4,operator:o.bounds[0][0],left:h,right:i}}if(o.bounds[1]!==null){const h=St(o.bounds[1][1]);if(h===l)return l;const E={type:4,operator:o.bounds[1][0],left:i,right:h};p=p!==l?{type:2,left:p,right:E}:E}return p}}(L(e.value.value.value),t);if(n!==l)return n}return{type:6,value:{type:1}}}}let ln=0;const cn={cqw:le,cqh:ce,cqi:ke,cqb:Ae},an=CSS.supports("selector(:where(div))"),qe=":not(.container-query-polyfill)";Lt(Array.from(Ge(qe)));const fn=document.createElement("div"),pn=new Set(["before","after","first-line","first-letter"]);function xt(e,t){return Te("calc",[{type:17,flag:e.flag,value:e.value},ie("*"),t])}function Rt(e){return e.map(t=>{switch(t.type){case 15:return function(n){const r=n.unit,u=cn[r];return u!=null?xt(n,He(u)):r==="cqmin"||r==="cqmax"?xt(n,Te(n.unit.slice(2),[He(ke),{type:6},He(Ae)])):n}(t);case 27:return b({},t,{value:Rt(t.value)})}return t})}function It(e){switch(e.name){case"container":return Ut(e.value)?b({},e,{name:Et}):e;case"container-name":return Xe(L(e.value),!0)?b({},e,{name:pe}):e;case"container-type":return Ze(L(e.value),!0)!=null?b({},e,{name:fe}):e}return b({},e,{value:Rt(e.value)})}function Le(e,t){return b({},e,{value:e.value.map(n=>{switch(n.type){case 25:return et(n,t);case 26:return function(r,u){return u.transformStyleRule(b({},r,{value:Qt(r.value,u)}))}(n,t);default:return n}})})}function qt(e){return e.type===0||e.type===6}function yn(e){for(let t=e.length-1;t>=0;t--)if(e[t].type!==1)return e.slice(0,t+1);return e}function Qt(e,t){return function(n,r){const u=[];let o=null,a=null;for(const i of n.value.value)switch(i.type){case 25:{const p=r?r(i):i;p&&u.push(p)}break;case 29:{const p=It(i);switch(p.name){case Et:{const h=Ut(i.value);h!==l&&(o=h[0],a=h[1]);break}case pe:{const h=Xe(L(i.value),!0);h!==l&&(o=h);break}case fe:{const h=Ze(L(i.value),!0);h!==l&&(a=h);break}default:u.push(p)}}}return o&&o.length>0&&u.push(wt(pe,[B(o.join(" "))])),a&&a.length>0&&u.push(wt(fe,[B(a.join(" "))])),b({},n,{value:{type:2,value:u}})}(e,n=>et(n,t))}function Ce(e){if(e.type===1)return b({},e,{value:Ce(e.value)});if(e.type===2||e.type===3)return b({},e,{left:Ce(e.left),right:Ce(e.right)});if(e.type===4&&e.value.type===28){const t=function(n){const r=L(n);return z(r),r.at(1).type!==24?l:Mt(r)||l}(e.value.value.value);if(t!==l)return b({},e,{value:b({},e.value,{value:{type:0,value:[It(t)]}})})}return e}function hn(e,t){let n=Pt(L(e.prelude));return n=n!==l?Ce(n):l,b({},e,{prelude:n!==l?Se(n):e.prelude,value:e.value?b({},e.value,{value:Le(ae(e.value.value.value),t)}):null})}function et(e,t){switch(e.name.toLocaleLowerCase()){case"media":case"layer":return function(n,r){return b({},n,{value:n.value?b({},n.value,{value:Le(ae(n.value.value.value),r)}):null})}(e,t);case"keyframes":return function(n,r){let u=null;return n.value&&(u=b({},n.value,{value:{type:3,value:ae(n.value.value.value).value.map(o=>{switch(o.type){case 26:return function(a,i){return b({},a,{value:Qt(a.value,i)})}(o,r);case 25:return et(o,r)}})}})),b({},n,{value:u})}(e,t);case"supports":return hn(e,t);case"container":return function(n,r){if(n.value){const u=sn(n.prelude);if(u!==l){const o={rule:u,selector:null,parent:r.parent,uid:"c"+ln++},a=new Set,i=[],p=Le(ae(n.value.value.value),{descriptors:r.descriptors,parent:o,transformStyleRule:h=>{const[E,$]=function(C,R,F){const m=L(C),c=[],y=[];for(;;){if(m.at(1).type===0)return[c,y];const f=Math.max(0,m.index);for(;g=m.at(1),s=m.at(2),!(qt(g)||g.type===7&&(s.type===7||s.type===24&&pn.has(s.value.toLowerCase())));)m.consume(1);const d=m.index+1,S=C.slice(f,d),w=S.length>0?yn(S):[ie("*")];for(;!qt(m.at(1));)m.consume(1);const q=C.slice(d,Math.max(0,m.index+1));let x=w,P=[{type:28,source:{type:9},value:{type:0,value:[B(q.length>0?ge:we),ie("~"),ie("="),{type:2,value:R}]}}];if(an)P=[ie(":"),Te("where",P)];else{const v=w.map(ht).join("");v.endsWith(qe)?x=Lt(Array.from(Ge(v.substring(0,v.length-qe.length)))):i.push({actual:v,expected:v+qe})}c.push(...w),y.push(...x),y.push(...P),y.push(...q),m.consume(1)}var g,s}(h.prelude,o.uid);if(i.length>0)return h;const j=E.map(ht).join("");try{fn.matches(j),a.add(j)}catch{}return b({},h,{prelude:$})}}).value;if(i.length>0){const h=new Set,E=[];let $=0;for(const{actual:C}of i)$=Math.max($,C.length);const j=Array.from({length:$},()=>" ").join("");for(const{actual:C,expected:R}of i)h.has(C)||(E.push(`${C}${j.substring(0,$-C.length)} => ${R}`),h.add(C));console.warn(`The :where() pseudo-class is not supported by this browser. To use the Container Query Polyfill, you must modify the selectors under your @container rules:

${E.join(`
`)}`)}return a.size>0&&(o.selector=Array.from(a).join(", ")),r.descriptors.push(o),{type:25,name:"media",prelude:[B("all")],value:b({},n.value,{value:{type:3,value:p}})}}}return n}(e,t)}return e}class je{constructor(t){this.value=void 0,this.value=t}}function Me(e,t){if(e===t)return!0;if(typeof e==typeof t&&e!==null&&t!==null&&typeof e=="object"){if(Array.isArray(e)){if(!Array.isArray(t)||t.length!==e.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!Me(e[n],t[n]))return!1;return!0}if(e instanceof je)return t instanceof je&&e.value===t.value;{const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0,u=n.length;r<u;r++){const o=n[r];if(!Me(e[o],t[o]))return!1}return!0}}return!1}const Ve=Symbol("CQ_INSTANCE"),Ht=Symbol("CQ_STYLESHEET"),vn=CSS.supports("width: 1svh"),dn=new Set(["vertical-lr","vertical-rl","sideways-rl","sideways-lr","tb","tb-lr","tb-rl"]),mn=["padding-left","padding-right","border-left-width","border-right-width"],gn=["padding-top","padding-bottom","border-top-width","border-bottom-width"],wn=/(\w*(\s|-))?(table|ruby)(-\w*)?/;class ee{constructor(t){this.node=void 0,this.node=t}connected(){}disconnected(){}updated(){}}class bn extends ee{constructor(t,n){super(t),this.context=void 0,this.controller=null,this.styleSheet=null,this.context=n}connected(){var t=this;const n=this.node;if(n.rel==="stylesheet"){const r=new URL(n.href,document.baseURI);r.origin===location.origin&&(this.controller=Vt(async function(u){const o=await fetch(r.toString(),{signal:u}),a=await o.text(),i=t.styleSheet=await t.context.registerStyleSheet({source:a,url:r,signal:u}),p=new Blob([i.source],{type:"text/css"}),h=new Image;h.onload=h.onerror=i.refresh,h.src=n.href=URL.createObjectURL(p)}))}}disconnected(){var t,n;(t=this.controller)==null||t.abort(),this.controller=null,(n=this.styleSheet)==null||n.dispose(),this.styleSheet=null}}class Sn extends ee{constructor(t,n){super(t),this.context=void 0,this.controller=null,this.styleSheet=null,this.context=n}connected(){var t=this;this.controller=Vt(async function(n){const r=t.node,u=t.styleSheet=await t.context.registerStyleSheet({source:r.innerHTML,signal:n});r.innerHTML=u.source,u.refresh()})}disconnected(){var t,n;(t=this.controller)==null||t.abort(),this.controller=null,(n=this.styleSheet)==null||n.dispose(),this.styleSheet=null}}class xn extends ee{connected(){const t=`* { ${fe}: cq-normal; ${pe}: cq-none; }`;this.node.innerHTML=window.CSSLayerBlockRule===void 0?t:`@layer cq-polyfill-${ye} { ${t} }`}}class qn extends ee{constructor(t,n){super(t),this.context=void 0,this.styles=void 0,this.context=n,this.styles=window.getComputedStyle(t)}connected(){this.node.style.cssText="position: fixed; top: 0; left: 0; visibility: hidden; "+(vn?"width: 1svw; height: 1svh;":"width: 1%; height: 1%;")}updated(){const t=Be(n=>this.styles.getPropertyValue(n));this.context.viewportChanged({width:t.width,height:t.height})}}function Vt(e){const t=new AbortController;return e(t.signal).catch(n=>{if(!(n instanceof DOMException&&n.message==="AbortError"))throw n}),t}function Cn(e){let t=0;if(e.length===0||e.startsWith("cq-")&&((e=e.substring(3))==="normal"||Pe(e)))return t;const n=e.split(" ");for(const r of n)switch(r){case"size":t|=3;break;case"inline-size":t|=1;break;default:return 0}return t}function $n(e){let t=0;return e!=="none"&&(t|=1,e==="contents"||e==="inline"||wn.test(e)||(t|=2)),t}function $e(e,t){return parseFloat(e(t))}function Ct(e,t){return t.reduce((n,r)=>n+$e(e,r),0)}function Be(e){let t=0,n=0;return e("box-sizing")==="border-box"&&(t=Ct(e,mn),n=Ct(e,gn)),{fontSize:$e(e,"font-size"),width:$e(e,"width")-t,height:$e(e,"height")-n}}function $t(e){return{containerType:Cn(e(fe).trim()),containerNames:(n=e(pe).trim(),n.startsWith("cq-")&&((n=n.substring(3))==="none"||Pe(n))?new Set([]):new Set(n.length===0?[]:n.split(" "))),writingAxis:(t=e("writing-mode").trim(),dn.has(t)?1:0),displayFlags:$n(e("display").trim())};var t,n}function me(e,t,n){n!=null?n!=e.getPropertyValue(t)&&e.setProperty(t,n):e.removeProperty(t)}function kn(e){const t=e[Ht];return t??[]}function kt(e,t){e[Ht]=t}new Promise(e=>{}),window.CQPolyfill={version:"1.0.2"},"container"in document.documentElement.style||function(e){function t(c){return c[Ve]||null}const n=document.documentElement;if(t(n))return;const r=document.createElement(`cq-polyfill-${ye}`),u=document.createElement("style");new MutationObserver(c=>{for(const y of c){for(const g of y.removedNodes){const s=t(g);s==null||s.disconnect()}y.target.nodeType!==Node.DOCUMENT_NODE&&y.target.nodeType!==Node.DOCUMENT_FRAGMENT_NODE&&y.target.parentNode===null||y.type==="attributes"&&y.attributeName&&(y.attributeName===ge||y.attributeName===we||y.target instanceof Element&&y.target.getAttribute(y.attributeName)===y.oldValue)||(m(y.target).mutate(),$())}}).observe(n,{childList:!0,subtree:!0,attributes:!0,attributeOldValue:!0});const o=new ResizeObserver(c=>{for(const y of c)m(y.target).resize();m(n).update(R())}),a=new ee(n);async function i(c,{source:y,url:g,signal:s}){const f=function(x,P){try{const v=Array.from(Ge(x));if(P)for(let N=0;N<v.length;N++){const k=v[N];if(k.type===20)k.value=new URL(k.value,P).toString();else if(k.type===23&&k.value.toLowerCase()==="url"){const M=N+1<v.length?v[N+1]:null;M&&M.type===2&&(M.value=new URL(M.value,P).toString())}}const A={descriptors:[],parent:null,transformStyleRule:N=>N};return{source:Tt(Le(ae(v,!0),A)),descriptors:A.descriptors}}catch(v){return console.warn("An error occurred while transpiling stylesheet: "+v),{source:x,descriptors:[]}}}(y,g?g.toString():void 0);let d=()=>{},S=()=>{};const w=m(n);let q=!1;return s!=null&&s.aborted||(S=()=>{if(!q){const{sheet:x}=c;x!=null&&(kt(x,f.descriptors),q=!0,d=()=>{kt(x),w.mutate(),$()},w.mutate(),$())}}),{source:f.source,dispose:d,refresh:S}}const p={cqw:null,cqh:null};function h({width:c,height:y}){p.cqw=c,p.cqh=y}function E(c,y,g){if(c instanceof Element&&y){let s="";for(const[f,d]of y.conditions){const S=f.value;S.selector!=null&&d!=null&&(2&d)==2&&c.matches(S.selector)&&(s.length>0&&(s+=" "),s+=S.uid)}s.length>0?c.setAttribute(g,s):c.removeAttribute(g)}}function $(){o.unobserve(n),o.observe(n)}const j=()=>{const c=[];for(const y of document.styleSheets)for(const g of kn(y))c.push([new je(g),0]);return c},C=window.getComputedStyle(n),R=()=>{const c=s=>C.getPropertyValue(s),y=$t(c),g=Be(c);return{parentState:null,conditions:j(),context:b({},p,{fontSize:g.fontSize,rootFontSize:g.fontSize,writingAxis:y.writingAxis}),displayFlags:y.displayFlags,isQueryContainer:!1}},F=c=>c;function m(c){let y=t(c);if(!y){let g,s=null,f=!1;c===n?(g=a,s=F):c===r?(f=!0,g=new qn(r,{viewportChanged:h})):g=c===u?new xn(u):c instanceof HTMLLinkElement?new bn(c,{registerStyleSheet:v=>i(c,b({},v))}):c instanceof HTMLStyleElement?new Sn(c,{registerStyleSheet:v=>i(c,b({},v))}):new ee(c);let d=Symbol();if(s==null&&c instanceof Element){const v=function(A){const N=window.getComputedStyle(A);return function(k){let M=null;return(...J)=>{if(M==null||!Me(M[0],J)){const te=((K,Ne)=>{const{context:Oe,conditions:Fe}=K,tt=Ie=>N.getPropertyValue(Ie),he=$t(tt),ne=b({},Oe,{writingAxis:he.writingAxis});let Ue=Fe,nt=!1,Re=he.displayFlags;!(1&K.displayFlags)&&(Re=0);const{containerType:rt,containerNames:Dt}=he;if(rt>0){const Ie=rt>0&&(2&Re)==2,Wt=new Map(Fe.map(re=>[re[0].value,re[1]]));if(Ue=[],nt=!0,Ie){const re=Be(tt);ne.fontSize=re.fontSize;const ue=function(O,U){const I={value:U.width},Q={value:U.height};let X=I,D=Q;if(O.writingAxis===1){const ve=X;X=D,D=ve}return(2&O.containerType)!=2&&(D.value=void 0),{width:I.value,height:Q.value,inlineSize:X.value,blockSize:D.value}}(he,re),_t={sizeFeatures:ue,treeContext:ne},Bt=O=>{const{rule:U}=O,I=U.name,Q=I==null||Dt.has(I)?function(D,ve){const ot=new Map,Yt=ve.sizeFeatures;for(const lt of D.features){const ct=Jt(lt,Yt);if(ct.type===1)return null;ot.set(lt,ct)}const st=_(D.condition,{sizeFeatures:ot,treeContext:ve.treeContext});return st.type===5?st.value:null}(U,_t):null;var X;return Q==null?(((X=Wt.get(O))!=null?X:0)&&1)===1:Q===!0},ut=(O,U)=>{let I=O.get(U);if(I==null){const Q=Bt(U);I=(Q?1:0)|(Q!==!0||U.parent!=null&&(1&ut(O,U.parent))!=1?0:2),O.set(U,I)}return I},Gt=new Map;for(const O of Fe)Ue.push([O[0],ut(Gt,O[0].value)]);ne.cqw=ue.width!=null?ue.width/100:Oe.cqw,ne.cqh=ue.height!=null?ue.height/100:Oe.cqh}}return{parentState:new je(K),conditions:Ue,context:ne,displayFlags:Re,isQueryContainer:nt}})(...J);M!=null&&Me(M[1],te)||(M=[J,te])}return M[1]}}()}(c);s=A=>v(A,d)}const S=s||F;let w=null;const q=v=>{const A=w;return w=S(v),[w,w!==A]},x=c instanceof HTMLElement||c instanceof SVGElement?c.style:null;let P=!1;y={connect(){for(let v=c.firstChild;v!=null;v=v.nextSibling)m(v);g.connected()},disconnect(){c instanceof Element&&(o.unobserve(c),c.removeAttribute(ge),c.removeAttribute(we)),x&&(x.removeProperty(ke),x.removeProperty(Ae),x.removeProperty(le),x.removeProperty(ce));for(let v=c.firstChild;v!=null;v=v.nextSibling){const A=t(v);A==null||A.disconnect()}g.disconnected(),delete c[Ve]},update(v){const[A,N]=q(v);if(N){if(E(c,v,we),E(c,A,ge),c instanceof Element){const k=f||A.isQueryContainer;k&&!P?(o.observe(c),P=!0):!k&&P&&(o.unobserve(c),P=!1)}if(x){const k=A.context,M=k.writingAxis;let J=null,te=null,K=null,Ne=null;(M!==v.context.writingAxis||A.isQueryContainer)&&(J=`var(${M===0?le:ce})`,te=`var(${M===1?le:ce})`),v&&!A.isQueryContainer||(k.cqw&&(K=k.cqw+"px"),k.cqh&&(Ne=k.cqh+"px")),me(x,ke,J),me(x,Ae,te),me(x,le,K),me(x,ce,Ne)}g.updated()}for(let k=c.firstChild;k!=null;k=k.nextSibling)m(k).update(A)},resize(){d=Symbol()},mutate(){d=Symbol();for(let v=c.firstChild;v!=null;v=v.nextSibling)m(v).mutate()}},c[Ve]=y,y.connect()}return y}n.prepend(u,r),m(n),$()}();
