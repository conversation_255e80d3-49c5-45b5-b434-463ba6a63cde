{% liquid
  assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
  assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
  assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
%}
<style>
  {{ settings.type_body_font | font_face: font_display: 'swap' }}
  {{ body_font_bold | font_face: font_display: 'swap' }}
  {{ body_font_italic | font_face: font_display: 'swap' }}
  {{ body_font_bold_italic | font_face: font_display: 'swap' }}
  {{ settings.type_heading_font | font_face: font_display: 'swap' }}

  :root {
    --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
    --font-body-style: {{ settings.type_body_font.style }};
    --font-body-weight: {{ settings.type_body_font.weight }};
    --font-body-weight-bold: {{ body_font_bold.weight | default: 700 }};

    --font-heading-family: {{ settings.type_heading_font.family }}, {{ settings.type_heading_font.fallback_families }};
    --font-heading-style: {{ settings.type_heading_font.style }};
    --font-heading-weight: {{ settings.type_heading_font.weight }};
    --heading-text-transform: {{ settings.type_heading_text_style }};
    --heading-letter-spacing: {{ settings.type_heading_letter_spacing | divided_by: 1000.0 }}em;

    --font-body-scale: {{ settings.type_body_base_size | divided_by: 16.0 }};
    --font-body-letter-spacing: {{ settings.type_body_letter_spacing | divided_by: 1000.0 }}em;

    {% case settings.type_navigation_font %}
      {% when "body_bold" %}
        --navigation-font-family: var(--font-body-family);
        --navigation-font-weight: var(--font-body-weight-bold);
        --navigation-font-style: var(--font-body-style);
      {% when "heading" %}
        --navigation-font-family: var(--font-heading-family);
        --navigation-font-weight: var(--font-heading-weight);
        --navigation-font-style: var(--font-heading-style);
      {% else %}
        --navigation-font-family: var(--font-body-family);
        --navigation-font-weight: var(--font-body-weight);
        --navigation-font-style: var(--font-body-style);
    {% endcase %}

    --navigation-text-transform: {{ settings.type_navigation_text_style }};

    {% case settings.type_button_font %}
      {% when "body_bold" %}
        --button-font-family: var(--font-body-family);
        --button-font-weight: var(--font-body-weight-bold);
        --button-font-style: var(--font-body-style);
      {% when "heading" %}
        --button-font-family: var(--font-heading-family);
        --button-font-weight: var(--font-heading-weight);
        --button-font-style: var(--font-heading-style);
      {% else %}
        --button-font-family: var(--font-body-family);
        --button-font-weight: var(--font-body-weight);
        --button-font-style: var(--font-body-style);
    {% endcase %}

    --button-text-transform: {{ settings.type_button_text_style }};

    {% case settings.type_label_font %}
      {% when "body_bold" %}
        --label-font-family: var(--font-body-family);
        --label-font-weight: var(--font-body-weight-bold);
        --label-font-style: var(--font-body-style);
      {% when "heading" %}
        --label-font-family: var(--font-heading-family);
        --label-font-weight: var(--font-heading-weight);
        --label-font-style: var(--font-heading-style);
      {% else %}
        --label-font-family: var(--font-body-family);
        --label-font-weight: var(--font-body-weight);
        --label-font-style: var(--font-body-style);
    {% endcase %}

    --label-text-transform: {{ settings.type_label_text_style }};


    --label-font-size: var(--size-text-sm);

    {% case settings.type_product_card_font %}
      {% when "body_bold" %}
        --product-card-font-family: var(--font-body-family);
        --product-card-font-weight: var(--font-body-weight-bold);
      {% when "heading" %}
        --product-card-font-family: var(--font-heading-family);
        --product-card-font-weight: var(--font-heading-weight);
      {% else %}
        --product-card-font-family: var(--font-body-family);
        --product-card-font-weight: var(--font-body-weight);
    {% endcase %}

    {% case settings.type_accordion_font %}
      {% when "body_bold" %}
        --accordion-font-family: var(--font-body-family);
        --accordion-font-weight: var(--font-body-weight-bold);
      {% when "heading" %}
        --accordion-font-family: var(--font-heading-family);
        --accordion-font-weight: var(--font-heading-weight);
        --accordion-text-transform: var(--heading-text-transform);
        --accordion-letter-spacing: var(--heading-letter-spacing);
      {% else %}
        --accordion-font-family: var(--font-body-family);
        --accordion-font-weight: var(--font-body-weight);
    {% endcase %}

    --font-heading-scale: 1;
    --size-text-h0: calc(var(--size-h0-rfs) * var(--font-heading-scale));
    --size-text-h1: calc(var(--size-h1-rfs) * var(--font-heading-scale));
    --size-text-h2: calc(var(--size-h2-rfs) * var(--font-heading-scale));
    --size-text-h3: calc(var(--size-h3-rfs) * var(--font-heading-scale));
    --size-text-h4: calc(var(--size-h4-rfs) * var(--font-heading-scale));
    --size-text-h5: calc(var(--size-h5-rfs) * var(--font-heading-scale));
    --size-text-h6: calc(var(--size-h6-rfs) * var(--font-heading-scale));
  }
</style>
