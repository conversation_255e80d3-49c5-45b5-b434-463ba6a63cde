{% liquid
  assign popular_searches = section.settings.popular_searches | newline_to_br | strip_newlines | split: '<br />'
%}

<search-modal
  position="right"
  initial-focus="input[type=search]"
  class="modal modal-drawer modal-drawer--right modal-search"
  id="search-modal"
>
  <div
    slot="content"
    tabindex="-1"
    class="p-6 md:p-8 lg:p-12 [--bleed-distance:1.5rem] md:[--bleed-distance:2rem] lg:[--bleed-distance:3rem]"
  >
    {% if section.settings.heading != blank %}
      <div class="mb-4 md:mb-6 flex justify-between">
        <div class="text-h6/h6 md:text-h4/h4 heading">
          {{ section.settings.heading }}
        </div>
        <button class="modal-close -mt-2 mr-2" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
          {% render 'icon-times' %}
        </button>
      </div>
    {% endif %}

    <div class="bleed flex items-center gap-x-2 md:gap-x-4 sticky top-0 z-10 bg-modal-background pt-4 pb-4 -mt-4 mb-2 md:mb-4">
      <form action="{{ routes.search_url }}" class="input-wrapper grow">
        {% comment %} <input type="hidden" name="type" value="product"> {% endcomment %}
        <input
          type="search"
          name="q"
          class="input has-icon-right text-base md:text-h5"
          aria-label="{{ 'templates.search.input_label' | t }}"
          placeholder="{{ 'templates.search.input_label' | t }}"
          autocorrect="off"
          autocomplete="off"
          autocapitalize="off"
          spellcheck="false"
        >
        <button class="icon-right icon-sm md:icon-lg px-4 box-content">
          {% render 'icon-search' %}
        </button>
      </form>

      {% if section.settings.heading == blank %}
        <button class="modal-close -mr-3 md:-mr-6" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
          {% render 'icon-times' %}
        </button>
      {% endif %}
    </div>

    {% if popular_searches.size > 0 %}
      <div data-placeholder>
        {% if section.settings.popular_searches_heading != blank %}
          <div class="h5 mb-4 md:mb-6">
            {{ section.settings.popular_searches_heading }}
          </div>
        {% endif %}

        <ul class="flex flex-wrap gap-2 md:gap-3">
          {% for item in popular_searches %}
            {% unless item == blank %}
              <li>
                {% assign popular_search_encoded = item | url_encode %}
                {% assign popular_search_url = routes.search_url | append: '?q=' | append: popular_search_encoded %}
                {{
                  item
                  | link_to: popular_search_url, class: 'button-pill text-xs md:text-sm py-2 px-4', data-instant: true
                }}
              </li>
            {% endunless %}
          {% endfor %}
        </ul>
      </div>
    {% endif %}

    <div
      class="hidden predictive-search"
      data-results-container
    ></div>

    <div
      class="hidden predictive-search"
      data-skeleton
    >
      {% render 'predictive-search-results', skeleton: true %}
    </div>
  </div>
</search-modal>

{% schema %}
{
  "name": "t:sections.search-modal.name",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "header",
      "content": "t:sections.search-modal.headers.featured_queries.content",
      "info": "t:sections.search-modal.headers.featured_queries.info"
    },
    {
      "type": "text",
      "id": "popular_searches_heading",
      "label": "t:sections.search-modal.settings.popular_searches_heading.label",
      "default": "Popular searches"
    },
    {
      "type": "textarea",
      "id": "popular_searches",
      "label": "t:sections.search-modal.settings.popular_searches.label",
      "info": "t:sections.search-modal.settings.popular_searches.info"
    }
  ]
}
{% endschema %}
