{% liquid
  if section.settings.video == blank and section.settings.video_url != blank and section.settings.image_size == ''
    assign media_class = 'media--ratio-16-9'
  endif

  if section.settings.video != blank and section.settings.image_size == ''
    capture media_style
      echo '--aspect-ratio: '
      echo section.settings.video.aspect_ratio
      echo ';'
    endcapture
  endif

  if section.settings.full_width
    assign media_class = media_class | append: ' bleed-margin'
  else
    assign media_class = media_class | append: ' rounded-block shadow-block'
  endif

  capture poster_sizes
    render 'image-sizes-full-width', full_width: section.settings.full_width
  endcapture
%}
{% render 'section-bg-number-vars', full_width: true %}

<div {% render 'section-attrs', type: 'section--full-width' %}>
  {% render 'section-header' %}

  <div class="section-body">
    <div
      class="media {{ media_class }} {{ section.settings.image_size }}"
      style="{{ media_style }}"
      {% if section.settings.full_width == false %}
        data-animation="block"
        data-animation-group="{{ section.id }}"
      {% endif %}
    >
      {% render 'video-player',
        video: section.settings.video,
        video_url: section.settings.video_url,
        video_poster: section.settings.video_poster,
        sizes: poster_sizes
      %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.video.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": false
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label",
      "info": "t:sections.video.settings.video.info"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "label": "t:sections.video.settings.video_url.label",
      "accept": [
        "youtube",
        "vimeo"
      ],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "image_picker",
      "id": "video_poster",
      "label": "t:sections.video.settings.video_poster.label"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:sections.video.settings.image_size.label",
      "options": [
        {
          "value": "media--ratio-4-3",
          "label": "t:sections.all.image_size.options.landscape_4_3"
        },
        {
          "value": "media--ratio-16-9",
          "label": "t:sections.all.image_size.options.landscape_wide_16_9"
        },
        {
          "value": "media--ratio-3-4",
          "label": "t:sections.all.image_size.options.portrait_3_4"
        },
        {
          "value": "media--ratio-2-3",
          "label": "t:sections.all.image_size.options.portrait_tall_2_3"
        },
        {
          "value": "media--ratio-1-1",
          "label": "t:sections.all.image_size.options.square_1_1"
        },
        {
          "value": "",
          "label": "t:sections.all.image_size.options.original_image_size"
        }
      ],
      "default": "media--ratio-16-9"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.text_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.text_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.text_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.text_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.video.presets__0.name"
    }
  ]
}
{% endschema %}
