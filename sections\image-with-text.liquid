{% liquid
  assign image_column_ratio = 1.0 | divided_by: section.settings.image_width | times: 100.0 | round: 2

  capture image_sizes
    if section.settings.full_width
      render 'image-sizes-columns-full-width', base: 1, lg: image_column_ratio
    else
      render 'image-sizes-columns', base: 1, lg: image_column_ratio
    endif
  endcapture
%}

{% capture media %}
  {% if section.settings.image %}
    {{ section.settings.image | image_url: width: 3840 | image_tag: sizes: image_sizes, loading: 'lazy' }}
  {% else %}
    {% render 'placeholder', type: 'image' %}
  {% endif %}
{% endcapture %}

{% render 'media-with-text', media: media %}

{% schema %}
{
  "name": "t:sections.image-with-text.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "remove_bottom_spacing",
      "label": "t:sections.all.remove_bottom_spacing.label",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text.settings.image.label"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:sections.all.image_size.label",
      "options": [
        {
          "value": "media--ratio-4-3",
          "label": "t:sections.all.image_size.options.landscape_4_3"
        },
        {
          "value": "media--ratio-16-9",
          "label": "t:sections.all.image_size.options.landscape_wide_16_9"
        },
        {
          "value": "media--ratio-3-4",
          "label": "t:sections.all.image_size.options.portrait_3_4"
        },
        {
          "value": "media--ratio-2-3",
          "label": "t:sections.all.image_size.options.portrait_tall_2_3"
        },
        {
          "value": "media--ratio-1-1",
          "label": "t:sections.all.image_size.options.square_1_1"
        },
        {
          "value": "",
          "label": "t:sections.all.image_size.options.original_image_size"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:sections.image-with-text.settings.image_position.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.image_position.options__0.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.image_position.options__1.label"
        }
      ],
      "default": "right"
    },
    {
      "type": "range",
      "id": "image_width",
      "min": 25,
      "max": 75,
      "step": 5,
      "unit": "%",
      "label": "t:sections.image-with-text.settings.image_width.label",
      "default": 50
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.content"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.text_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.text_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.text_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.text_alignment.options.text_right.label"
        }
      ],
      "default": "text-left"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    }
  ],
  "blocks": [
    {
      "type": "subheading",
      "name": "t:sections.image-with-text.blocks.subheading.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.image-with-text.blocks.subheading.settings.text.label",
          "default": "Subheading"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Image with text",
          "label": "t:sections.all.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:sections.all.heading_size.label",
          "options": [
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__0.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__6.label"
            }
          ],
          "default": "h1"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.seo"
        },
        {
          "type": "select",
          "id": "heading_html_tag",
          "label": "t:sections.all.heading_html_tag.label",
          "options": [
            {
              "value": "h1",
              "label": "h1"
            },
            {
              "value": "h2",
              "label": "h2"
            },
            {
              "value": "h3",
              "label": "h3"
            },
            {
              "value": "h4",
              "label": "h4"
            },
            {
              "value": "h5",
              "label": "h5"
            },
            {
              "value": "h6",
              "label": "h6"
            },
            {
              "value": "p",
              "label": "p"
            },
            {
              "value": "span",
              "label": "span"
            },
            {
              "value": "div",
              "label": "div"
            }
          ],
          "default": "p",
          "info": "t:sections.all.heading_html_tag.info"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Showcase visual content with accompanying text. Accessible and responsive on all devices to grab your audience's attention.</p>",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "settings": [
        {
          "type": "text",
          "id": "button_text",
          "default": "Button label",
          "label": "t:sections.image-with-text.blocks.button.settings.button_text.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "t:sections.image-with-text.blocks.button.settings.button_size.label",
          "options": [
            {
              "value": "text-xs md:text-sm",
              "label": "t:sections.image-with-text.blocks.button.settings.button_size.options__0.label"
            },
            {
              "value": "text-sm md:text-base",
              "label": "t:sections.image-with-text.blocks.button.settings.button_size.options__1.label"
            },
            {
              "value": "text-base md:text-lg",
              "label": "t:sections.image-with-text.blocks.button.settings.button_size.options__2.label"
            },
            {
              "value": "text-lg md:text-h5",
              "label": "t:sections.image-with-text.blocks.button.settings.button_size.options__3.label"
            }
          ],
          "default": "text-sm md:text-base"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "label": "t:sections.all.background_color.label",
          "id": "button_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.all.text_color.label",
          "id": "button_text_color"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.image-with-text.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "icon",
      "name": "t:sections.image-with-text.blocks.icon.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "t:sections.all.custom_icon.label"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "t:sections.all.icon.label",
          "options": [
            {
              "group": "t:sections.all.icon.options__0.group",
              "value": "message-chat-circle",
              "label": "t:sections.all.icon.options__0.label"
            },
            {
              "group": "t:sections.all.icon.options__1.group",
              "value": "at-sign",
              "label": "t:sections.all.icon.options__1.label"
            },
            {
              "group": "t:sections.all.icon.options__2.group",
              "value": "headphones",
              "label": "t:sections.all.icon.options__2.label"
            },
            {
              "group": "t:sections.all.icon.options__3.group",
              "value": "mail",
              "label": "t:sections.all.icon.options__3.label"
            },
            {
              "group": "t:sections.all.icon.options__4.group",
              "value": "phone",
              "label": "t:sections.all.icon.options__4.label"
            },
            {
              "group": "t:sections.all.icon.options__5.group",
              "value": "message-text-square",
              "label": "t:sections.all.icon.options__5.label"
            },
            {
              "group": "t:sections.all.icon.options__6.group",
              "value": "voicemail",
              "label": "t:sections.all.icon.options__6.label"
            },
            {
              "group": "t:sections.all.icon.options__7.group",
              "value": "bank-note",
              "label": "t:sections.all.icon.options__7.label"
            },
            {
              "group": "t:sections.all.icon.options__8.group",
              "value": "credit-card",
              "label": "t:sections.all.icon.options__8.label"
            },
            {
              "group": "t:sections.all.icon.options__9.group",
              "value": "credit-card-check",
              "label": "t:sections.all.icon.options__9.label"
            },
            {
              "group": "t:sections.all.icon.options__10.group",
              "value": "credit-card-shield",
              "label": "t:sections.all.icon.options__10.label"
            },
            {
              "group": "t:sections.all.icon.options__11.group",
              "value": "shield",
              "label": "t:sections.all.icon.options__11.label"
            },
            {
              "group": "t:sections.all.icon.options__12.group",
              "value": "percent",
              "label": "t:sections.all.icon.options__12.label"
            },
            {
              "group": "t:sections.all.icon.options__13.group",
              "value": "tag",
              "label": "t:sections.all.icon.options__13.label"
            },
            {
              "group": "t:sections.all.icon.options__14.group",
              "value": "shopping-cart",
              "label": "t:sections.all.icon.options__14.label"
            },
            {
              "group": "t:sections.all.icon.options__15.group",
              "value": "plane",
              "label": "t:sections.all.icon.options__15.label"
            },
            {
              "group": "t:sections.all.icon.options__16.group",
              "value": "package-check",
              "label": "t:sections.all.icon.options__16.label"
            },
            {
              "group": "t:sections.all.icon.options__17.group",
              "value": "package",
              "label": "t:sections.all.icon.options__17.label"
            },
            {
              "group": "t:sections.all.icon.options__18.group",
              "value": "return",
              "label": "t:sections.all.icon.options__18.label"
            },
            {
              "group": "t:sections.all.icon.options__19.group",
              "value": "truck",
              "label": "t:sections.all.icon.options__19.label"
            },
            {
              "group": "t:sections.all.icon.options__20.group",
              "value": "check-heart",
              "label": "t:sections.all.icon.options__20.label"
            },
            {
              "group": "t:sections.all.icon.options__21.group",
              "value": "check",
              "label": "t:sections.all.icon.options__21.label"
            },
            {
              "group": "t:sections.all.icon.options__22.group",
              "value": "star",
              "label": "t:sections.all.icon.options__22.label"
            },
            {
              "group": "t:sections.all.icon.options__23.group",
              "value": "thumbs-up",
              "label": "t:sections.all.icon.options__23.label"
            },
            {
              "group": "t:sections.all.icon.options__24.group",
              "value": "check-verified",
              "label": "t:sections.all.icon.options__24.label"
            },
            {
              "group": "t:sections.all.icon.options__25.group",
              "value": "building",
              "label": "t:sections.all.icon.options__25.label"
            },
            {
              "group": "t:sections.all.icon.options__26.group",
              "value": "file",
              "label": "t:sections.all.icon.options__26.label"
            },
            {
              "group": "t:sections.all.icon.options__27.group",
              "value": "gift",
              "label": "t:sections.all.icon.options__27.label"
            },
            {
              "group": "t:sections.all.icon.options__28.group",
              "value": "heart",
              "label": "t:sections.all.icon.options__28.label"
            },
            {
              "group": "t:sections.all.icon.options__29.group",
              "value": "heart-hand",
              "label": "t:sections.all.icon.options__29.label"
            },
            {
              "group": "t:sections.all.icon.options__30.group",
              "value": "marker-pin",
              "label": "t:sections.all.icon.options__30.label"
            },
            {
              "group": "t:sections.all.icon.options__31.group",
              "value": "face-smile",
              "label": "t:sections.all.icon.options__31.label"
            },
            {
              "group": "t:sections.all.icon.options__32.group",
              "value": "store",
              "label": "t:sections.all.icon.options__32.label"
            },
            {
              "group": "t:sections.all.icon.options__33.group",
              "value": "target",
              "label": "t:sections.all.icon.options__33.label"
            },
            {
              "group": "t:sections.all.icon.options__34.group",
              "value": "user",
              "label": "t:sections.all.icon.options__34.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "icon_background",
          "label": "t:sections.all.icon_background.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.icon_background.options__0.label"
            },
            {
              "value": "square",
              "label": "t:sections.all.icon_background.options__1.label"
            },
            {
              "value": "circle",
              "label": "t:sections.all.icon_background.options__2.label"
            }
          ],
          "default": ""
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 24,
          "max": 96,
          "step": 8,
          "unit": "px",
          "label": "t:sections.all.icon_width.label",
          "default": 48
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "id": "icon_background_color",
          "label": "t:sections.all.icon_background_color.label"
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "t:sections.all.icon_color.label"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.image-with-text.presets__0.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
