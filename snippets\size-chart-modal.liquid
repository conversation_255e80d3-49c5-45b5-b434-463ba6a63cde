<modal-trigger class="contents" target="#size-chart-modal-{{ product.id }}">
  <a
    href="{{ block.settings.size_chart_page.url }}"
    class="ms-auto styled-link text-foreground/75 text-xs md:text-sm inline-flex gap-1.5 items-center"
  >
    <div class="w-4">
      {% render 'icon-size-chart' %}
    </div>
    <span>
      {{ 'products.product.size_chart' | t }}
    </span>
  </a>
</modal-trigger>

<modal-drawer
  id="size-chart-modal-{{ product.id }}"
  append-to-body
  position="right"
  class="contents modal modal-drawer modal-drawer--lg"
>
  <div slot="content" tabindex="-1" class="max-h-full flex flex-col">
    <div class="modal-header flex items-center">
      <div class="h5">{{ block.settings.size_chart_page.title }}</div>
      <button class="modal-close ml-auto" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
        {% render 'icon-times' %}
      </button>
    </div>
    <div class="modal-body overflow-y-auto">
      <div class="prose ">
        {{ block.settings.size_chart_page.content }}
      </div>
    </div>
  </div>
</modal-drawer>
