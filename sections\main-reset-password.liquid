{% render 'section-bg-number-vars' %}

<div class="section section--full-width styled-links text-center" style="--container-max-inner-width: 512px;">
  <h1 class="h3 mb-4 md:mb-6" tabindex="-1">
    {{ 'customer.reset_password.title' | t }}
  </h1>
  <p class="rfs:mb-12">
    {{ 'customer.reset_password.subtext' | t }}
  </p>

  {%- form 'reset_customer_password', id: 'form-password-reset' -%}
    {% if form.errors %}
      <div class="mb-6">
        {% render 'form-errors', errors: form.errors, form_id: 'form-password-reset' %}
      </div>
    {% endif %}

    <div class="form-floating">
      <input
        class="input"
        type="password"
        name="customer[password]"
        id="form-password-reset-password"
        autocomplete="new-password"
        placeholder="{{ 'customer.reset_password.password' | t }}"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="password-error"
        {% endif %}
      >

      <label for="form-password-reset-password">
        {{ 'customer.reset_password.password' | t }}
      </label>
    </div>

    <div class="form-floating mt-6">
      <input
        class="input"
        type="password"
        name="customer[password_confirmation]"
        id="form-password-reset-password_confirmation"
        autocomplete="new-password"
        placeholder="{{ 'customer.reset_password.password_confirm' | t }}"
        {% if form.errors contains 'password_confirmation' %}
          aria-invalid="true"
          aria-describedby="password_confirmation-error"
        {% endif %}
      >

      <label for="form-password-reset-password_confirmation">
        {{ 'customer.reset_password.password_confirm' | t }}
      </label>
    </div>

    <div class="mt-12">
      <button class="button button-primary">
        {{ 'customer.reset_password.submit' | t }}
      </button>
    </div>
  {%- endform -%}
</div>
