{% liquid
  if section.settings.carousel == false
    assign carousel_tag = 'div'
    assign grid_class = 'grid-carousel--stack rfs:gap-y-12'
  else
    assign carousel_tag = 'scroll-carousel'
    assign carousel_class = 'grid scroll-area-x bleed'
  endif

  assign articles = section.settings.blog.articles

  if section.blocks.size > 0
    assign articles = section.blocks | map: 'settings' | map: 'article'
  endif

  assign articles_size = articles | size

  assign columns_sm = 2.25 | at_most: section.settings.grid_columns
  assign columns_lg = 3 | at_most: section.settings.grid_columns
  assign columns_hd = 4 | at_most: section.settings.grid_columns

  capture article_image_sizes
    render 'image-sizes-columns', base: 1.2, sm: columns_sm, lg: columns_lg, hd: columns_hd
  endcapture
%}

<style>
  #shopify-section-{{ section.id }} {
    --grid-columns-max: {{ section.settings.grid_columns }};
  }
</style>

{% render 'section-bg-number-vars' %}

{% capture item_attrs %}
data-animation="block"
data-animation-group="{{ section.id }}-article-cards"
{% endcapture %}

<div {% render 'section-attrs' %}>
  {% render 'section-header', link_text: section.settings.link_text, link_url: section.settings.blog.url %}

  <div class="section-body">
    <div class="relative">
      <{{ carousel_tag }} class="{{ carousel_class }}" item-selector=".article-card">
        <div
          class="
            grid-carousel
            grid-carousel--pseudo-pr
            [--grid-gap-min:.75rem]
            md:[--grid-gap-min:1.5rem]
            [--grid-columns:1.2]
            sm:[--grid-columns:2.25]
            lg:[--grid-columns:3]
            hd:[--grid-columns:4]
            {{ grid_class }}
          "
        >
          {% if section.settings.blog == blank and articles_size == 0 %}
            {% comment %} Placeholder articles {% endcomment %}
            {% for n in (1..3) %}
              <div class="grid-carousel-item">
                {% render 'article-card',
                  excerpt: false,
                  details: false,
                  attrs: item_attrs,
                  tag: section.settings.show_category,
                  class: 'h-full',
                  sizes: article_image_sizes
                %}
              </div>
            {% endfor %}
          {% else %}
            {% for article in articles %}
              {% if forloop.index <= section.settings.articles_count %}
                <div class="grid-carousel-item">
                  {% render 'article-card',
                    card_article: article,
                    excerpt: false,
                    attrs: item_attrs,
                    tag: section.settings.show_category,
                    class: 'h-full',
                    sizes: article_image_sizes,
                    carousel: true
                  %}
                </div>
              {% endif %}
            {% endfor %}
          {% endif %}
        </div>
      </{{ carousel_tag }}>

      {% render 'carousel-buttons' %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "class": "section-featured-blog overflow-hidden",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "carousel",
      "label": "t:sections.all.carousel.label",
      "default": true
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label",
      "info": "t:sections.featured-blog.settings.blog.info"
    },
    {
      "type": "range",
      "id": "articles_count",
      "min": 3,
      "max": 12,
      "label": "t:sections.featured-blog.settings.articles_count.label",
      "default": 3
    },
    {
      "type": "range",
      "id": "grid_columns",
      "label": "t:sections.featured-blog.settings.grid_columns.label",
      "min": 2,
      "max": 4,
      "default": 3
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.featured-blog.settings.heading.label",
      "default": "Blog posts"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.text_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.text_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.text_alignment.options.text_center.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "t:sections.featured-blog.settings.link_text.label",
      "default": "View all"
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "label": "t:sections.featured-blog.settings.show_category.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "blocks": [
    {
      "type": "article",
      "name": "t:sections.featured-blog.blocks.article.name",
      "settings": [
        {
          "type": "article",
          "id": "article",
          "label": "t:sections.featured-blog.blocks.article.settings.article.label"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.featured-blog.name"
    }
  ]
}
{% endschema %}
