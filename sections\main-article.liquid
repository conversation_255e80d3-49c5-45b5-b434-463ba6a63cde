{% liquid
  assign main_tag = article.tags | first
  assign main_tag_handle = main_tag | handleize
  assign main_tag_url = blog.url | append: '/tagged/' | append: main_tag_handle
  assign section_class = 'trim-margins'

  if section.blocks[0].type == 'featured_image'
    assign section_class = section_class | append: ' [--section-pt-max:2.5rem]'

    if section.blocks[0].settings.full_width
      assign section_class = section_class | append: ' !pt-0'
    endif
  endif
%}

{% capture prev_next_image_sizes %}
min({{ section.settings.content_width | divided_by: 2 }}px, 50vw)
{% endcapture %}

{% render 'section-bg-number-vars' %}

<style>
  #shopify-section-{{ section.id }} {
    --container-max-inner-width: {{ section.settings.content_width }}px;
  }

  {% if section.settings.article_title_color != blank and section.settings.article_title_color.rgba != '0 0 0 / 0.0' %}
    #shopify-section-{{ section.id }} .article__title {
      --color-headings: {{ section.settings.article_title_color.rgb }};
    }
  {% endif %}
</style>

<article {% render 'section-attrs', class: section_class %}>
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
        {% render block %}
      {%- when 'featured_image' -%}
        {%- if article.image -%}
          <div class="bleed-margin">
            <div
              class="
                article__image article__image--block media {{ block.settings.image_size }} my-8 lg:my-12
                {% if block.settings.full_width %}

                {% else %}
                  max-w-5xl mx-auto [@media(min-width:1024px)]:rounded-block
                {% endif %}
              "
              {{ block.shopify_attributes }}
              data-animation
              data-animation-group="{{ section.id }}"
            >
              {% capture image_sizes %}
                {%- if block.settings.full_width -%}
                  100vw
                {%- else -%}
                  min({{ section.settings.content_width | plus: 48 }}px, 100vw)
                {%- endif -%}
              {% endcapture %}

              <lqip-element class="image-loader">
                {{ article.image | image_url: width: 3840 | image_tag: sizes: image_sizes }}
                {{
                  article.image
                  | image_url: width: 20
                  | image_tag: class: 'media-lq-placeholder', fetchpriority: 'low'
                }}
              </lqip-element>
            </div>
          </div>
        {%- endif -%}
      {%- when 'title' -%}
        <div
          class="my-6 {{ block.settings.text_alignment }}"
          {{ block.shopify_attributes }}
          data-animation
          data-animation-group="{{ section.id }}"
        >
          <div>
            {% if block.settings.show_tag and article.tags[0] %}
              <a href="{{ main_tag_url }}" class="info-badge article-badge mb-4" data-instant>
                {{ article.tags | first }}
              </a>
            {% endif %}
            <h1 class="{{ block.settings.heading_size }} normal-case article__title">{{ article.title | escape }}</h1>
          </div>

          {% if block.settings.show_author or block.settings.show_date or block.settings.show_comment_count %}
            <div class="mt-6 inline-flex flex-wrap items-center gap-y-2 gap-x-3 md:gap-x-4 max-md:text-sm">
              {% if block.settings.show_author %}
                <div class="text-foreground/75">
                  {{ 'blogs.article.author' | t: author: article.author }}
                </div>
              {% endif %}

              {% if block.settings.show_date %}
                <div class="separator-dot first:hidden"></div>
                <div class="text-foreground/75">{{ article.published_at | time_tag: format: 'date' }}</div>
              {% endif %}

              {% if block.settings.show_comment_count %}
                <div class="separator-dot first:hidden"></div>
                <a href="#comments" class="text-foreground/75 styled-link">
                  {{- 'blogs.article.comments' | t: count: article.comments_count -}}
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>

      {%- when 'content' -%}
        <div
          class="prose prose-lg my-8 mx-auto max-w-none"
          {{ block.shopify_attributes }}
          data-animation
          data-animation-group="{{ section.id }}"
        >
          {{ article.content }}
        </div>

      {% when 'share-button' %}
        <div class="my-8 md:my-12" {{ block.shopify_attributes }}>
          {% render 'share-button' %}
        </div>

      {% when 'previous-next-articles' %}
        {% assign prev_article = blog.previous_article %}
        {% assign next_article = blog.next_article %}
        {% if prev_article or next_article %}
          <div
            class="grid grid-cols-2 gap-block my-8 md:my-12"
            {{ block.shopify_attributes }}
          >
            {% if prev_article %}
              <a
                class="group"
                href="{{ prev_article.url }}"
                data-animation
                data-animation-group="{{ section.id }}-prev-next-article"
                data-instant
              >
                <div class="article__image">
                  <div class="subheading mb-4 md:mb-6 max-md:text-xs">{{ 'blogs.article.previous' | t }}</div>

                  {% if prev_article.image %}
                    <lqip-element class="image-loader media {{ settings.article_card_image_ratio }} rounded-block mb-4 md:mb-6">
                      {{
                        prev_article.image
                        | image_url: width: 1024
                        | image_tag: sizes: prev_next_image_sizes, loading: 'lazy', class: 'image-hover-zoom'
                      }}
                    </lqip-element>
                  {% endif %}
                </div>
                <h2 class="font-bold max-md:text-sm">{{ prev_article.title | escape }}</h2>
              </a>
            {% endif %}
            {% if next_article %}
              <a
                href="{{ next_article.url }}"
                class="group {% if prev_article %} text-right {% endif %}"
                data-animation
                data-animation-group="{{ section.id }}-prev-next-article"
                data-instant
              >
                <div class="article__image">
                  <div class="subheading mb-4 md:mb-6 max-md:text-xs">{{ 'blogs.article.next' | t }}</div>
                  {% if next_article.image %}
                    <lqip-element class="image-loader media {{ settings.article_card_image_ratio }} rounded-block mb-4 md:mb-6">
                      {{
                        next_article.image
                        | image_url: width: 1024
                        | image_tag: sizes: prev_next_image_sizes, loading: 'lazy', class: 'image-hover-zoom'
                      }}
                    </lqip-element>
                  {% endif %}
                </div>
                <h2 class="font-bold max-md:text-sm">{{ next_article.title | escape }}</h2>
              </a>
            {% endif %}
          </div>
        {% endif %}
      {% when 'comments' %}
        {% if article.comments_enabled? %}
          <div id="comments" class="my-8 sm:my-12" {{ block.shopify_attributes }}>
            <hr class="mb-8 sm:mb-12">
            <div>
              {% if article.comments != blank %}
                <div class="mb-12 sm:mb-16">
                  <h3 class="h5 mb-8">{{ 'blogs.article.comments' | t: count: article.comments_count }}</h3>

                  <div class="grid gap-8 sm:gap-12 loading-target">
                    {% paginate article.comments by block.settings.per_page %}
                      {% for comment in article.comments %}
                        <div class="blog-comment flex gap-6">
                          <div class="w-12 h-12 sm:w-14 sm:h-14 shrink-0">
                            {% render 'icon-avatar' %}
                          </div>
                          <div class="grow">
                            <div class="font-bold mb-1">{{ comment.author }}</div>
                            <div class="prose  max-w-none">
                              {{ comment.content }}
                            </div>
                            <div class="opacity-75 text-sm mt-3">
                              {{ comment.created_at | time_tag: format: 'date_at_time' }}
                            </div>
                          </div>
                        </div>
                      {% endfor %}

                      {% render 'pagination-classic', paginate: paginate, scroll_to_target: '#comments' %}
                    {% endpaginate %}
                  </div>
                </div>
              {% endif %}

              <div>
                <h3 class="h5">{{ 'blogs.article.leave_comment' | t }}</h3>

                {% if article.moderated? %}
                  <p class="mt-4">{{ 'blogs.article.comments_moderated' | t }}</p>
                {% endif %}

                {% form 'new_comment', article, data-form-button-loading: true %}
                  {% if form.errors %}
                    <div class="mt-6 md:mt-8">
                      {% render 'form-errors', errors: form.errors, form_id: 'comment_form' %}
                    </div>
                  {% endif %}

                  {% if form.posted_successfully? %}
                    {% liquid
                      assign post_message = 'blogs.article.comment_success'
                      if blog.moderated? and comment.status == 'unapproved'
                        assign post_message = 'blogs.article.comment_success_moderated'
                      endif
                    %}

                    <h3 class="message message-success text-success mt-6 md:mt-8" tabindex="-1" autofocus>
                      {{ post_message | t }}
                    </h3>
                  {% endif %}

                  <div class="grid gap-4 mt-6 md:mt-8">
                    <div class="name form-floating">
                      <input
                        id="comment-name"
                        required
                        placeholder="{{ 'blogs.article.comment_form_name' | t }}"
                        class="input"
                        type="text"
                        name="comment[author]"
                        value="{{ form.author }}"
                      >
                      <label for="comment-name">{{ 'blogs.article.comment_form_name' | t }}</label>
                    </div>

                    <div class="email form-floating">
                      <input
                        id="comment-email"
                        required
                        placeholder="{{ 'blogs.article.comment_form_email' | t }}"
                        class="input"
                        type="email"
                        name="comment[email]"
                        value="{{ form.email }}"
                      >
                      <label for="comment-email">{{ 'blogs.article.comment_form_email' | t }}</label>
                    </div>

                    <div class="comment form-floating sm:col-span-2">
                      <textarea
                        id="comment-body"
                        required
                        rows="6"
                        placeholder="{{ 'blogs.article.comment_form_message' | t }}"
                        class="input leading-normal"
                        name="comment[body]"
                      >{{ form.body }}</textarea>
                      <label for="comment-body">{{ 'blogs.article.comment_form_message' | t }}</label>
                    </div>
                  </div>

                  <div class="submit mt-6">
                    <button class="button button-primary" type="submit">
                      {{ 'blogs.article.submit_comment' | t }}
                    </button>
                  </div>
                {% endform %}
              </div>
            </div>
          </div>
        {% endif %}
    {%- endcase -%}
  {%- endfor -%}
</article>

<script type="application/ld+json">
  {{ article | structured_data }}
</script>

{% schema %}
{
  "name": "t:sections.main-article.name",
  "class": "section-main-article",
  "settings": [
    {
      "type": "select",
      "id": "content_width",
      "label": "t:sections.main-article.settings.content_width.label",
      "options": [
        {
          "value": "640",
          "label": "t:sections.main-article.settings.content_width.options__0.label"
        },
        {
          "value": "768",
          "label": "t:sections.main-article.settings.content_width.options__1.label"
        },
        {
          "value": "896",
          "label": "t:sections.main-article.settings.content_width.options__2.label"
        },
        {
          "value": "1024",
          "label": "t:sections.main-article.settings.content_width.options__3.label"
        }
      ],
      "default": "768"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "id": "article_title_color",
      "label": "t:sections.main-article.settings.article_title_color.label"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "featured_image",
      "name": "t:sections.main-article.blocks.featured_image.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "t:sections.main-article.blocks.featured_image.settings.full_width.label",
          "default": false
        },
        {
          "type": "select",
          "id": "image_size",
          "label": "t:sections.all.image_size.label",
          "options": [
            {
              "value": "media--ratio-4-3",
              "label": "t:sections.all.image_size.options.landscape_4_3"
            },
            {
              "value": "media--ratio-16-9",
              "label": "t:sections.all.image_size.options.landscape_wide_16_9"
            },
            {
              "value": "media--ratio-21-9",
              "label": "t:sections.all.image_size.options.landscape_wide_21_9"
            },
            {
              "value": "",
              "label": "t:sections.all.image_size.options.original_image_size"
            }
          ],
          "default": ""
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-article.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_tag",
          "label": "t:sections.main-article.blocks.title.settings.show_tag.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_date",
          "label": "t:sections.main-article.blocks.title.settings.show_date.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_author",
          "label": "t:sections.main-article.blocks.title.settings.show_author.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_comment_count",
          "label": "t:sections.main-article.blocks.title.settings.show_comment_count.label",
          "default": true
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:sections.all.heading_size.label",
          "options": [
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__0.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__6.label"
            }
          ],
          "default": "h1"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "t:sections.all.text_alignment.label",
          "options": [
            {
              "value": "text-left",
              "label": "t:sections.all.text_alignment.options.text_left.label"
            },
            {
              "value": "text-center",
              "label": "t:sections.all.text_alignment.options.text_center.label"
            },
            {
              "value": "text-right",
              "label": "t:sections.all.text_alignment.options.text_right.label"
            }
          ],
          "default": "text-left"
        }
      ]
    },
    {
      "type": "content",
      "name": "t:sections.main-article.blocks.content.name",
      "limit": 1
    },
    {
      "type": "share-button",
      "name": "t:sections.main-article.blocks.share_button.name",
      "limit": 1
    },
    {
      "type": "previous-next-articles",
      "name": "t:sections.main-article.blocks.previous_next_articles.name",
      "limit": 1
    },
    {
      "type": "comments",
      "name": "t:sections.main-article.blocks.comments.name",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "per_page",
          "min": 3,
          "max": 50,
          "step": 1,
          "label": "t:sections.main-article.blocks.comments.settings.per_page.label",
          "default": 5
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-article.paragraph__0.content"
        }
      ]
    }
  ]
}
{% endschema %}
