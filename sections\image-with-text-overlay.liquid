{% liquid
  capture image_sizes
    render 'image-sizes-full-width', full_width: section.settings.full_width
  endcapture

  if section.settings.image_mobile
    assign image_class = 'max-md:hidden'
  endif
%}

{% capture media %}
  {% if section.settings.image %}
      {{
        section.settings.image
        | image_url: width: 3840
        | image_tag:
          widths: '512, 768, 1024, 1280, 1536, 1792, 2048, 2560, 3072, 3840',
          sizes: image_sizes,
          class: image_class
      }}
  {% else %}
    {% render 'placeholder', type: 'lifestyle-1', class: 'placeholder--dark' %}
  {% endif %}
{% endcapture %}

{% if section.settings.image_mobile %}
  {%- capture media_mobile -%}
    {{
      section.settings.image_mobile
      | image_url: width: 1536
      | image_tag:
        widths: '420, 840, 1072, 1304, 1536',
        sizes: image_sizes,
        class: 'md:hidden'
    }}
  {%- endcapture -%}
{% endif %}

{% render 'media-with-text-overlay', media: media, media_mobile: media_mobile %}

{% schema %}
{
  "name": "t:sections.image-with-text-overlay.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_transparent_header",
      "label": "t:sections.all.enable_transparent_header.label",
      "info": "t:sections.all.enable_transparent_header.info",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "use_original_media_size",
      "label": "t:sections.image-with-text-overlay.settings.use_original_media_size.label",
      "default": false,
      "info": "t:sections.image-with-text-overlay.settings.use_original_media_size.info"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text-overlay.settings.image.label"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "t:sections.image-with-text-overlay.settings.image_mobile.label",
      "info": "t:sections.image-with-text-overlay.settings.image_mobile.info"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "label": "t:sections.all.content_position.label",
      "options": [
        {
          "value": "md:content-top-left",
          "label": "t:sections.all.position.top_left.label"
        },
        {
          "value": "md:content-top-center",
          "label": "t:sections.all.position.top_center.label"
        },
        {
          "value": "md:content-top-right",
          "label": "t:sections.all.position.top_right.label"
        },
        {
          "value": "md:content-middle-left",
          "label": "t:sections.all.position.middle_left.label"
        },
        {
          "value": "md:content-middle-center",
          "label": "t:sections.all.position.middle_center.label"
        },
        {
          "value": "md:content-middle-right",
          "label": "t:sections.all.position.middle_right.label"
        },
        {
          "value": "md:content-bottom-left",
          "label": "t:sections.all.position.bottom_left.label"
        },
        {
          "value": "md:content-bottom-center",
          "label": "t:sections.all.position.bottom_center.label"
        },
        {
          "value": "md:content-bottom-right",
          "label": "t:sections.all.position.bottom_right.label"
        }
      ],
      "default": "md:content-middle-center"
    },
    {
      "type": "select",
      "id": "content_alignment_mobile",
      "label": "t:sections.all.content_position_mobile.label",
      "options": [
        {
          "value": "content-top-left",
          "label": "t:sections.all.position.top_left.label"
        },
        {
          "value": "content-top-center",
          "label": "t:sections.all.position.top_center.label"
        },
        {
          "value": "content-top-right",
          "label": "t:sections.all.position.top_right.label"
        },
        {
          "value": "content-middle-left",
          "label": "t:sections.all.position.middle_left.label"
        },
        {
          "value": "content-middle-center",
          "label": "t:sections.all.position.middle_center.label"
        },
        {
          "value": "content-middle-right",
          "label": "t:sections.all.position.middle_right.label"
        },
        {
          "value": "content-bottom-left",
          "label": "t:sections.all.position.bottom_left.label"
        },
        {
          "value": "content-bottom-center",
          "label": "t:sections.all.position.bottom_center.label"
        },
        {
          "value": "content-bottom-right",
          "label": "t:sections.all.position.bottom_right.label"
        }
      ],
      "default": "content-middle-center"
    },
    {
      "type": "url",
      "id": "link",
      "label": "t:sections.all.link.label",
      "info": "t:sections.image-with-text-overlay.settings.link.info"
    },
    {
      "type": "range",
      "id": "min_height",
      "min": 0,
      "max": 1000,
      "step": 20,
      "unit": "px",
      "label": "t:sections.image-with-text-overlay.settings.min_height.label",
      "default": 500
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "label": "t:sections.all.overlay_color.label",
      "id": "overlay_color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "t:sections.all.overlay_opacity.label",
      "default": 35
    },
    {
      "type": "select",
      "id": "overlay_type",
      "label": "t:sections.all.overlay_type.label",
      "options": [
        {
          "value": "solid",
          "label": "t:sections.all.overlay_type.options__0.label"
        },
        {
          "value": "gradient",
          "label": "t:sections.all.overlay_type.options__1.label"
        }
      ],
      "default": "solid"
    }
  ],
  "blocks": [
    {
      "type": "subheading",
      "name": "t:sections.image-with-text-overlay.blocks.subheading.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.image-with-text-overlay.blocks.subheading.settings.text.label",
          "default": "Subheading"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.image-with-text-overlay.blocks.heading.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Talk about your brand",
          "label": "t:sections.all.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:sections.all.heading_size.label",
          "options": [
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__0.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__6.label"
            }
          ],
          "default": "h1"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.seo"
        },
        {
          "type": "select",
          "id": "heading_html_tag",
          "label": "t:sections.all.heading_html_tag.label",
          "options": [
            {
              "value": "h1",
              "label": "h1"
            },
            {
              "value": "h2",
              "label": "h2"
            },
            {
              "value": "h3",
              "label": "h3"
            },
            {
              "value": "h4",
              "label": "h4"
            },
            {
              "value": "h5",
              "label": "h5"
            },
            {
              "value": "h6",
              "label": "h6"
            },
            {
              "value": "p",
              "label": "p"
            },
            {
              "value": "span",
              "label": "span"
            },
            {
              "value": "div",
              "label": "div"
            }
          ],
          "default": "p",
          "info": "t:sections.all.heading_html_tag.info"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text-overlay.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>",
          "label": "t:sections.image-with-text-overlay.blocks.text.settings.text.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text-overlay.blocks.button.name",
      "settings": [
        {
          "type": "text",
          "id": "button_text",
          "default": "Button",
          "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_text.label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_size.label",
          "options": [
            {
              "value": "text-xs md:text-sm",
              "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_size.options__0.label"
            },
            {
              "value": "text-sm md:text-base",
              "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_size.options__1.label"
            },
            {
              "value": "text-base md:text-lg",
              "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_size.options__2.label"
            },
            {
              "value": "text-lg md:text-h5",
              "label": "t:sections.image-with-text-overlay.blocks.button.settings.button_size.options__3.label"
            }
          ],
          "default": "text-sm md:text-base"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "label": "t:sections.all.background_color.label",
          "id": "button_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.all.text_color.label",
          "id": "button_text_color"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.image-with-text-overlay.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "icon",
      "name": "t:sections.image-with-text-overlay.blocks.icon.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "t:sections.all.custom_icon.label"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "t:sections.all.icon.label",
          "options": [
            {
              "group": "t:sections.all.icon.options__0.group",
              "value": "message-chat-circle",
              "label": "t:sections.all.icon.options__0.label"
            },
            {
              "group": "t:sections.all.icon.options__1.group",
              "value": "at-sign",
              "label": "t:sections.all.icon.options__1.label"
            },
            {
              "group": "t:sections.all.icon.options__2.group",
              "value": "headphones",
              "label": "t:sections.all.icon.options__2.label"
            },
            {
              "group": "t:sections.all.icon.options__3.group",
              "value": "mail",
              "label": "t:sections.all.icon.options__3.label"
            },
            {
              "group": "t:sections.all.icon.options__4.group",
              "value": "phone",
              "label": "t:sections.all.icon.options__4.label"
            },
            {
              "group": "t:sections.all.icon.options__5.group",
              "value": "message-text-square",
              "label": "t:sections.all.icon.options__5.label"
            },
            {
              "group": "t:sections.all.icon.options__6.group",
              "value": "voicemail",
              "label": "t:sections.all.icon.options__6.label"
            },
            {
              "group": "t:sections.all.icon.options__7.group",
              "value": "bank-note",
              "label": "t:sections.all.icon.options__7.label"
            },
            {
              "group": "t:sections.all.icon.options__8.group",
              "value": "credit-card",
              "label": "t:sections.all.icon.options__8.label"
            },
            {
              "group": "t:sections.all.icon.options__9.group",
              "value": "credit-card-check",
              "label": "t:sections.all.icon.options__9.label"
            },
            {
              "group": "t:sections.all.icon.options__10.group",
              "value": "credit-card-shield",
              "label": "t:sections.all.icon.options__10.label"
            },
            {
              "group": "t:sections.all.icon.options__11.group",
              "value": "shield",
              "label": "t:sections.all.icon.options__11.label"
            },
            {
              "group": "t:sections.all.icon.options__12.group",
              "value": "percent",
              "label": "t:sections.all.icon.options__12.label"
            },
            {
              "group": "t:sections.all.icon.options__13.group",
              "value": "tag",
              "label": "t:sections.all.icon.options__13.label"
            },
            {
              "group": "t:sections.all.icon.options__14.group",
              "value": "shopping-cart",
              "label": "t:sections.all.icon.options__14.label"
            },
            {
              "group": "t:sections.all.icon.options__15.group",
              "value": "plane",
              "label": "t:sections.all.icon.options__15.label"
            },
            {
              "group": "t:sections.all.icon.options__16.group",
              "value": "package-check",
              "label": "t:sections.all.icon.options__16.label"
            },
            {
              "group": "t:sections.all.icon.options__17.group",
              "value": "package",
              "label": "t:sections.all.icon.options__17.label"
            },
            {
              "group": "t:sections.all.icon.options__18.group",
              "value": "return",
              "label": "t:sections.all.icon.options__18.label"
            },
            {
              "group": "t:sections.all.icon.options__19.group",
              "value": "truck",
              "label": "t:sections.all.icon.options__19.label"
            },
            {
              "group": "t:sections.all.icon.options__20.group",
              "value": "check-heart",
              "label": "t:sections.all.icon.options__20.label"
            },
            {
              "group": "t:sections.all.icon.options__21.group",
              "value": "check",
              "label": "t:sections.all.icon.options__21.label"
            },
            {
              "group": "t:sections.all.icon.options__22.group",
              "value": "star",
              "label": "t:sections.all.icon.options__22.label"
            },
            {
              "group": "t:sections.all.icon.options__23.group",
              "value": "thumbs-up",
              "label": "t:sections.all.icon.options__23.label"
            },
            {
              "group": "t:sections.all.icon.options__24.group",
              "value": "check-verified",
              "label": "t:sections.all.icon.options__24.label"
            },
            {
              "group": "t:sections.all.icon.options__25.group",
              "value": "building",
              "label": "t:sections.all.icon.options__25.label"
            },
            {
              "group": "t:sections.all.icon.options__26.group",
              "value": "file",
              "label": "t:sections.all.icon.options__26.label"
            },
            {
              "group": "t:sections.all.icon.options__27.group",
              "value": "gift",
              "label": "t:sections.all.icon.options__27.label"
            },
            {
              "group": "t:sections.all.icon.options__28.group",
              "value": "heart",
              "label": "t:sections.all.icon.options__28.label"
            },
            {
              "group": "t:sections.all.icon.options__29.group",
              "value": "heart-hand",
              "label": "t:sections.all.icon.options__29.label"
            },
            {
              "group": "t:sections.all.icon.options__30.group",
              "value": "marker-pin",
              "label": "t:sections.all.icon.options__30.label"
            },
            {
              "group": "t:sections.all.icon.options__31.group",
              "value": "face-smile",
              "label": "t:sections.all.icon.options__31.label"
            },
            {
              "group": "t:sections.all.icon.options__32.group",
              "value": "store",
              "label": "t:sections.all.icon.options__32.label"
            },
            {
              "group": "t:sections.all.icon.options__33.group",
              "value": "target",
              "label": "t:sections.all.icon.options__33.label"
            },
            {
              "group": "t:sections.all.icon.options__34.group",
              "value": "user",
              "label": "t:sections.all.icon.options__34.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "icon_background",
          "label": "t:sections.all.icon_background.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.icon_background.options__0.label"
            },
            {
              "value": "square",
              "label": "t:sections.all.icon_background.options__1.label"
            },
            {
              "value": "circle",
              "label": "t:sections.all.icon_background.options__2.label"
            }
          ],
          "default": ""
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 24,
          "max": 96,
          "step": 8,
          "unit": "px",
          "label": "t:sections.all.icon_width.label",
          "default": 48
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "id": "icon_background_color",
          "label": "t:sections.all.icon_background_color.label"
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "t:sections.all.icon_color.label"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.image-with-text-overlay.presets__0.name",
      "blocks": [
        {
          "type": "subheading"
        },
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
