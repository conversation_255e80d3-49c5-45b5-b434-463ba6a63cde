{"sections": {"slideshow_kRWa6w": {"type": "slideshow", "blocks": {"slide_gyXRMm": {"type": "slide", "settings": {"video": "shopify://files/videos/DevDev Wide_fbc6ea19-bd77-4a76-94db-365ee54c51d4.mp4", "content_width_maximum": 960, "desktop_content_position": "middle-center", "mobile_content_position": "middle-center", "desktop_text_alignment": "default", "subheading": "Develop Device Studio", "heading": "Album-Ready Sound. Quick & Easy!", "heading_size": "h0", "text": "", "hide_text_on_mobile": false, "button_size": "text-sm md:text-base", "button_link": "shopify://collections/all", "button_text": "Browse All Products", "button_style": "button-outline", "button_background_color": "#ffffff", "button_text_color": "", "button2_link": "shopify://products/all-access-pass", "button2_text": "All-Access Pass", "button2_style": "", "button2_background_color": "#551bda", "button2_text_color": "#ffffff", "background_color": "", "color_text": "#ffffff", "overlay_color": "#000000", "overlay_opacity": 30, "overlay_type": "solid", "heading_html_tag": "p"}}}, "block_order": ["slide_gyXRMm"], "name": "t:sections.slideshow.presets__0.name", "settings": {"full_width": false, "enable_transparent_header": true, "use_original_media_size": false, "autoplay": false, "speed": 5, "height": 560, "controls": "arrows", "background_animation": "ZoomOut", "content_animation": "FadeIn", "background_color": "#1d1d1d"}}, "slideshow_jckh8w": {"type": "slideshow", "blocks": {"slide_9XjjPy": {"type": "slide", "settings": {"video": "shopify://files/videos/Generated File April 22, 2025 - 11_59AM.mp4", "content_width_maximum": 640, "desktop_content_position": "middle-center", "mobile_content_position": "middle-center", "desktop_text_alignment": "default", "subheading": "Develop Device Studio", "heading": "Flash Sale!", "heading_size": "h0", "text": "50% Off Storewide", "hide_text_on_mobile": false, "button_size": "text-sm md:text-base", "button_link": "shopify://collections/all", "button_text": "Shop now", "button_style": "", "button_background_color": "", "button_text_color": "", "button2_link": "", "button2_text": "", "button2_style": "", "button2_background_color": "", "button2_text_color": "", "background_color": "#1d1d1d", "color_text": "#ffffff", "overlay_color": "#000000", "overlay_opacity": 30, "overlay_type": "solid", "heading_html_tag": "p"}}}, "block_order": ["slide_9XjjPy"], "name": "t:sections.slideshow.presets__0.name", "settings": {"full_width": false, "enable_transparent_header": false, "use_original_media_size": false, "autoplay": true, "speed": 5, "height": 560, "controls": "arrows", "background_animation": "ZoomOut", "content_animation": "AppearLineByLine", "background_color": "#262626"}}, "featured_collection_gMtybX": {"type": "featured-collection", "name": "t:sections.featured-collection.name", "settings": {"full_width": true, "carousel": false, "products_count": 4, "grid_columns": 4, "grid_columns_mobile": "2", "products": [], "collection": "apps", "subheading": "", "heading": "{{ section.settings.collection.title }}", "heading_size": "h2", "text_alignment": "text-center", "link_text": "View all", "link_url": "", "background_color": "", "text_color": "", "heading_color": "", "heading_html_tag": "p"}}, "collection_list_8LTkkE": {"type": "collection-list", "blocks": {"collection_xaMUjh": {"type": "collection", "settings": {"collection": "superior-drummer-3-presets", "title": ""}}, "collection_NaBHi9": {"type": "collection", "settings": {"collection": "artist-presets-for-superior-drummer-3", "title": ""}}, "collection_tKNUM7": {"type": "collection", "settings": {"collection": "ezdrummer-3-templates", "title": ""}}, "collection_LtxkxV": {"type": "collection", "settings": {"collection": "ggd-drum-templates", "title": ""}}, "collection_tKxXXf": {"type": "collection", "settings": {"collection": "perfect-drums-templates", "title": ""}}, "collection_fV4LVw": {"type": "collection", "settings": {"collection": "fractal-axe-fx-iii-presets", "title": ""}}, "collection_HEb7Qh": {"type": "collection", "settings": {"collection": "fractal-fm3-presets", "title": ""}}, "collection_Hw637N": {"type": "collection", "settings": {"collection": "fractal-axe-fx-ii-presets", "title": ""}}, "collection_XKai3K": {"type": "collection", "settings": {"collection": "neural-dsp-quad-cortex-captures", "title": "Neural DSP Quad Cortex Captures"}}, "collection_Pxt76t": {"type": "collection", "settings": {"collection": "kemper-profiles", "title": ""}}, "collection_djXUpE": {"type": "collection", "settings": {"collection": "line-6-helix-presets", "title": ""}}, "collection_PPBNLw": {"type": "collection", "settings": {"collection": "line-6-pod-go", "title": ""}}, "collection_EMeExm": {"type": "collection", "settings": {"collection": "ik-multimedia-tonex-captures", "title": ""}}, "collection_RkaF6P": {"type": "collection", "settings": {"collection": "cabinet-irs", "title": ""}}, "collection_PMHzgM": {"type": "collection", "settings": {"collection": "daw-templates", "title": ""}}}, "block_order": ["collection_xaMUjh", "collection_NaBHi9", "collection_tKNUM7", "collection_LtxkxV", "collection_tKxXXf", "collection_fV4LVw", "collection_HEb7Qh", "collection_Hw637N", "collection_XKai3K", "collection_Pxt76t", "collection_djXUpE", "collection_PPBNLw", "collection_EMeExm", "collection_RkaF6P", "collection_PMHzgM"], "name": "t:sections.collection-list.name", "settings": {"full_width": true, "carousel": false, "grid_columns": 5, "grid_columns_mobile": "1", "style": "overlay", "title_size": "text-h4/h4", "content_alignment": "content-bottom-left", "image_size": "media--ratio-1-1", "subheading": "", "heading": "", "content": "", "heading_size": "h2", "text_alignment": "text-center", "background_color": "", "text_color": "", "heading_color": "", "collection_title_color": "", "overlay": "#000000", "overlay_opacity": 50, "overlay_type": "gradient", "heading_html_tag": "p"}}, "custom_liquid_ULHaVd": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "<!-- Product Feature Section -->\n<section class=\"sd3-product-feature\">\n  <div class=\"sd3-container\">\n    <div class=\"sd3-feature-content\">\n      <div class=\"sd3-video-wrapper\">\n        <div class=\"sd3-video-grid\">\n          <div class=\"sd3-video-item\">\n            <div class=\"sd3-video-container\">\n              <iframe \n                width=\"560\" \n                height=\"315\" \n                src=\"https://www.youtube.com/embed/5byGi_jT6jI\" \n                title=\"Superior Drummer 3 Presets Showcase\" \n                frameborder=\"0\" \n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" \n                allowfullscreen>\n              </iframe>\n            </div>\n          </div>\n          <div class=\"sd3-video-item\">\n            <div class=\"sd3-video-container\">\n              <iframe \n                width=\"560\" \n                height=\"315\" \n                src=\"https://www.youtube.com/embed/gAytp0LOPm4\" \n                title=\"Superior Drummer 3 Core Library Presets\" \n                frameborder=\"0\" \n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" \n                allowfullscreen>\n              </iframe>\n            </div>\n          </div>\n          <div class=\"sd3-video-item\">\n            <div class=\"sd3-video-container\">\n              <iframe \n                width=\"560\" \n                height=\"315\" \n                src=\"https://www.youtube.com/embed/i__1jqunMEc\" \n                title=\"Superior Drummer 3 Presets Demo\" \n                frameborder=\"0\" \n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" \n                allowfullscreen>\n              </iframe>\n            </div>\n          </div>\n          <div class=\"sd3-video-item\">\n            <div class=\"sd3-video-container\">\n              <iframe \n                width=\"560\" \n                height=\"315\" \n                src=\"https://www.youtube.com/embed/EmYrWb6_0Gs\" \n                title=\"Superior Drummer 3 Metal Presets Showcase\" \n                frameborder=\"0\" \n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" \n                allowfullscreen>\n              </iframe>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"sd3-product-info\">\n        <div class=\"sd3-product-header\">\n          <h2>Essential Core Collection for Superior Drummer 3</h2>\n          <h3>Complete Collection of 45 Professional Presets for CORE Library</h3>\n        </div>\n        <div class=\"sd3-product-features\">\n          <ul>\n            <li><span class=\"sd3-feature-highlight\">45 Premium Presets</span> for Superior Drummer 3</li>\n            <li>Designed specifically for the CORE Library</li>\n            <li>Studio-quality drum tones ready for your productions</li>\n            <li>Multiple genres including rock, metal, pop, and more</li>\n            <li>Instant download after purchase</li>\n          </ul>\n        </div>\n        <p class=\"sd3-product-description\">\n          Elevate your music productions with this comprehensive collection of 45 professional drum presets. \n          Designed exclusively for the Superior Drummer 3 CORE Library, these presets deliver \n          versatile, studio-quality drum tones suitable for a wide range of musical styles.\n        </p>\n        <div class=\"sd3-cta-container\">\n          <a href=\"https://developdevice.com/products/essential-core-collection-for-superior-drummer-3\" class=\"sd3-cta-button\">Get Your Presets Now</a>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<style>\n  .sd3-product-feature {\n    background-color: #0f0f0f;\n    color: #ffffff;\n    padding: 20px;\n    width: 100%;\n    box-sizing: border-box;\n    height: 100%;\n    position: relative;\n    /* Nové: Zaoblení sekce na 24px */\n    border-radius: 24px; \n    overflow: hidden; /* Zajistí oříznutí pozadí a obsahu */\n  }\n  \n  .sd3-product-feature::before {\n    content: \"\";\n    background-image: url('https://cdn.shopify.com/s/files/1/2599/2974/files/cb3d1b2468D8k7qZfsDJBpzvcEWI5AcftisqnqNu.webp?v=1743846178');\n    background-size: cover;\n    background-position: center;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    opacity: 0.2;\n    z-index: 1;\n  }\n  \n  .sd3-container {\n    max-width: 1800px;\n    margin: 0 auto;\n    padding: 0;\n    width: 100%;\n    box-sizing: border-box;\n    height: 100%;\n    position: relative;\n    z-index: 2;\n  }\n  \n  .sd3-feature-content {\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 48px;\n    width: 100%;\n    height: 100%;\n    position: relative;\n    z-index: 2;\n  }\n  \n  .sd3-video-wrapper {\n    background-color: #161616;\n    padding: 0;\n    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n    box-sizing: border-box;\n    overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n    position: relative;\n    z-index: 2;\n    /* Nové: Zaoblení obalu videa na 24px */\n    border-radius: 24px; \n  }\n  \n  .sd3-video-grid {\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 15px;\n    width: 100%;\n    height: 100%;\n    padding: 15px;\n    box-sizing: border-box;\n  }\n  \n  .sd3-video-item {\n    position: relative;\n    width: 100%;\n    height: 100%;\n    background-color: #111;\n    overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n    /* Změna: Zaoblení jednotlivých video položek na 24px */\n    border-radius: 24px; \n  }\n  \n  .sd3-video-container {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n  \n  .sd3-video-container iframe {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border: none;\n  }\n  \n  .sd3-product-info {\n    padding: 20px;\n    background-color: rgba(22, 22, 22, 0.5);\n    backdrop-filter: blur(15px);\n    -webkit-backdrop-filter: blur(15px);\n    border: 1px solid rgba(255, 255, 255, 0.15);\n    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), \n                inset 0 0 20px rgba(255, 255, 255, 0.05);\n    display: flex;\n    flex-direction: column;\n    gap: 16px;\n    position: relative;\n    z-index: 2;\n    overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n    /* Nové: Zaoblení informační sekce na 24px */\n    border-radius: 24px; \n  }\n  \n  .sd3-product-info::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, \n                 rgba(255, 255, 255, 0.01), \n                 rgba(255, 255, 255, 0.2), \n                 rgba(255, 255, 255, 0.01));\n    z-index: 3;\n  }\n  \n  .sd3-product-info::after {\n    content: \"\";\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 1px;\n    height: 70%;\n    background: linear-gradient(0deg, \n                 rgba(255, 255, 255, 0.01), \n                 rgba(255, 255, 255, 0.1));\n    z-index: 3;\n  }\n  \n  .sd3-product-header {\n    border-left: 4px solid rgba(232, 232, 225, 0.8);\n    padding-left: 16px;\n  }\n  \n  .sd3-product-info h2 {\n    font-size: 22px;\n    font-weight: 700;\n    margin-bottom: 6px;\n    color: rgba(255, 255, 255, 0.95);\n    line-height: 1.2;\n  }\n  \n  .sd3-product-info h3 {\n    font-size: 16px;\n    font-weight: 400;\n    color: rgba(232, 232, 225, 0.9);\n    line-height: 1.4;\n  }\n  \n  .sd3-product-features ul {\n    list-style-type: none;\n    padding: 0;\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n  \n  .sd3-product-features li {\n    position: relative;\n    padding-left: 28px;\n    font-size: 16px;\n    line-height: 1.5;\n    color: rgba(255, 255, 255, 0.85);\n  }\n  \n  .sd3-product-features li:before {\n    content: \"→\";\n    position: absolute;\n    left: 0;\n    color: rgba(232, 232, 225, 0.9);\n    font-weight: bold;\n  }\n  \n  .sd3-feature-highlight {\n    font-weight: 600;\n    color: rgba(232, 232, 225, 0.95);\n  }\n  \n  .sd3-product-description {\n    line-height: 1.6;\n    font-size: 16px;\n    color: rgba(255, 255, 255, 0.85);\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    position: relative;\n    background-color: rgba(255, 255, 255, 0.05);\n    padding: 20px;\n    border-left: 1px solid rgba(255, 255, 255, 0.1);\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(5px);\n    -webkit-backdrop-filter: blur(5px);\n    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.2);\n    /* Nové: Zaoblení popisu na 24px */\n    border-radius: 24px; \n    overflow: hidden; /* Pro jistotu, pokud by byl nějaký vnitřní obsah */\n  }\n  \n  .sd3-cta-container {\n    margin-top: 8px;\n  }\n  \n  .sd3-cta-button {\n    display: inline-block;\n    background-color: rgba(232, 232, 225, 0.9);\n    color: #0f0f0f;\n    font-weight: 600;\n    padding: 16px 32px;\n    text-decoration: none;\n    text-transform: uppercase;\n    letter-spacing: 1px;\n    transition: all 0.25s ease;\n    border: 2px solid transparent;\n    backdrop-filter: blur(5px);\n    -webkit-backdrop-filter: blur(5px);\n    /* Změna: Zaoblení tlačítka na 12px */\n    border-radius: 12px; \n  }\n  \n  .sd3-cta-button:hover {\n    background-color: transparent;\n    color: rgba(232, 232, 225, 1);\n    border: 2px solid rgba(232, 232, 225, 0.7);\n    transform: translateY(-2px);\n  }\n  \n  /* Small mobile devices */\n  @media (max-width: 479px) {\n    .sd3-product-feature {\n      padding: 15px;\n    }\n    \n    .sd3-video-grid {\n      gap: 10px;\n      padding: 10px;\n    }\n    \n    .sd3-product-info {\n      padding: 15px;\n    }\n    \n    .sd3-product-info h2 {\n      font-size: 20px;\n    }\n    \n    .sd3-product-info h3 {\n      font-size: 14px;\n    }\n    \n    .sd3-product-features li {\n      font-size: 14px;\n    }\n    \n    .sd3-product-description {\n      font-size: 14px;\n      padding: 15px;\n    }\n    \n    .sd3-cta-button {\n      padding: 12px 24px;\n      font-size: 14px;\n    }\n  }\n  \n  /* Medium mobile to tablet */\n  @media (min-width: 480px) and (max-width: 767px) {\n    .sd3-product-feature {\n      padding: 30px;\n    }\n    \n    .sd3-video-grid {\n      grid-template-columns: 1fr;\n      gap: 15px;\n    }\n  }\n  \n  /* Tablet devices */\n  @media (min-width: 768px) {\n    .sd3-product-features ul {\n      grid-template-columns: repeat(2, 1fr);\n    }\n    \n    .sd3-product-feature {\n      padding: 40px;\n    }\n    \n    .sd3-video-grid {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n  \n  /* Desktop devices */\n  @media (min-width: 992px) {\n    .sd3-feature-content {\n      /* Updated: Changed ratio to make videos larger (2:1) */\n      grid-template-columns: 2fr 1fr;\n      align-items: stretch;\n      height: 100%;\n    }\n    \n    .sd3-video-wrapper, .sd3-product-info {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .sd3-video-container {\n      flex: 1;\n      padding-bottom: 0;\n    }\n    \n    .sd3-product-feature {\n      padding: 60px;\n    }\n  }\n  \n  /* Large desktop devices */\n  @media (min-width: 1200px) {\n    .sd3-product-feature {\n      padding: 80px;\n    }\n  }\n</style>", "background_color": "", "text_color": ""}}, "custom_liquid_wxcHWa": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "<style>\n    :root {\n      /* Static white background, no gradient needed */\n      --gradient-opacity: 0;\n    }\n\n    .testimonials-section {\n      position: relative;\n      padding: 100px 0;\n      margin: 0;\n      width: 100%;\n      max-width: 100%;\n      /* Změna: Zaoblení sekce na 24px */\n      border-radius: 24px; \n      overflow: hidden;\n      /* Inverted to white background */\n      background-color: #f4f4f4;\n    }\n\n    /* Remove noise effect and gradient overlay */\n    \n    /* Ensure content is above background */\n    .section-header,\n    .testimonials-grid {\n      position: relative;\n      z-index: 1;\n    }\n\n    /* Remove particle styles */\n\n    /* --- Rest of styles (Inverted) --- */\n     .section-header {\n      text-align: center;\n      max-width: 1800px;\n      margin: 0 auto 80px;\n      padding: 0 20px;\n    }\n    .section-title { color: #000000; font-size: 42px; font-weight: 700; margin-bottom: 20px; line-height: 1.2; }\n    .section-description { color: #000000; font-size: 20px; line-height: 1.6; margin: 0 auto; max-width: 650px; }\n    .testimonials-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 40px; max-width: 100%; margin: 0 auto; padding: 0 40px; }\n    .testimonial-card { \n      background: rgba(255,255,255,0.90); \n      backdrop-filter: blur(10px); \n      -webkit-backdrop-filter: blur(10px); \n      /* Změna: Zaoblení karty recenze na 24px */\n      border-radius: 24px; \n      padding: 0; \n      box-shadow: 0 15px 40px rgba(0,0,0,0.1); \n      overflow: hidden; \n      transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.4s ease, border-color 0.4s ease; \n      position: relative; \n      border: 1px solid rgba(0, 0, 0, 0.08); \n    }\n    .testimonial-card:hover { transform: translateY(-10px); box-shadow: 0 20px 50px rgba(0,0,0,0.15); border-color: rgba(0, 0, 0, 0.15); }\n    .testimonial-photo-wrapper { width: 100%; overflow: hidden; position: relative; }\n    .testimonial-photo { display: block; width: 100%; height: auto; object-fit: cover; transition: transform 0.5s ease; background: #ffffff; }\n    .testimonial-card:hover .testimonial-photo { transform: scale(1.05); }\n    .testimonial-content { padding: 30px; }\n    .artist-info { margin-bottom: 20px; }\n    .artist-name { font-size: 24px; color: #000000; margin: 0 0 5px; font-weight: 700; }\n    .artist-profession { color: #000000; font-size: 16px; margin: 0; font-weight: 600; }\n    .testimonial-text { color: #000000; font-size: 16px; line-height: 1.8; margin: 0; }\n    .artist-link { text-decoration: none; color: inherit; transition: color 0.2s ease; }\n    .artist-link:hover { color: #000000; }\n\n    @media (max-width: 1280px) { .testimonials-grid { grid-template-columns: repeat(2, 1fr); padding: 0 20px; } }\n    /* Carousel styles for mobile */\n    .carousel-container { display: none; }\n    .carousel-slide { display: none; }\n    .carousel-controls { display: none; }\n    \n    @media (max-width: 768px) {\n      .testimonials-section { padding: 60px 15px; }\n      .section-title { font-size: 32px; }\n      .section-description { font-size: 18px; }\n      \n      /* Hide grid on mobile */\n      .testimonials-grid { display: none; }\n      \n      /* Show carousel on mobile */\n      .carousel-container { \n        display: block; \n        position: relative;\n        width: 100%;\n        overflow: hidden;\n      }\n      \n      .carousel-track {\n        display: flex;\n        transition: transform 0.5s ease;\n      }\n      \n      .carousel-slide {\n        display: block;\n        flex: 0 0 100%;\n        width: 100%;\n      }\n      \n      .carousel-controls {\n        display: flex;\n        justify-content: center;\n        margin-top: 20px;\n        gap: 10px;\n      }\n      \n      .carousel-button {\n        background: #000000;\n        color: #ffffff;\n        border: none;\n        width: 40px;\n        height: 40px;\n        /* Změna: Zaoblení tlačítka na 12px */\n        border-radius: 12px; \n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: background-color 0.3s ease;\n      }\n      \n      .carousel-button:hover {\n        background: #333333;\n      }\n      \n      .carousel-dots {\n        display: flex;\n        justify-content: center;\n        margin-top: 15px;\n        gap: 8px;\n      }\n      \n      .carousel-dot {\n        width: 10px;\n        height: 10px;\n        border-radius: 50%; /* Ponecháno jako kruhové */\n        background: #cccccc;\n        cursor: pointer;\n        transition: background-color 0.3s ease;\n      }\n      \n      .carousel-dot.active {\n        background: #000000;\n      }\n    }\n</style>\n\n<!-- HTML with particles removed -->\n<section class=\"testimonials-section\" id=\"testimonials-section\">\n  <div class=\"section-header\">\n    <h2 class=\"section-title\">What Artists Say About My Products</h2>\n    <p class=\"section-description\">Discover why leading musicians and industry professionals trust and recommend my products for their performances and recordings.</p>\n  </div>\n  <!-- Desktop Grid View -->\n  <div class=\"testimonials-grid\">\n    {% assign testimonials = shop.metaobjects.artist_testimonials %}\n    {% for testimonial in testimonials.values %}\n      <article class=\"testimonial-card\">\n         {% if testimonial.artist_photo.value != blank %}\n           <div class=\"testimonial-photo-wrapper\">\n             <img src=\"{{ testimonial.artist_photo.value | image_url }}\" alt=\"{{ testimonial.artist_name.value }}\" class=\"testimonial-photo\" loading=\"lazy\">\n           </div>\n         {% endif %}\n         <div class=\"testimonial-content\">\n           <div class=\"artist-info\">\n             {% if testimonial.artist_url.value != blank %}\n               <a href=\"{{ testimonial.artist_url.value }}\" target=\"_blank\" rel=\"noopener\" class=\"artist-link\">\n                 <h3 class=\"artist-name\">{{ testimonial.artist_name.value }}</h3>\n               </a>\n             {% else %}\n               <h3 class=\"artist-name\">{{ testimonial.artist_name.value }}</h3>\n             {% endif %}\n             <p class=\"artist-profession\">{{ testimonial.band_profession.value }}</p>\n           </div>\n           <p class=\"testimonial-text\">{{ testimonial.testimonial_text.value }}</p>\n         </div>\n       </article>\n    {% endfor %}\n  </div>\n  \n  <!-- Mobile Carousel View -->\n  <div class=\"carousel-container\">\n    <div class=\"carousel-track\" id=\"testimonial-carousel\">\n      {% for testimonial in testimonials.values %}\n        <div class=\"carousel-slide\">\n          <article class=\"testimonial-card\">\n            {% if testimonial.artist_photo.value != blank %}\n              <div class=\"testimonial-photo-wrapper\">\n                <img src=\"{{ testimonial.artist_photo.value | image_url }}\" alt=\"{{ testimonial.artist_name.value }}\" class=\"testimonial-photo\" loading=\"lazy\">\n              </div>\n            {% endif %}\n            <div class=\"testimonial-content\">\n              <div class=\"artist-info\">\n                {% if testimonial.artist_url.value != blank %}\n                  <a href=\"{{ testimonial.artist_url.value }}\" target=\"_blank\" rel=\"noopener\" class=\"artist-link\">\n                    <h3 class=\"artist-name\">{{ testimonial.artist_name.value }}</h3>\n                  </a>\n                {% else %}\n                  <h3 class=\"artist-name\">{{ testimonial.artist_name.value }}</h3>\n                {% endif %}\n                <p class=\"artist-profession\">{{ testimonial.band_profession.value }}</p>\n              </div>\n              <p class=\"testimonial-text\">{{ testimonial.testimonial_text.value }}</p>\n            </div>\n          </article>\n        </div>\n      {% endfor %}\n    </div>\n    \n    <div class=\"carousel-controls\">\n      <button class=\"carousel-button prev-button\" aria-label=\"Previous testimonial\"><</button>\n      <button class=\"carousel-button next-button\" aria-label=\"Next testimonial\">></button>\n    </div>\n    \n    <div class=\"carousel-dots\" id=\"carousel-dots\">\n      {% for testimonial in testimonials.values %}\n        <span class=\"carousel-dot{% if forloop.first %} active{% endif %}\" data-index=\"{{ forloop.index0 }}\"></span>\n      {% endfor %}\n    </div>\n  </div>\n</section>\n\n<!-- Testimonials Carousel JavaScript -->\n<script>\n  document.addEventListener('DOMContentLoaded', function() {\n    // Only initialize carousel on mobile devices\n    if (window.innerWidth <= 768) {\n      initCarousel();\n    }\n    \n    // Re-initialize on resize if crossing the mobile breakpoint\n    window.addEventListener('resize', function() {\n      if (window.innerWidth <= 768) {\n        initCarousel();\n      }\n    });\n    \n    function initCarousel() {\n      const track = document.getElementById('testimonial-carousel');\n      const slides = Array.from(track.getElementsByClassName('carousel-slide'));\n      const dots = Array.from(document.getElementsByClassName('carousel-dot'));\n      const prevButton = document.querySelector('.prev-button');\n      const nextButton = document.querySelector('.next-button');\n      \n      let currentIndex = 0;\n      // Get slide width dynamically\n      const slideWidth = slides[0].getBoundingClientRect().width;\n      \n      // Set initial position\n      updateCarousel();\n      \n      // Add event listeners for buttons\n      prevButton.addEventListener('click', () => {\n        currentIndex = (currentIndex > 0) ? currentIndex - 1 : slides.length - 1;\n        updateCarousel();\n      });\n      \n      nextButton.addEventListener('click', () => {\n        currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;\n        updateCarousel();\n      });\n      \n      // Add event listeners for dots\n      dots.forEach((dot, index) => {\n        dot.addEventListener('click', () => {\n          currentIndex = index;\n          updateCarousel();\n        });\n      });\n      \n      // Touch events for swiping\n      let touchStartX = 0;\n      let touchEndX = 0;\n      \n      track.addEventListener('touchstart', (e) => {\n        touchStartX = e.changedTouches[0].screenX;\n      }, { passive: true });\n      \n      track.addEventListener('touchend', (e) => {\n        touchEndX = e.changedTouches[0].screenX;\n        handleSwipe();\n      }, { passive: true });\n      \n      function handleSwipe() {\n        const swipeThreshold = 50; // Minimum distance to register as a swipe\n        \n        if (touchStartX - touchEndX > swipeThreshold) {\n          // Swipe left - go to next slide\n          currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;\n          updateCarousel();\n        } else if (touchEndX - touchStartX > swipeThreshold) {\n          // Swipe right - go to previous slide\n          currentIndex = (currentIndex > 0) ? currentIndex - 1 : slides.length - 1;\n          updateCarousel();\n        }\n      }\n      \n      // Auto-advance carousel every 5 seconds\n      let autoAdvanceInterval = setInterval(() => {\n        currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;\n        updateCarousel();\n      }, 5000);\n      \n      // Clear interval when user interacts with carousel\n      const carouselContainer = document.querySelector('.carousel-container');\n      carouselContainer.addEventListener('mouseenter', () => {\n        clearInterval(autoAdvanceInterval);\n      });\n      carouselContainer.addEventListener('mouseleave', () => {\n        // Restart auto-advance when mouse leaves\n        autoAdvanceInterval = setInterval(() => {\n          currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;\n          updateCarousel();\n        }, 5000);\n      });\n\n      // Clear interval on touch interaction as well\n      track.addEventListener('touchstart', () => clearInterval(autoAdvanceInterval), { passive: true });\n      track.addEventListener('touchend', () => {\n        // Restart auto-advance shortly after touch ends\n        autoAdvanceInterval = setInterval(() => {\n          currentIndex = (currentIndex < slides.length - 1) ? currentIndex + 1 : 0;\n          updateCarousel();\n        }, 5000);\n      }, { passive: true });\n      \n      function updateCarousel() {\n        // Update track position\n        track.style.transform = `translateX(-${currentIndex * slideWidth}px)`;\n        \n        // Update active dot\n        dots.forEach((dot, index) => {\n          dot.classList.toggle('active', index === currentIndex);\n        });\n      }\n    }\n  });\n</script>", "background_color": "rgba(0,0,0,0)", "text_color": ""}}, "custom_liquid_YVwXgc": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "<!-- Product Feature Section -->\n<section class=\"pf-main-section\">\n    <div class=\"pf-layout-container\">\n      <div class=\"pf-content-grid\">\n        <div class=\"pf-video-column\">\n          <div class=\"pf-video-items-grid\">\n            <div class=\"pf-video-item-cell\">\n              <div class=\"pf-video-embed-wrapper\">\n                <iframe\n                  class=\"pf-video-iframe\"\n                  width=\"560\"\n                  height=\"315\"\n                  src=\"https://www.youtube.com/embed/UkfeN81_4Rg\"\n                  title=\"Superior Drummer 3 Metal Presets Showcase\"\n                  frameborder=\"0\"\n                  allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                  allowfullscreen>\n                </iframe>\n              </div>\n            </div>\n            <div class=\"pf-video-item-cell\">\n              <div class=\"pf-video-embed-wrapper\">\n                <iframe\n                  class=\"pf-video-iframe\"\n                  width=\"560\"\n                  height=\"315\"\n                  src=\"https://www.youtube.com/embed/LtW9x7xS2MQ\"\n                  title=\"Superior Drummer 3 Video\"\n                  frameborder=\"0\"\n                  allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                  allowfullscreen>\n                </iframe>\n              </div>\n            </div>\n            <div class=\"pf-video-item-cell\">\n              <div class=\"pf-video-embed-wrapper\">\n                <iframe\n                  class=\"pf-video-iframe\"\n                  width=\"560\"\n                  height=\"315\"\n                  src=\"https://www.youtube.com/embed/iNq9EBM885k\"\n                  title=\"Superior Drummer 3 Video\"\n                  frameborder=\"0\"\n                  allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                  allowfullscreen>\n                </iframe>\n              </div>\n            </div>\n            <div class=\"pf-video-item-cell\">\n              <div class=\"pf-video-embed-wrapper\">\n                <iframe\n                  class=\"pf-video-iframe\"\n                  width=\"560\"\n                  height=\"315\"\n                  src=\"https://www.youtube.com/embed/E30QN9zmbf0\"\n                  title=\"Superior Drummer 3 Video\"\n                  frameborder=\"0\"\n                  allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                  allowfullscreen>\n                </iframe>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"pf-info-column\">\n          <div class=\"pf-info-header-block\">\n            <h2 class=\"pf-info-title\">Superior Drummer 3 Preset Bundle for Death & Darkness SDX</h2>\n            <h3 class=\"pf-info-subtitle\">Premium Collection of 25 Professional Metal Drum Presets</h3>\n          </div>\n          <div class=\"pf-info-features-section\">\n            <ul class=\"pf-info-features-list\">\n              <li class=\"pf-info-feature-item\"><span class=\"pf-info-feature-item-highlight\">25 Premium Presets</span> for Superior Drummer 3</li>\n              <li class=\"pf-info-feature-item\">Optimized for Death & Darkness SDX library</li>\n              <li class=\"pf-info-feature-item\">Studio-quality drum tones ready for your productions</li>\n              <li class=\"pf-info-feature-item\">Professionally crafted by a musician for musicians</li>\n              <li class=\"pf-info-feature-item\">Instant download after purchase</li>\n            </ul>\n          </div>\n          <p class=\"pf-info-description-text\">\n            Take your metal productions to the next level with these meticulously crafted drum presets.\n            Designed specifically for the Death & Darkness SDX expansion, these presets deliver\n            powerful, aggressive drum tones ready for your extreme metal projects.\n          </p>\n          <div class=\"pf-info-cta-wrapper\">\n            <a href=\"https://developdevice.com/products/superior-drummer-3-preset-bundle-for-death-darkness-sdx-vol-1\" class=\"pf-info-cta-link\">Get Your Presets Now</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </section>\n\n  <style>\n    .pf-main-section {\n      background-color: #0f0f0f;\n      color: #ffffff;\n      padding: 30px 20px;\n      width: 100%;\n      box-sizing: border-box;\n      height: 100%;\n      position: relative;\n      /* Nové: Zaoblení sekce na 24px */\n      border-radius: 24px;\n      overflow: hidden; /* Zajistí oříznutí pozadí a obsahu */\n    }\n\n    .pf-main-section::before {\n      content: \"\";\n      background-image: url('https://cdn.shopify.com/s/files/1/2599/2974/files/background.jpg?v=1743845276');\n      background-size: cover;\n      background-position: center;\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      opacity: 0.2;\n      z-index: 1;\n    }\n\n    .pf-layout-container {\n      max-width: 1800px;\n      margin: 0 auto;\n      padding: 0;\n      width: 100%;\n      box-sizing: border-box;\n      height: 100%;\n      position: relative;\n      z-index: 2;\n    }\n\n    .pf-content-grid {\n      display: grid;\n      grid-template-columns: 1fr;\n      gap: 48px;\n      width: 100%;\n      height: 100%;\n      position: relative;\n      z-index: 2;\n    }\n\n    .pf-video-column {\n      background-color: #161616;\n      padding: 0;\n      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      box-sizing: border-box;\n      overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n      position: relative;\n      z-index: 2;\n      /* Nové: Zaoblení obalu videa na 24px */\n      border-radius: 24px; \n    }\n\n    .pf-video-items-grid {\n      display: grid;\n      grid-template-columns: 1fr;\n      gap: 20px;\n      width: 100%;\n      height: 100%;\n      padding: 20px;\n      box-sizing: border-box;\n    }\n\n    @media (min-width: 576px) {\n      .pf-video-items-grid {\n        grid-template-columns: repeat(2, 1fr);\n        grid-template-rows: repeat(2, 1fr);\n      }\n    }\n\n    .pf-video-item-cell {\n      position: relative;\n      width: 100%;\n      height: 250px;\n      background-color: #111;\n      overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n      margin-bottom: 20px;\n      /* Nové: Zaoblení jednotlivých video položek na 24px */\n      border-radius: 24px;\n    }\n\n    @media (min-width: 576px) {\n      .pf-video-item-cell {\n        height: 100%;\n        margin-bottom: 0;\n      }\n    }\n\n    .pf-video-embed-wrapper {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n    }\n\n    .pf-video-iframe { /* Cílíme přímo na třídu přidanou k iframe */\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      border: none;\n    }\n\n    .pf-info-column {\n      padding: 20px;\n      background-color: rgba(22, 22, 22, 0.5);\n      backdrop-filter: blur(15px);\n      -webkit-backdrop-filter: blur(15px);\n      border: 1px solid rgba(255, 255, 255, 0.15);\n      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2),\n                  inset 0 0 20px rgba(255, 255, 255, 0.05);\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      position: relative;\n      z-index: 2;\n      overflow: hidden; /* Již přítomno, dobré pro zaoblení */\n      /* Nové: Zaoblení informační sekce na 24px */\n      border-radius: 24px;\n    }\n\n    @media (min-width: 768px) {\n      .pf-info-column {\n        padding: 32px;\n        gap: 24px;\n      }\n    }\n\n    .pf-info-column::before {\n      content: \"\";\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 1px;\n      background: linear-gradient(90deg,\n                   rgba(255, 255, 255, 0.01),\n                   rgba(255, 255, 255, 0.2),\n                   rgba(255, 255, 255, 0.01));\n      z-index: 3;\n    }\n\n    .pf-info-column::after {\n      content: \"\";\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      width: 1px;\n      height: 70%;\n      background: linear-gradient(0deg,\n                   rgba(255, 255, 255, 0.01),\n                   rgba(255, 255, 255, 0.1));\n      z-index: 3;\n    }\n\n    .pf-info-header-block {\n      border-left: 4px solid rgba(232, 232, 225, 0.8);\n      padding-left: 16px;\n    }\n\n    .pf-info-title {\n      font-size: 22px;\n      font-weight: 700;\n      margin-bottom: 8px;\n      color: rgba(255, 255, 255, 0.95);\n      line-height: 1.2;\n    }\n\n    .pf-info-subtitle {\n      font-size: 16px;\n      font-weight: 400;\n      color: rgba(232, 232, 225, 0.9);\n      line-height: 1.4;\n    }\n\n    @media (min-width: 768px) {\n      .pf-info-title {\n        font-size: 28px;\n      }\n\n      .pf-info-subtitle {\n        font-size: 18px;\n      }\n    }\n\n    .pf-info-features-list {\n      list-style-type: none;\n      padding: 0;\n      display: grid;\n      grid-template-columns: 1fr;\n      gap: 12px;\n    }\n\n    .pf-info-feature-item {\n      position: relative;\n      padding-left: 28px;\n      font-size: 16px;\n      line-height: 1.5;\n      color: rgba(255, 255, 255, 0.85);\n    }\n\n    .pf-info-feature-item:before {\n      content: \"→\";\n      position: absolute;\n      left: 0;\n      color: rgba(232, 232, 225, 0.9);\n      font-weight: bold;\n    }\n\n    .pf-info-feature-item-highlight {\n      font-weight: 600;\n      color: rgba(232, 232, 225, 0.95);\n    }\n\n    .pf-info-description-text {\n      line-height: 1.6;\n      font-size: 16px;\n      color: rgba(255, 255, 255, 0.85);\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n      position: relative;\n      background-color: rgba(255, 255, 255, 0.05);\n      padding: 20px;\n      border-left: 1px solid rgba(255, 255, 255, 0.1);\n      border-top: 1px solid rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(5px);\n      -webkit-backdrop-filter: blur(5px);\n      box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.2);\n      /* Nové: Zaoblení popisu na 24px */\n      border-radius: 24px;\n      overflow: hidden; /* Pro jistotu, pokud by byl nějaký vnitřní obsah */\n    }\n\n    .pf-info-cta-wrapper {\n      margin-top: 8px;\n    }\n\n    .pf-info-cta-link {\n      display: inline-block;\n      background-color: rgba(232, 232, 225, 0.9);\n      color: #0f0f0f;\n      font-weight: 600;\n      padding: 12px 24px;\n      text-decoration: none;\n      text-transform: uppercase;\n      letter-spacing: 1px;\n      transition: all 0.25s ease;\n      border: 2px solid transparent;\n      backdrop-filter: blur(5px);\n      -webkit-backdrop-filter: blur(5px);\n      font-size: 14px;\n      width: 100%;\n      text-align: center;\n      /* Nové: Zaoblení tlačítka na 12px */\n      border-radius: 12px;\n    }\n\n    @media (min-width: 576px) {\n      .pf-info-cta-link {\n        width: auto;\n      }\n    }\n\n    @media (min-width: 768px) {\n      .pf-info-cta-link {\n        padding: 16px 32px;\n        font-size: 16px;\n      }\n    }\n\n    .pf-info-cta-link:hover {\n      background-color: transparent;\n      color: rgba(232, 232, 225, 1);\n      border: 2px solid rgba(232, 232, 225, 0.7);\n      transform: translateY(-2px);\n    }\n\n    @media (max-width: 767px) {\n      .pf-video-column {\n        display: none;\n      }\n    }\n\n    @media (max-width: 575px) {\n      .pf-info-description-text {\n        padding: 15px;\n        font-size: 14px;\n      }\n\n      .pf-info-feature-item {\n        font-size: 14px;\n      }\n    }\n\n    @media (min-width: 576px) and (max-width: 767px) {\n      .pf-main-section {\n        padding: 40px 30px;\n      }\n\n      .pf-video-item-cell {\n        height: 180px;\n      }\n    }\n\n    @media (min-width: 768px) {\n      .pf-info-features-list {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .pf-main-section {\n        padding: 60px;\n      }\n    }\n\n    @media (min-width: 992px) {\n      .pf-content-grid {\n        grid-template-columns: 2fr 1fr;\n        align-items: stretch;\n        /* height: 100%; - already defined on .pf-content-grid */\n      }\n\n      .pf-video-column, .pf-info-column {\n        /* height: 100%; - already defined on these elements */\n        /* display: flex; - already defined */\n        /* flex-direction: column; - already defined */\n      }\n\n      /* Původní pravidlo .video-container { flex: 1; padding-bottom: 0; }\n         bylo pro .pf-video-embed-wrapper, které je position:absolute.\n         Flex vlastnosti by zde neměly efekt. Výška je již nastavena na 100%.\n         Proto toto pravidlo pro .pf-video-embed-wrapper zde není. */\n\n      .pf-main-section {\n        padding: 80px;\n      }\n    }\n  </style>", "background_color": "", "text_color": ""}}, "featured_blog_4iUQtg": {"type": "featured-blog", "name": "t:sections.featured-blog.name", "settings": {"full_width": true, "carousel": true, "blog": "news", "articles_count": 3, "grid_columns": 3, "heading": "Blog posts", "heading_size": "h2", "text_alignment": "text-center", "link_text": "View all", "show_category": true, "background_color": "", "text_color": "", "heading_color": "", "heading_html_tag": "p"}}}, "order": ["slideshow_kRWa6w", "slideshow_jckh8w", "featured_collection_gMtybX", "collection_list_8LTkkE", "custom_liquid_ULHaVd", "custom_liquid_wxcHWa", "custom_liquid_YVwXgc", "featured_blog_4iUQtg"]}