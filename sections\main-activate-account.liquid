{% render 'section-bg-number-vars' %}

<div class="section section--full-width styled-links text-center" style="--container-max-inner-width: 512px;">
  <h1 class="h3 mb-4 md:mb-6" tabindex="-1">
    {{ 'customer.activate_account.title' | t }}
  </h1>
  <p class="rfs:mb-12">
    {{ 'customer.activate_account.subtext' | t }}
  </p>

  {%- form 'activate_customer_password', id: 'form-activate-account' -%}
    {% if form.errors %}
      <div class="mb-6">
        {% render 'form-errors', errors: form.errors, form_id: 'form-activate-account' %}
      </div>
    {% endif %}

    <div class="form-floating">
      <input
        class="input"
        type="password"
        name="customer[password]"
        id="form-activate-account-password"
        autocomplete="new-password"
        placeholder="{{ 'customer.activate_account.password' | t }}"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="password-error"
        {% endif %}
      >
      <label for="form-activate-account-password">
        {{ 'customer.activate_account.password' | t }}
      </label>
    </div>

    <div class="form-floating mt-6">
      <input
        class="input"
        type="password"
        name="customer[password_confirmation]"
        id="form-activate-account-password_confirmation"
        autocomplete="new-password"
        placeholder="{{ 'customer.activate_account.password_confirm' | t }}"
        {% if form.errors contains 'password_confirmation' %}
          aria-invalid="true"
          aria-describedby="password_confirmation-error"
        {% endif %}
      >
      <label for="form-activate-account-password_confirmation">
        {{ 'customer.activate_account.password_confirm' | t }}
      </label>
    </div>

    <div class="mt-12">
      <button class="button button-primary">
        {{ 'customer.activate_account.submit' | t }}
      </button>
    </div>
    <div class="mt-6">
      <button name="decline" class="styled-link">
        {{ 'customer.activate_account.cancel' | t }}
      </button>
    </div>
  {%- endform -%}
</div>
