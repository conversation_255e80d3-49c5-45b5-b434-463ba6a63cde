{% render 'section-bg-number-vars' %}

<style>
#shopify-section-{{ section.id }} .featured-article {
  --color-background: var(--color-base-background);
  --color-foreground: var(--color-base-foreground);
  --color-headings: var(--color-base-headings);
}
</style>

<div {% render 'section-attrs' %}>
  {% render 'section-header' %}

  <div class="section-body">
    {% render 'featured-article',
      featured_article: section.settings.featured_article,
      button_style: section.settings.featured_post_button_style,
      button_background_color: section.settings.featured_post_button_background_color,
      button_text_color: section.settings.featured_post_button_text_color,
      image_width: section.settings.image_width,
      tag: section.settings.card_show_tag,
      excerpt: section.settings.card_show_excerpt,
      date: section.settings.card_show_date,
      comments: section.settings.card_show_comment_count,
      background_color: section.settings.featured_post_background_color,
      text_color: section.settings.featured_post_text_color,
      heading_color: section.settings.featured_post_heading_color
    %}
  </div>
</div>

{% schema %}
{
  "name": "Featured blog post",
  "settings": [
    {
      "type": "article",
      "id": "featured_article",
      "label": "Blog post"
    },
    {
      "type": "checkbox",
      "id": "card_show_tag",
      "label": "t:sections.main-blog.blocks.posts.settings.card_show_tag.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "card_show_excerpt",
      "label": "t:sections.main-blog.blocks.posts.settings.card_show_excerpt.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "card_show_date",
      "label": "t:sections.main-blog.blocks.posts.settings.card_show_date.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "card_show_comment_count",
      "label": "t:sections.main-blog.blocks.posts.settings.card_show_comment_count.label",
      "default": false
    },
    {
      "type": "range",
      "id": "image_width",
      "min": 25,
      "max": 75,
      "step": 5,
      "unit": "%",
      "label": "t:sections.main-blog.blocks.posts.settings.image_width.label",
      "default": 50
    },
    {
      "type": "select",
      "id": "featured_post_button_style",
      "label": "t:sections.all.button_style.label",
      "options": [
        {
          "value": "",
          "label": "t:sections.all.button_style.options.filled"
        },
        {
          "value": "button-outline",
          "label": "t:sections.all.button_style.options.outline"
        },
        {
          "value": "hidden",
          "label": "t:sections.main-blog.blocks.posts.settings.featured_post_button_style.options__2.label"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.heading_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.heading_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.heading_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.heading_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "id": "featured_post_background_color",
      "label": "Post background"
    },
    {
      "type": "color",
      "id": "featured_post_text_color",
      "label": "Post text"
    },
    {
      "type": "color",
      "id": "featured_post_heading_color",
      "label": "Post heading"
    },
    {
      "type": "color",
      "id": "featured_post_button_background_color",
      "label": "t:sections.main-blog.blocks.posts.settings.featured_post_button_background_color.label"
    },
    {
      "type": "color",
      "id": "featured_post_button_text_color",
      "label": "t:sections.main-blog.blocks.posts.settings.featured_post_button_text_color.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "Featured blog post"
    }
  ]
}
{% endschema %}
