{% liquid
  if section.settings.background_color == blank or section.settings.background_color.rgba == '0 0 0 / 0.0' or section.settings.background_color.rgba == settings.colors_background.rgba
    assign section_class = 'border-b'
  endif
%}

<style>
  :root {
    --header-logo-width: {{ section.settings.logo_width }}px;
    --header-logo-width-mobile: {{ section.settings.logo_width_mobile }}px;
  }

  main > .shopify-section:first-child {
    {% liquid
      if section.settings.background_color == blank or section.settings.background_color.rgba == '0 0 0 / 0.0'
        assign bg_number = 16646655
      else
        assign red = section.settings.background_color.red | times: 65025
        assign green = section.settings.background_color.green | times: 255
        assign blue = section.settings.background_color.blue

        assign bg_number = red | plus: green | plus: blue
      endif
    %}
    --previous-section-bg-number: {{ bg_number }}
  }
</style>

<div
  class="flex items-center py-2 min-h-[4rem] md:min-h-[6rem] color {{ section_class }}"
  style="{% render 'section-style' %}"
>
  <div class="container flex items-center">
    <div class="header__logo">
      {%- if section.settings.logo != blank -%}
        {% assign logo_width_2x = section.settings.logo_width | times: 2 %}
        {% assign logo_width_mobile_2x = section.settings.logo_width_mobile | times: 2 %}
        {%- capture logo_widths -%}
          {{ section.settings.logo_width_mobile }}, {{ logo_width_mobile_2x }}, {{ section.settings.logo_width }}, {{ logo_width_2x }}
        {%- endcapture -%}
        {%- capture logo_sizes -%}
          (min-width: 768px) {{ section.settings.logo_width }}px, {{ section.settings.logo_width_mobile }}px
        {%- endcapture -%}
        {{
          section.settings.logo
          | image_url: width: logo_width_2x
          | image_tag:
            width: section.settings.logo_width,
            widths: logo_widths,
            sizes: logo_sizes,
            alt: section.settings.logo.alt,
            class: 'header__logo-img'
        }}
      {%- else -%}
        <span class="heading text-h6/h6 md:text-h4/h4 !normal-case pr-2">{{ shop.name }}</span>
      {%- endif -%}
    </div>

    <modal-trigger target="#enter-password-modal" class="contents">
      <button class="min-w-[6rem] ml-auto flex items-center gap-2 md:gap-3 styled-link text-xs md:text-sm">
        <svg
          class="w-4 md:w-5 shrink-0"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M17 10V8C17 5.23858 14.7614 3 12 3C9.23858 3 7 5.23858 7 8V10M12 14.5V16.5M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21Z" stroke="currentColor" stroke-width="var(--icon-stroke-width)" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>

        {{ 'general.password_page.login_password_button' | t }}
      </button>
    </modal-trigger>
  </div>
</div>

<x-modal
  id="enter-password-modal"
  class="modal bottom-modal [&::part(panel)]:w-[28rem]"
  position="center-center"
  show-on=".message-danger"
>
  <div slot="content" tabindex="-1">
    <div class="modal-header flex items-center py-6">
      <h2 class="h6">
        {{ 'general.password_page.login_form_heading' | t }}
      </h2>
      <button class="modal-close ml-auto" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}">
        {% render 'icon-times' %}
      </button>
    </div>
    <div class="modal-body">
      {% form 'storefront_password', data-form-button-loading: true %}
        <div class="form-floating grow">
          <input
            type="password"
            name="password"
            id="Password"
            class="input"
            autocomplete="current-password"
            {% if form.errors %}
              aria-invalid="true"
              aria-describedby="PasswordLoginForm-password-error"
            {% endif %}
            placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"
          >
          <label for="{{ 'general.password_page.login_form_password_label' | t }}">
            {{- 'general.password_page.login_form_password_placeholder' | t -}}
          </label>
          {% if form.errors %}
            <div id="PasswordLoginForm-password-error" role="status">
              <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
              <span class="message message-danger text-danger text-xs md:text-sm mt-2">
                {{ 'general.password_page.login_form_error' | t -}}
              </span>
            </div>
          {% endif %}
        </div>
        <button type="submit" name="commit" class="button mt-4 w-full">
          {{ 'general.password_page.login_form_submit' | t }}
        </button>
      {% endform %}
      <div class="text-xs md:text-sm mt-8">{{ 'general.password_page.admin_link_html' | t }}</div>
    </div>
  </div>
</x-modal>

{% schema %}
{
  "name": "t:sections.password-header.name",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.password-header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "logo_width",
      "label": "t:sections.password-header.settings.logo_width.label",
      "min": 64,
      "max": 320,
      "step": 4,
      "default": 160,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "logo_width_mobile",
      "label": "t:sections.password-header.settings.logo_width_mobile.label",
      "min": 48,
      "max": 160,
      "step": 4,
      "default": 96,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    }
  ]
}
{% endschema %}
