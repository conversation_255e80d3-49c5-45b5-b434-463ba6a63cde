{% render 'section-bg-number-vars' %}

<div {% render 'section-attrs' %}>
  <div class="section-body section-content-spacing {{ section.settings.text_alignment }}">
    {% render 'section-content' %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-404.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label",
      "default": "Page not found"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.text_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.text_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.text_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.text_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "t:sections.all.button_text.label",
      "default": "Continue shopping"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "t:sections.all.button_link.label",
      "default": "/collections/all"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:sections.all.button_style.label",
      "options": [
        {
          "value": "",
          "label": "t:sections.all.button_style.options.filled"
        },
        {
          "value": "button-outline",
          "label": "t:sections.all.button_style.options.outline"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.button_background_color.label",
      "id": "button_background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.button_text_color.label",
      "id": "button_text_color"
    }
  ]
}
{% endschema %}
