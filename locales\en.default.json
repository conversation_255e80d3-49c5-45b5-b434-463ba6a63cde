{"general": {"submit": "Submit", "continue_shopping": "Continue shopping", "social": {"links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tiktok": "TikTok", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo"}}, "pagination": {"label": "Pagination", "page": "Page {{ number }}", "next": "Next page", "previous": "Previous page", "load_more": "Load more", "load_more_text": "Showing {{ count }} of {{ all }}", "load_more_failed": "Failed to load products"}, "password_page": {"login_form_heading": "Store login", "login_password_button": "Enter using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_error": "Wrong password!", "login_form_submit": "Enter", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "dialog": {"close": "Close", "close_modal": "Close modal", "yes": "Yes", "no": "No", "confirm": "Confirm"}, "media": {"3d": "3D"}, "share": {"close": "Close share", "copy_link": "Copy link", "share_url": "Link", "copied_to_clipboard": "Link copied to clipboard", "button_text": "Share"}, "breadcrumbs": {"home": "Home"}}, "newsletter": {"email": "E-mail", "button_label": "Subscribe", "success": "Thank you for subscribing!"}, "blogs": {"tags": {"all": "All posts"}, "article": {"author": "{{ author }}", "comments": {"one": "{{ count }} comment", "other": "{{ count }} comments"}, "back_to_blog": "Back to blog", "comment_success": "Your comment was posted successfully! Thank you!", "comment_success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "previous": "Previous", "next": "Next", "leave_comment": "Leave a comment", "comments_moderated": "All comments are moderated before being published.", "comment_form_name": "Name", "comment_form_email": "Email", "comment_form_message": "Your comment", "submit_comment": "Submit comment"}}, "accessibility": {"close_modal": "Close modal", "skip_to_content": "Skip to content", "skip_to_text": "Skip to text", "unit_price_separator": "per", "error": "Error", "refresh_page": "Choosing a selection results in a full page refresh.", "filter_active_values": "active values", "by": "By", "vendor": "Vendor:", "video_exit_message": "{{ title }} opens full screen video in same window.", "loading": "Loading", "skip_to_product_info": "Skip to product info", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars", "total_reviews": "total reviews", "previous": "Previous", "next": "Next", "open_menu": "Open menu", "image_before_and_after_label": "Use left and right arrow keys to move.", "read_more": "Read more", "breadcrumbs": "Breadcrumbs", "cart_remove_item": "Remove {{ item }}", "add_to_cart": "Add to cart", "filter_minimum_price": "Minimum price", "filter_maximum_price": "Maximum price", "play_video": "Play video", "view_3d_model": "View 3D model", "go_to_item": "Go to item {{ index }}", "decrease_quantity": "Decrease quantity", "increase_quantity": "Increase quantity"}, "customer": {"activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal", "total_duties": "Duties"}, "account": {"title": "Account", "orders": "Orders", "addresses": "Addresses", "details": "Account details", "view_addresses": "View addresses ({{ count }})", "return": "Return to Account details"}, "log_out": "Log out", "addresses": {"title": "Addresses", "default": "Default address", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "login_page": {"cancel": "Cancel", "create_account": "Create account", "email": "Email", "forgot_password": "Forgot your password?", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "<PERSON><PERSON>", "sign_in": "Sign in", "submit": "Submit"}, "recover_password": {"title": "Reset your password", "subtext": "We will send you an email to reset your password", "success": "We've sent you an email with a link to update your password."}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "orders": {"order_number_link": "Order number {{ number }}", "view_order_button": "View details", "payment_status": "Payment status", "title": "Order history", "order": "Order", "order_number": "Order {{ name }}", "order_number_summary": "Order {{ name }} summary", "date": "Date", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}}, "templates": {"404": {"title": "Page not found", "subtext": "404"}, "collection": {"empty": "This collection is empty"}, "contact": {"form": {"name": "Name", "email": "E-mail", "comment": "Comment", "send": "Send message", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "error_heading": "Please adjust the following:"}}, "search": {"no_results": "No results found for “{{ terms }}”.", "products": "Products", "results_pages_with_count": {"one": "{{ count }} page", "other": "{{ count }} pages"}, "results_suggestions_with_count": {"one": "{{ count }} suggestion", "other": "{{ count }} suggestions"}, "results_products_with_count": {"one": "{{ count }} product", "other": "{{ count }} products"}, "results_with_count": {"one": "{{ count }} result", "other": "{{ count }} results"}, "results_with_count_and_term": {"one": "{{ count }} result found for “{{ terms }}”", "other": "{{ count }} results found for “{{ terms }}”"}, "title": "Search", "results_title": "Search results", "view_all": "View all", "suggestions": "Suggestions", "collections": "Collections", "blog_posts": "Blog posts", "input_label": "Search for..."}}, "products": {"facets": {"filter_and_sort_button": "Filter and sort", "from": "From", "max_price": "The highest price is {{ price }}", "reset": "Reset", "sort_by_label": "Sort by", "to": "to", "use_fewer_filters_html": "No products found.  Use fewer filters or <a class=\"font-bold\" href=\"{{ link }}\">remove all</a>.", "results_count": {"one": "{{ count }} Result", "other": "{{ count }} Results"}, "apply": "Apply", "filters_drawer_button": "Filters", "filters_drawer_title": "Filters", "clear_all": "Clear all", "clear_filter": "Remove filter"}, "product": {"add_to_cart": "Add to cart", "include_taxes": "Tax included", "exclude_taxes": "Tax excluded", "on_sale": "Sale", "sold_out": "Sold out", "shipping_policy_html": "<a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "product_variants": "Product variants", "total_reviews": "total reviews", "reviews_count": {"one": "{{ count }} review", "other": "{{ count }} reviews"}, "no_reviews_yet": "No reviews yet.", "write_review": "Write a review", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars", "price": {"from_price_html": "<span class=\"price-from\">From</span> {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price", "starting_at": "Starting at"}, "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}", "minimum_of": "Minimum of {{ quantity }}", "maximum_of": "Maximum of {{ quantity }}", "multiples_of": "Increments of {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> in cart"}, "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "unavailable": "Unavailable", "view_full_details": "View full details", "low_stock": {"one": "Only 1 unit left", "other": "Only {{ count }} units left"}, "in_stock": "In stock", "colors_count_text": {"one": "{{ count }} color available", "other": "{{ count }} colors available"}, "size_chart": "Size chart", "on_backorder": "On backorder", "sku": "SKU: {{ sku }}", "select_options": "Select options"}, "pickup_availability": {"view_info": {"one": "View store information", "other": "Check availability at other stores"}, "error": "Couldn't load pickup availability", "available": "Pickup available", "available_at_html": "Pickup available at <span class=\"\">{{ location_name }}</span>", "unavailable_at_html": "Pickup currently unavailable at <span class=\"\">{{ location_name }}</span>", "unavailable": "Pickup currently unavailable", "refresh": "Refresh"}, "price": {"from_price_html": "From {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}"}, "unavailable": "Unavailable", "added_to_cart": "Product added to cart", "save_badge": "Save {{ value }}"}, "sections": {"header": {"account": "Account", "cart": "<PERSON><PERSON>", "search": "Search", "login": "<PERSON><PERSON>", "menu": "<PERSON><PERSON>"}, "cart": {"title": "<PERSON><PERSON>", "item_count": {"one": "{{ count }} item", "other": "{{ count }} items"}, "order_summary": "Summary", "remove": "Remove", "weight": "Weight", "subtotal": "Subtotal", "total": "Total", "note": "Order note", "checkout": "Check out", "view_cart": "View cart", "empty": "Your cart is empty", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add {{ quantity }} of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included. If you have a valid EU VAT number, please contact me before purchase to apply it.", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "If you have a valid EU VAT number, please contact me before purchase to apply it.", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity", "image": "Product image", "remove": "Remove"}, "update_quantities": "Update quantities", "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}, "add_note": "Add order note", "edit_note": "Edit order note", "save_note_button": "Save note", "save_note_failed": "An error occured while saving the order note. Please try again.", "estimate_shipping": "Estimate shipping", "free_shipping_message_html": "Spend <strong>{{ remaining }}</strong> more for FREE delivery!", "free_shipping_qualified_html": "You have qualified for <strong>FREE</strong> delivery!"}, "page": {"title": "Page title"}, "featured_blog": {"view_all": "View all", "onboarding_title": "Blog post", "onboarding_content": "Give your customers a summary of your blog post"}, "shipping_estimator": {"title": "Estimate shipping", "country": "Country", "province": "State/Province", "zipcode": "Zipcode", "get_estimate": "Get Estimate", "error": "An error occured while loading shipping rates.", "no_rates": "No shipping rates found for this location."}, "privacy_banner": {"accept": "Accept", "decline": "Decline"}, "featured_article": {"read_more": "Read more"}}, "gift_cards": {"issued": {"how_to_use_gift_card": "Use the gift card code online or QR code in-store", "title": "Your gift card", "remaining_balance": "Remaining balance:", "shop_link": "Visit online store", "add_to_apple_wallet": "Add to Apple Wallet", "qr_image_alt": "QR code — scan to redeem gift card", "copy_code": "Copy code", "print": "Print", "expiration_date": "Expires {{ expires_on }}", "copy_code_success": "Code copied successfully", "expired": "Expired"}}, "localization": {"country_label": "Country / region", "country_option": "{{ name }} ({{ currency_iso_code }} {{ currency_symbol }})", "language_label": "Language", "update_language": "Update language", "update_country": "Update country / region"}, "recipient": {"form": {"checkbox": "I want to send this as a gift", "email_label": "Recipient email", "email": "Email", "name_label": "Recipient name (optional)", "name": "Name", "message_label": "Message (optional)", "message": "Message", "max_characters": "{{ max_chars }} characters max", "send_on": "YYYY-MM-DD", "send_on_label": "Send on (optional)"}}}