{% liquid
  if section.settings.image
    assign bg_number = -2
  endif
%}

{% render 'section-bg-number-vars', bg_number: bg_number %}

{%
  render 'page-banner',
  image: section.settings.image,
  heading: section.settings.heading,
  content: section.settings.content,
  background_color: section.settings.background_color,
  text_color: section.settings.text_color,
  heading_color: section.settings.heading_color,
  overlay_color: section.settings.overlay_color,
  overlay_opacity: section.settings.overlay_opacity,
  overlay_type: section.settings.overlay_type,
  full_width: section.settings.full_width,
  enable_transparent_header: section.settings.enable_transparent_header,
  use_original_media_size: section.settings.use_original_media_size,
  banner_spacing: section.settings.banner_spacing,
  text_alignment_mobile: section.settings.text_alignment_mobile,
  text_alignment: section.settings.text_alignment,
  heading_size: section.settings.heading_size,
  show_heading: true,
  show_content: true,
  show_image: true,
  animation_group: "main-blog"
%}

{% schema %}
{
  "name": "t:sections.main-blog-banner.name",
  "class": "section-main-blog-banner",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_transparent_header",
      "label": "t:sections.all.enable_transparent_header.label",
      "info": "t:sections.all.enable_transparent_header.info",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "use_original_media_size",
      "label": "t:sections.main-blog-banner.settings.use_original_media_size.label",
      "default": false,
      "info": "t:sections.main-blog-banner.settings.use_original_media_size.info"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.main-blog-banner.settings.image.label"
    },
    {
      "type": "range",
      "id": "banner_spacing",
      "min": 0,
      "max": 640,
      "step": 20,
      "unit": "px",
      "label": "t:sections.main-blog-banner.settings.banner_spacing.label",
      "default": 360
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label",
      "default": "{{ blog.title }}"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h1"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.content_position.label",
      "options": [
        {
          "value": "md:content-top-left",
          "label": "t:sections.all.position.top_left.label"
        },
        {
          "value": "md:content-top-center",
          "label": "t:sections.all.position.top_center.label"
        },
        {
          "value": "md:content-top-right",
          "label": "t:sections.all.position.top_right.label"
        },
        {
          "value": "md:content-middle-left",
          "label": "t:sections.all.position.middle_left.label"
        },
        {
          "value": "md:content-middle-center",
          "label": "t:sections.all.position.middle_center.label"
        },
        {
          "value": "md:content-middle-right",
          "label": "t:sections.all.position.middle_right.label"
        },
        {
          "value": "md:content-bottom-left",
          "label": "t:sections.all.position.bottom_left.label"
        },
        {
          "value": "md:content-bottom-center",
          "label": "t:sections.all.position.bottom_center.label"
        },
        {
          "value": "md:content-bottom-right",
          "label": "t:sections.all.position.bottom_right.label"
        }
      ],
      "default": "md:content-middle-center"
    },
    {
      "type": "select",
      "id": "text_alignment_mobile",
      "label": "t:sections.all.content_position_mobile.label",
      "options": [
        {
          "value": "content-top-left",
          "label": "t:sections.all.position.top_left.label"
        },
        {
          "value": "content-top-center",
          "label": "t:sections.all.position.top_center.label"
        },
        {
          "value": "content-top-right",
          "label": "t:sections.all.position.top_right.label"
        },
        {
          "value": "content-middle-left",
          "label": "t:sections.all.position.middle_left.label"
        },
        {
          "value": "content-middle-center",
          "label": "t:sections.all.position.middle_center.label"
        },
        {
          "value": "content-middle-right",
          "label": "t:sections.all.position.middle_right.label"
        },
        {
          "value": "content-bottom-left",
          "label": "t:sections.all.position.bottom_left.label"
        },
        {
          "value": "content-bottom-center",
          "label": "t:sections.all.position.bottom_center.label"
        },
        {
          "value": "content-bottom-right",
          "label": "t:sections.all.position.bottom_right.label"
        }
      ],
      "default": "content-middle-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.overlay_color.label",
      "id": "overlay_color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "t:sections.all.overlay_opacity.label",
      "default": 35
    },
    {
      "type": "select",
      "id": "overlay_type",
      "label": "t:sections.all.overlay_type.label",
      "options": [
        {
          "value": "solid",
          "label": "t:sections.all.overlay_type.options__0.label"
        },
        {
          "value": "gradient",
          "label": "t:sections.all.overlay_type.options__1.label"
        }
      ],
      "default": "solid"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "h1",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ]
}
{% endschema %}
