{% liquid
  if section.settings.carousel == false
    assign pills_container_class = 'flex-wrap'
    assign carousel_tag = 'div'
  else
    assign pills_container_class = 'flex-nowrap'
    assign carousel_class = 'grid scroll-area-x bleed'
    assign carousel_tag = 'scroll-carousel'
  endif

  case section.settings.text_alignment
    when 'text-left'
      assign pills_container_class = pills_container_class | append: ' justify-start '
    when 'text-center'
      assign pills_container_class = pills_container_class | append: ' justify-center '
    when 'text-right'
      assign pills_container_class = pills_container_class | append: ' justify-end '
  endcase
%}

{% render 'section-bg-number-vars' %}

<style>
  #shopify-section-{{ section.id }} {
    {% if section.settings.pills_background_color != blank and section.settings.pills_background_color.rgba != '0 0 0 / 0.0' %}
      --button-pill-background: {{ section.settings.pills_background_color.rgb }};
      {% render 'button-hover-active-vars', color: section.settings.pills_background_color, var: "--button-pill-background" %}
    {% endif %}

    {% if section.settings.pills_text_color != blank and section.settings.pills_text_color.rgba != '0 0 0 / 0.0' %}
      --button-pill-foreground: {{ section.settings.pills_text_color.rgb }};
    {% endif %}
  }
</style>

<div {% render 'section-attrs' %}>
  {% render 'section-header' %}

  <div class="section-body">
    <div class="relative -mb-4">
      <{{ carousel_tag }} class="grid {{ carousel_class }}" item-selector=".button-pill">
        <div
          class="pb-4 inline-flex gap-x-3 gap-y-4 {{ pills_container_class }}"
        >
          {%- for block in section.blocks -%}
            <a
              data-instant
              href="{{ block.settings.collection.url }}"
              class="button-pill"
              data-animation="block"
              data-animation-group="{{ section.id }}"
              {{ block.shopify_attributes }}
            >
              {{ block.settings.collection.title | default: 'Collection' }}
            </a>
          {%- endfor -%}
        </div>
      </{{ carousel_tag }}>

      {% render 'carousel-buttons' %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.collection-list-pills.name",
  "class": "section-collection-list-pills",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "carousel",
      "label": "t:sections.all.carousel.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label",
      "default": "Featured collections"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.heading_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.heading_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.heading_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.heading_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "color",
      "label": "t:sections.collection-list-pills.settings.pills_background_color.label",
      "id": "pills_background_color"
    },
    {
      "type": "color",
      "label": "t:sections.collection-list-pills.settings.pills_text_color.label",
      "id": "pills_text_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:sections.collection-list.blocks.collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection-list.blocks.collection.settings.collection.label"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.collection-list-pills.presets__0.name",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
