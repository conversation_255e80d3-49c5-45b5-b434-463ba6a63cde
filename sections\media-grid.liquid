{% render 'section-bg-number-vars' %}

<style>
  #shopify-section-{{ section.id }} {
    --media-grid-row-height: {% render 'rfs', value: section.settings.row_height, base_value: 150 %};
  }
</style>

<div {% render 'section-attrs' %}>
  {% render 'section-header' %}

  <div
    class="
      section-body media-grid
      {% if section.settings.layout == 'stack' %}
        media-grid--stack
      {% else %}
        media-grid--layout-{{ section.blocks.size }}-{{ section.settings.layout }}
      {% endif %}
    "
  >
    {% for block in section.blocks %}
      <div
        class="media-grid__element color rounded-block overflow-hidden grid p-10 md:p-12 relative"
        style="
          {% render 'apply-color-var', var: '--color-background', color: block.settings.background_color %}
          {% render 'apply-color-var', var: '--color-foreground', color: block.settings.text_color %}
          {% render 'apply-color-var', var: '--color-headings', color: block.settings.heading_color %}
        "
        data-animation="block"
        data-animation-group="{{ section.id }}"
        {{ block.shopify_attributes }}
      >
        {% if block.settings.image != blank %}
          <div
            class="media media--overlay absolute inset-0 select-none pointer-events-none"
            style="--media-overlay: rgb(0 0 0 / {{ block.settings.overlay_opacity }}%);"
          >
            {% capture image_sizes %}
              {% render 'media-grid-image-sizes',
                count: section.blocks.size,
                layout: section.settings.layout,
                index: forloop.index
              %}
            {% endcapture %}
            {{ block.settings.image | image_url: width: 2800 | image_tag: sizes: image_sizes, loading: 'lazy' }}
          </div>
        {% endif %}

        <div class="media-grid__element-content z-10 relative {{ block.settings.content_alignment_mobile }} {{ block.settings.content_alignment }}">
          {% render 'section-content', section: block %}
        </div>
      </div>
    {% endfor %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.media-grid.name",
  "class": "section-media-grid",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.media-grid.settings.layout.label",
      "options": [
        {
          "value": "stack",
          "label": "t:sections.media-grid.settings.layout.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.media-grid.settings.layout.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.media-grid.settings.layout.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.media-grid.settings.layout.options__3.label"
        },
        {
          "value": "4",
          "label": "t:sections.media-grid.settings.layout.options__4.label"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "row_height",
      "min": 160,
      "max": 640,
      "step": 20,
      "unit": "px",
      "label": "t:sections.media-grid.settings.row_height.label",
      "default": 360
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.content"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.heading_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.heading_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.heading_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.heading_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "blocks": [
    {
      "type": "card",
      "name": "t:sections.media-grid.blocks.card.name",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.media-grid.blocks.card.settings.image.label"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "%",
          "label": "t:sections.media-grid.blocks.card.settings.overlay_opacity.label",
          "default": 24
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.all.heading.label",
          "default": "Card title"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:sections.all.heading_size.label",
          "options": [
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__0.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__5.label"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__6.label"
            }
          ],
          "default": "h3"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.all.content.label",
          "default": "<p>Showcase your content with a media grid. Customize the layout, arrange images, and add text overlays to engage your audience.</p>"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "t:sections.all.button_text.label"
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "t:sections.all.button_link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_position.label",
          "options": [
            {
              "value": "md:content-top-left",
              "label": "t:sections.all.position.top_left.label"
            },
            {
              "value": "md:content-top-center",
              "label": "t:sections.all.position.top_center.label"
            },
            {
              "value": "md:content-top-right",
              "label": "t:sections.all.position.top_right.label"
            },
            {
              "value": "md:content-middle-left",
              "label": "t:sections.all.position.middle_left.label"
            },
            {
              "value": "md:content-middle-center",
              "label": "t:sections.all.position.middle_center.label"
            },
            {
              "value": "md:content-middle-right",
              "label": "t:sections.all.position.middle_right.label"
            },
            {
              "value": "md:content-bottom-left",
              "label": "t:sections.all.position.bottom_left.label"
            },
            {
              "value": "md:content-bottom-center",
              "label": "t:sections.all.position.bottom_center.label"
            },
            {
              "value": "md:content-bottom-right",
              "label": "t:sections.all.position.bottom_right.label"
            }
          ],
          "default": "md:content-middle-left"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "label": "t:sections.all.content_position_mobile.label",
          "options": [
            {
              "value": "content-top-left",
              "label": "t:sections.all.position.top_left.label"
            },
            {
              "value": "content-top-center",
              "label": "t:sections.all.position.top_center.label"
            },
            {
              "value": "content-top-right",
              "label": "t:sections.all.position.top_right.label"
            },
            {
              "value": "content-middle-left",
              "label": "t:sections.all.position.middle_left.label"
            },
            {
              "value": "content-middle-center",
              "label": "t:sections.all.position.middle_center.label"
            },
            {
              "value": "content-middle-right",
              "label": "t:sections.all.position.middle_right.label"
            },
            {
              "value": "content-bottom-left",
              "label": "t:sections.all.position.bottom_left.label"
            },
            {
              "value": "content-bottom-center",
              "label": "t:sections.all.position.bottom_center.label"
            },
            {
              "value": "content-bottom-right",
              "label": "t:sections.all.position.bottom_right.label"
            }
          ],
          "default": "content-middle-center"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "label": "t:sections.all.background_color.label",
          "id": "background_color",
          "default": "#444444"
        },
        {
          "type": "color",
          "label": "t:sections.all.text_color.label",
          "id": "text_color",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "label": "t:sections.all.heading_color.label",
          "id": "heading_color",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "label": "t:sections.all.button_background_color.label",
          "id": "button_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.all.button_text_color.label",
          "id": "button_text_color"
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.seo"
        },
        {
          "type": "select",
          "id": "heading_html_tag",
          "label": "t:sections.all.heading_html_tag.label",
          "options": [
            {
              "value": "h1",
              "label": "h1"
            },
            {
              "value": "h2",
              "label": "h2"
            },
            {
              "value": "h3",
              "label": "h3"
            },
            {
              "value": "h4",
              "label": "h4"
            },
            {
              "value": "h5",
              "label": "h5"
            },
            {
              "value": "h6",
              "label": "h6"
            },
            {
              "value": "p",
              "label": "p"
            },
            {
              "value": "span",
              "label": "span"
            },
            {
              "value": "div",
              "label": "div"
            }
          ],
          "default": "p",
          "info": "t:sections.all.heading_html_tag.info"
        }
      ]
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.media-grid.name",
      "blocks": [
        {
          "type": "card"
        },
        {
          "type": "card"
        }
      ]
    }
  ]
}
{% endschema %}
