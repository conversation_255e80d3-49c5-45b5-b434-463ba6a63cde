<!doctype html>
<html
  class="
    no-js
    {{ settings.layout_space_between_sections }}
    {{ settings.layout_space_between_blocks }}
    {% if settings.enable_reveal_on_scroll_animations %}
      scroll-animations-enabled
    {% endif %}
    {% if settings.enable_zoom_in_animations %}
      image-hover-zoom-enabled
    {% endif %}
  "
  lang="{{ request.locale.iso_code }}"
>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {% if settings.favicon != blank %}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {% endif %}

    <title>
      {{ page_title -}}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}

    <script type="module" src="{{ 'vendor.mjs' | asset_url }}" defer></script>
    <script type="module" src="{{ 'main.mjs' | asset_url }}" defer></script>

    {{ content_for_header }}

    <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    </script>

    {% liquid
      render 'theme-css-colors'
      render 'theme-css-typography'
      render 'theme-css-layout'
      render 'theme-css-appearance'
      render 'theme-css-icons'
    %}

    {{ 'main.css' | asset_url | stylesheet_tag }}

    <script type="text/javascript">
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "jo078319tm");
    </script>
  </head>
  <body
    class="
      antialiased
      {{ template.name }}-page
      color-background
      inputs-{{ settings.input_style }}
      {% if settings.colors_input_accent != blank and settings.colors_input_accent.rgba != '0 0 0 / 0.0' %} use-input-accent {% endif %}
    "
    data-instant-whitelist
  >
    <script>
      function setScrollbarWidth() {
        if (document.body.classList.contains('no-scroll')) return;

        const w = document.body.getBoundingClientRect().width;
        document.body.style.overflow = 'hidden';
        const w2 = document.body.getBoundingClientRect().width;
        document.body.style.overflow = null;

        document.documentElement.style.setProperty('--scrollbar-width', `${w2 - w}px`);
      }

      setScrollbarWidth();
    </script>

    {% render 'height-observer-script' %}

    <a class="skip-to-content-link visually-hidden" href="#MainContent">
      {{ 'accessibility.skip_to_content' | t }}
    </a>

    {% liquid
      render 'template-modal'
      render 'template-mobile-menu-page'
    %}

    <header class="contents">
      {% sections 'header-group' %}
    </header>

    {% sections 'overlay-group' %}

    {% capture content_str %}{{ content_for_layout }}{% endcapture %}
    {% assign section_split = content_str | split: 'id="shopify-section' %}

    <style
      data-transparent-header-style
      {% unless section_split[1] contains 'enable-transparent-header' %}
        media="not all"
      {% endunless %}
    >
      .js {
        --header-height-actual: 0px;
      }

      .js .section-site-header:not(.section-site-header--sticky) .header--transparent {
        --header-background-opacity: 0%;
        --color-header-foreground: var(--header-transparent-text-color);
      }

      .js .section-site-header .header {
        transition: 333ms background-color, 333ms color;
      }

      .js .section-site-header:not(.section-site-header--sticky) .header--transparent .header__logo-img {
        opacity: 0;
      }

      .js
        .section-site-header:not(.section-site-header--sticky)
        .header--transparent
        .header__logo-img--header-transparent {
        display: block;
      }
    </style>

    {% render 'transparent-header-script' %}

    <scroll-animate threshold="0.2" root-margin="0px 0px -5% 0px">
      <main
        id="MainContent"
        role="main"
        tabindex="-1"
        class="{% if request.page_type == 'product' %} product-grid {% endif %}"
      >
        {{ content_for_layout }}
        {% sections 'footer-group' %}
      </main>

      {% if request.page_type == 'product' %}
        {% render 'product-grid-update-layout' %}
      {% endif %}
    </scroll-animate>

    <ul hidden>
      <li id="A11y-RefreshPageMessage">{{ 'accessibility.refresh_page' | t }}</li>
    </ul>

    <x-modal position="center-center" class="modal bottom-modal bottom-modal-lg" id="variant-added-modal">
      <div slot="content" tabindex="-1" class="w-full p-8 md:px-10"></div>
    </x-modal>

    {% render 'quick-add-modal' %}

    <div class="loading-overlay" data-main-loading-overlay style="display: none;">
      {% render 'spinner' %}
    </div>

    <notification-wrapper
      class="notification-wrapper flex lg:justify-center fixed bottom-0 left-0 right-0 z-[1000]"
      style="display: none;"
    >
      <div
        class="notification bg-base-accent text-base-accent-foreground max-lg:w-full lg:mb-8 px-6 py-4 lg:rounded-full"
      ></div>
    </notification-wrapper>

    <script>
      window.enableRevealOnScrollAnimations = {{ settings.enable_reveal_on_scroll_animations | json }};

      window.addedToCartNotification = {{ settings.added_to_cart_notification | json }};

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`,
      };

      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}.js',
        cart_url: '{{ routes.cart_url }}.js',
        cart_change_url: '{{ routes.cart_change_url }}.js',
        cart_update_url: '{{ routes.cart_update_url }}.js',
        predictive_search_url: '{{ routes.predictive_search_url }}',
        product_recommendations_url: '{{ routes.product_recommendations_url }}',
        search_url: '{{ routes.search_url }}'
      };

      window._t = {
        share: {
          copied_to_clipboard: '{{ 'general.share.copied_to_clipboard' | t }}',
        },
        pagination: {
          load_more_error: '{{ 'general.pagination.load_more_failed' | t }}',
        },
        sections: {
          cart: {
            add_note: '{{ 'sections.cart.add_note' | t }}',
            edit_note: '{{ 'sections.cart.edit_note' | t }}',
          },
          shipping_estimator: {
            no_rates: '{{ 'sections.shipping_estimator.no_rates' | t }}',
          }
        }
      };

      window.svgs = {
        chevron: `{% render 'icon-chevron' %}`,
        times: `{% render 'icon-times' %}`,
        zoom_in: `{% render 'icon-zoom-in' %}`,
        zoom_out: `{% render 'icon-zoom-out' %}`,
      };

      {% if request.page_type == 'product' %}
        const rvp = new Set(JSON.parse(localStorage.getItem('essence:recently-viewed-products') ?? '[]'));
        rvp.delete({{ product.id }});
        rvp.add({{ product.id }});
        localStorage.setItem('essence:recently-viewed-products', JSON.stringify(Array.from(rvp)));
      {% endif %}

      {% if request.page_type == 'policy' %}
        document.querySelector('.shopify-policy__body .rte').classList.add('prose', 'max-w-none');
      {% endif %}
    </script>
  </body>
</html>
