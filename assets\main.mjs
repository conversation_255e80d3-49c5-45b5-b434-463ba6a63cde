var Kn=Object.defineProperty;var Xn=(i,t,e)=>t in i?Kn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var kt=(i,t,e)=>(Xn(i,typeof t!="symbol"?t+"":t,e),e);class Yn extends HTMLElement{constructor(){super(),this.addEventListener("click",this.handleClick)}handleClick(t){t.preventDefault(),document.querySelector(t.currentTarget.getAttribute("target-input")).style.display=null,this.remove()}}customElements.define("add-order-note",Yn);const Xt="cubic-bezier(.7, 0, .3, 1)",se="cubic-bezier(.7, 0, 1, 1)",H="cubic-bezier(0, 0, .3, 1)";function z(i,t){let e;const n=(...s)=>{clearTimeout(e),e=setTimeout(()=>i.apply(this,s),t)};return n.cancel=()=>{clearTimeout(e)},n}function ei(i,t){let e=!1;return(...n)=>{e||(i(...n),e=!0,setTimeout(()=>{e=!1},t))}}function Jn(i,t){return t(i),i}function dt(i,t){return(i%t+t)%t}function it(){return window.pageYOffset||document.documentElement.scrollTop}function qt(i="json"){switch(i){case"form-data":return{method:"POST",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}};case"json":return{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"}}}}function Ui(){document.querySelectorAll("product-model").forEach(i=>{i.modelViewerUI&&i.modelViewerUI.pause()})}function Re(){window.setScrollbarWidth()}function Gi(...i){i.filter(t=>t).forEach(t=>t.getAnimations().forEach(e=>e.cancel()))}function j(i){return new Promise(t=>setTimeout(t,i))}window._delay=j;function pt(i){const t=document.createElement("template");return t.innerHTML=i,t.content.firstElementChild}function Ki(i,t=16){const e=window.innerHeight||document.documentElement.clientHeight,n=i.getBoundingClientRect();if(!(n.top>=0&&n.bottom<=e)){if(n.top<0)window.scrollBy({top:n.top-t,behavior:"smooth"});else if(n.height>e){if(n.top>=0&&n.top<=e*.85)return;window.scrollBy({top:n.top-t,behavior:"smooth"})}else if(n.bottom>e){const r=n.bottom-e;window.scrollBy({top:r+t,behavior:"smooth"})}}}async function xe(i){if(Array.isArray(i)){await Promise.all(i.map(t=>xe(t)));return}if(!i.complete)return new Promise(t=>{const e=()=>{i.removeEventListener("load",e),i.removeEventListener("error",e),t()};i.addEventListener("load",e),i.addEventListener("error",e)})}function pe(i){var e;const t=(e=document.activeElement)==null?void 0:e.id;i(),setTimeout(()=>{var n;(n=document.getElementById(t))==null||n.focus()})}function Qn(i,t,e){if(i=Zn(i),i.length===0)return()=>{};const n=new IntersectionObserver(([s],r)=>{s&&s.isIntersecting&&(t(),r.disconnect())},e);return i.forEach(s=>n.observe(s)),()=>{n.disconnect()}}function Zn(i){return Array.isArray(i)?i:[i]}let Xi=!1;window.addEventListener("load",()=>{Xi=!0});function ts(){return Xi?Promise.resolve():new Promise(i=>{window.addEventListener("load",i)})}class es extends HTMLElement{constructor(){super(),this.currentIndex=0,this.autoplay=!0,this.autoplayRunning=!1}get transition(){return this.getAttribute("transition")??"fade"}get speed(){return Number(this.getAttribute("speed")??4e3)}connectedCallback(){this.autoplay=this.getAttribute("autoplay")==="true",this.slides=this.querySelectorAll(".announcement"),this.slides[0].classList.add("is-active"),this.slides[0].animate({opacity:[0,1]},{duration:250}),!(this.slides.length<=1)&&(this.buttonPrev=this.querySelector("[data-button-prev]"),this.buttonNext=this.querySelector("[data-button-next]"),this.buttonPrev.addEventListener("click",this.onPrevClick.bind(this)),this.buttonNext.addEventListener("click",this.onNextClick.bind(this)),this.autoplay&&this.startAutoplay())}disconnectedCallback(){this.stopAutoplay(),this.slides.forEach(t=>{t.classList.remove("is-active"),t.getAnimations().forEach(e=>e.cancel())})}async startAutoplay(){if(!this.autoplayRunning){for(this.autoplayRunning=!0;this.autoplay&&(await j(this.speed),!!this.autoplay);)this.next();this.autoplayRunning=!1}}stopAutoplay(){this.autoplay=!1}onPrevClick(){this.stopAutoplay(),this.prev()}onNextClick(){this.stopAutoplay(),this.next()}async prev(){const t=this.getCurSlide();this.decIndex(),await this.animateTransition(t,this.getCurSlide())}async next(){const t=this.getCurSlide();this.incIndex(),await this.animateTransition(t,this.getCurSlide())}async animateTransition(t,e){var n,s;try{(n=this.currentNextAnim)==null||n.cancel(),(s=this.currentPrevAnim)==null||s.cancel(),this.slides.forEach(r=>r.classList.remove("is-active")),t.classList.add("is-active"),t&&(this.currentPrevAnim=this.animateOutSlide(t),this.transition!=="fade"&&await this.currentPrevAnim.finished,t.classList.remove("is-active")),this.currentNextAnim=this.animateInSlide(e),await this.currentNextAnim.finished,e.classList.add("is-active")}catch{}}getCurSlide(){return this.slides[this.currentIndex]}incIndex(){this.currentIndex=dt(this.currentIndex+1,this.slides.length)}decIndex(){this.currentIndex=dt(this.currentIndex-1,this.slides.length)}animateOutSlide(t){switch(this.transition){case"fade":return t.animate({opacity:[1,0]},{duration:150});case"slide-y":return t.animate({opacity:[1,0],transform:["none","translateY(-.5rem)"]},{duration:200,easing:se})}}animateInSlide(t){switch(this.transition){case"fade":return t.animate({opacity:[0,1]},{duration:150});case"slide-y":return t.animate({opacity:[0,1],transform:["translateY(.5rem)","none"]},{duration:200,easing:H})}}}customElements.define("announcement-bar",es);class Yi extends HTMLElement{constructor(){super(),this.scrollTarget=0,this.currentIndex=0,this.scrollBehavior="smooth",this.userScroll=!1}connectedCallback(){var t,e,n;this.addEventListener("touchstart",()=>{clearTimeout(this.touchendTimeout),this.touching=!0},{passive:!0}),this.addEventListener("touchend",()=>{this.touchendTimeout=setTimeout(()=>{this.touching=!1},100)}),this.sectionId=(t=this.closest(".shopify-section"))==null?void 0:t.id,this.vertical=this.getAttribute("vertical")!==null,this.btnPrev=this.getAttribute("button-prev")?document.querySelector(this.getAttribute("button-prev")):this.parentNode.querySelector(":scope > [data-carousel-prev]"),this.btnNext=this.getAttribute("button-next")?document.querySelector(this.getAttribute("button-next")):this.parentNode.querySelector(":scope > [data-carousel-next]"),this.scrollShadowStart=this.querySelector(".scroll-shadow-start"),this.scrollShadowEnd=this.querySelector(".scroll-shadow-end"),this.items=Array.from(this.querySelectorAll(this.itemSelector)),(e=this.btnPrev)==null||e.addEventListener("click",()=>this.goPrev()),(n=this.btnNext)==null||n.addEventListener("click",()=>this.goNext()),this.onResizeDebounced=z(this.onResize.bind(this),100),this.addEventListener("scroll",this.onScroll.bind(this)),this.addEventListener("scroll",z(this.onScrollEnd.bind(this),150)),this.resizeObserver=new ResizeObserver(this.onResizeDebounced),this.resizeObserver.observe(this),this.updateButtons(),this.updateShadow()}get itemSelector(){return this.getAttribute("item-selector")??":scope > *"}get loop(){return this.getAttribute("loop")!==null}disconnectedCallback(){this.resizeObserver.disconnect()}scrollAmount(){return this.vertical?this.scrollTop:this.scrollLeft}onScroll(){this.touching&&(this.userScroll=!0),this.updateShadow()}onScrollEnd(){this.scrollTarget=this.scrollAmount(),this.updateButtons(),this.userScroll=!1}onResize(){this.scrollTarget=this.scrollAmount(),this.updateButtons(),this.updateShadow()}triggerScroll(t){this.scrollTo({[this.vertical?"top":"left"]:t,behavior:this.scrollBehavior}),this.scrollTarget=t,this.updateButtons()}goPrev(){}goNext(){}updateButtons(){if(!this.btnPrev||!this.btnNext)return;if(this.loop&&this.items.length>1){this.btnPrev.classList.remove("is-hidden"),this.btnNext.classList.remove("is-hidden");return}let t,e;this.vertical?t=this.scrollHeight-this.clientHeight-Math.abs(this.scrollTarget)<3:t=this.scrollWidth-this.clientWidth-Math.abs(this.scrollTarget)<3,e=Math.abs(this.scrollTarget)<3,t?this.btnNext.classList.add("is-hidden"):this.btnNext.classList.remove("is-hidden"),e?this.btnPrev.classList.add("is-hidden"):this.btnPrev.classList.remove("is-hidden")}isOverflowing(){return this.vertical?this.scrollHeight>this.clientHeight:this.scrollWidth>this.clientWidth}updateShadow(){!this.scrollShadowStart||!this.scrollShadowEnd||(this.isOverflowing()?(this.scrollShadowStart.classList.remove("hidden"),this.scrollShadowEnd.classList.remove("hidden")):(this.scrollShadowStart.classList.add("hidden"),this.scrollShadowEnd.classList.add("hidden")),this.style.setProperty("--scroll-progress",this.getScrollProgressRatio()))}getScrollProgressRatio(){return this.vertical?this.scrollTop/(this.scrollHeight-this.clientHeight):Math.abs(this.scrollLeft)/(this.scrollWidth-this.clientWidth)}}function is(i){const t=document.querySelector("[data-main-loading-overlay]");i?t.style.display=null:t.style.display="none"}async function ns(i){const t=Array.isArray(i)?i:Array.from(i),e=r=>r.complete&&r.naturalWidth!==0&&r.naturalHeight!==0,n=r=>new Promise((a,o)=>{const l=()=>{r.removeEventListener("load",l),r.removeEventListener("error",c),a(r)},c=f=>{r.removeEventListener("load",l),r.removeEventListener("error",c),o(f)};e(r)?a(r):(r.addEventListener("load",l),r.addEventListener("error",c))}),s=t.map(r=>n(r));try{await Promise.all(s)}catch(r){throw console.error("One or more images failed to load:",r),r}}function De(i){return new Promise(t=>{const e=document.createElement("link");e.rel="preload",e.as="image",e.href=i.src,e.imageSrcset=i.srcset,e.imageSizes=i.sizes,document.head.appendChild(e),e.onload=()=>{document.head.removeChild(e),t()},e.onerror=()=>{document.head.removeChild(e),t()}})}async function Ji(i){await document.querySelector("notification-wrapper").showNotification(i)}function re(i){return i.id.replace(/^shopify-section-/,"")}function Qi(i,t=".shopify-section"){return new DOMParser().parseFromString(i,"text/html").querySelector(t)}async function xt(i,{url:t,signal:e}={}){const n=await ss(re(i),t,e);Zi(i,n)}async function ss(i,t=null,e=null){t||(t=window.location.origin+window.location.pathname),t=new URL(t),t.searchParams.append("section_id",i);const n=await fetch(t.toString(),{signal:e});if(!n.ok)throw new Error(`Failed to fetch section #${i}`);return await n.text()}function Zi(i,t){tn(i,()=>{i.innerHTML=Qi(t,"#"+i.id).innerHTML})}function tn(i,t){const e=rs(i),n=as(i);t(),Object.entries(e).forEach(([s,r])=>{var a;(a=document.getElementById(s))==null||a.loadComponentState(r)}),Object.entries(n).forEach(([s,r])=>{var a;(a=document.getElementById(s))==null||a.scrollTo(r)})}function rs(i){return Object.fromEntries([...i.querySelectorAll("[data-preserve-state]")].map(t=>[t.id,t.saveComponentState()]))}function as(i){return Object.fromEntries([...i.querySelectorAll("[data-preserve-scroll]")].map(t=>[t.id,{top:t.scrollTop,left:t.scrollLeft}]))}async function os({id:i,quantity:t}){const e=await fetch(window.routes.cart_add_url,{...qt(),body:JSON.stringify({id:i,quantity:t,sections:["cart-bubble","variant-added"],sections_url:`${window.Shopify.routes.root}variants/${i}`})});if(!e.ok){const r=await e.json();return Ji({type:"danger",text:(r==null?void 0:r.description)||(r==null?void 0:r.message)||window.cartStrings.error}),!1}const n=await e.json();qe(n.sections["cart-bubble"].match(/\d+/));const s=document.querySelector(".section-main-cart");return s&&xt(s),document.dispatchEvent(new CustomEvent("product:added-to-cart",{detail:{id:i,quantity:t}})),n}function qe(i){const t=document.getElementById("CartBubble");i?(t.classList.remove("hidden"),t.innerHTML=i):(t.classList.add("hidden"),t.innerHTML="")}async function ls(i){window.addedToCartNotification==="drawer"?await en():await nn(i)}async function en(i={}){var t,e;await((t=document.querySelector("cart-modal"))==null?void 0:t.reloadContent()),(e=document.querySelector("cart-modal"))==null||e.show(i)}function cs(i){const t=document.getElementById("quick-add-modal");t.setProductInfo(i),t.show()}async function nn({html:i,key:t}){const e=document.getElementById("variant-added-modal"),n=e.querySelector("[slot=content]"),s=pt(i);t&&s.querySelectorAll(`[data-cart-key]:not([data-cart-key="${t}"])`).forEach(r=>r.remove()),await Promise.all([...s.querySelectorAll("img")].map(r=>De(r))),n.replaceChildren(s),e.show()}class ds extends HTMLElement{constructor(){super(),this.debouncedOnChange=z(t=>{this.onChange(t)},300),this.addEventListener("change",this.debouncedOnChange.bind(this))}connectedCallback(){this.querySelectorAll("[data-button-remove]").forEach(t=>t.addEventListener("click",this.onRemoveButtonClick.bind(this))),this.cartModal=this.closest("cart-modal"),this.parentSection=this.closest(".shopify-section")}onRemoveButtonClick(t){return t.preventDefault(),this.updateQuantity(t.currentTarget.dataset.index,0)}onChange(t){if(t.target.id==="Cart-Note"){const e=JSON.stringify({note:t.target.value});fetch(`${window.routes.cart_update_url}`,{...qt(),body:e})}else t.target.closest("qty-selector")&&this.updateQuantity(t.target.dataset.index,t.target.value)}async updateQuantity(t,e){this.setLoading(!0);const n=this.closest(".shopify-section"),s=re(n),r=JSON.stringify({line:t,quantity:e,sections:[s,"cart-bubble"],sections_url:window.location.pathname});try{const a=await fetch(`${window.routes.cart_change_url}`,{...qt(),body:r}),o=await a.json();if(a.ok)this.cartModal?this.cartModal.updateFromSectionHtml(o.sections[s]):Zi(n,o.sections[s]),qe(o.sections["cart-bubble"].match(/\d+/));else if(o.description||o.message)await this.reloadParentSection(),this.displayError(o.description||o.message,t);else throw new Error("Unknown error response from cart api")}catch(a){console.error(a),await this.reloadParentSection(),this.displayError(window.cartStrings.error,t)}finally{this.setLoading(!1)}}async reloadParentSection(){this.cartModal?await this.cartModal.reloadContent():await xt(this.closest(".shopify-section"))}setLoading(t){this.cartModal?t?this.cartModal.showLoadingOverlay():this.cartModal.hideLoadingOverlay():is(t)}displayError(t,e=null){let n=this.parentSection.querySelector("#Cart-Errors");e!==null&&(n=this.parentSection.querySelector(`[data-cart-item-index="${e}"] .message-danger`)),n.textContent=t}}customElements.define("cart-form",ds);function sn(i){if(!ii[i])throw new Error(`Unknown animation: ${i}`);return ii[i]}function P(i,t,e={}){return sn(t)(i,e)}const ii={"fade-in":function(t,e={}){const n={opacity:[0,1],transform:[`translate(${e.x??0}, ${e.y??"1rem"})`,"none"]},s={duration:375,easing:H,...e};return t.animate(n,s)},"fade-out":function(t,e={}){const n={opacity:[1,0],transform:["none",`translate(${e.x??0}, ${e.y??"1rem"})`]},s={duration:375,easing:se,...e};return t.animate(n,s)},"scroll-slide-in":function(t,e={}){const n={opacity:[0,1],transform:[`translate(${e.x??0}, ${e.y??"2rem"})`,"none"]},s={duration:600,easing:H,...e};return t.animate(n,s)},"clip-inset":function(t,e={}){const n=e.round??"0",s=e.clipDirection??"bottom";let r=null;switch(s){case"top":r=`inset(100% 0 0 0 round ${n})`;break;case"right":r=`inset(0 100% 0 0 round ${n})`;break;case"bottom":r=`inset(0 0 100% 0 round ${n})`;break;case"left":r=`inset(0 0 0 100% round ${n})`;break}const a=`inset(0 0 0 0 round ${n})`,o={clipPath:[r,a]},l={duration:250,easing:Xt,...e};return t.animate(o,l)},"scale-in":function(t,e={}){const s={transform:[`scale(${e.scale||.9})`,"none"],opacity:[0,1]},r={duration:375,easing:H,...e};return t.animate(s,r)},"scale-out":function(t,e={}){const s={transform:["none",`scale(${e.scale||.95})`],opacity:[1,0]},r={duration:375,easing:"linear",...e};return t.animate(s,r)}};function rn(){document.body.classList.add("no-scroll")}function _e(){document.body.classList.remove("no-scroll")}/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var an=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Yt=an.join(","),on=typeof Element>"u",ht=on?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Jt=!on&&Element.prototype.getRootNode?function(i){var t;return i==null||(t=i.getRootNode)===null||t===void 0?void 0:t.call(i)}:function(i){return i==null?void 0:i.ownerDocument},Qt=function i(t,e){var n;e===void 0&&(e=!0);var s=t==null||(n=t.getAttribute)===null||n===void 0?void 0:n.call(t,"inert"),r=s===""||s==="true",a=r||e&&t&&i(t.parentNode);return a},hs=function(t){var e,n=t==null||(e=t.getAttribute)===null||e===void 0?void 0:e.call(t,"contenteditable");return n===""||n==="true"},ln=function(t,e,n){if(Qt(t))return[];var s=Array.prototype.slice.apply(t.querySelectorAll(Yt));return e&&ht.call(t,Yt)&&s.unshift(t),s=s.filter(n),s},cn=function i(t,e,n){for(var s=[],r=Array.from(t);r.length;){var a=r.shift();if(!Qt(a,!1))if(a.tagName==="SLOT"){var o=a.assignedElements(),l=o.length?o:a.children,c=i(l,!0,n);n.flatten?s.push.apply(s,c):s.push({scopeParent:a,candidates:c})}else{var f=ht.call(a,Yt);f&&n.filter(a)&&(e||!t.includes(a))&&s.push(a);var m=a.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(a),y=!Qt(m,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(a));if(m&&y){var v=i(m===!0?a.children:m.children,!0,n);n.flatten?s.push.apply(s,v):s.push({scopeParent:a,candidates:v})}else r.unshift.apply(r,a.children)}}return s},dn=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},ct=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||hs(t))&&!dn(t)?0:t.tabIndex},us=function(t,e){var n=ct(t);return n<0&&e&&!dn(t)?0:n},fs=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},hn=function(t){return t.tagName==="INPUT"},ps=function(t){return hn(t)&&t.type==="hidden"},ms=function(t){var e=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(n){return n.tagName==="SUMMARY"});return e},gs=function(t,e){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===e)return t[n]},ys=function(t){if(!t.name)return!0;var e=t.form||Jt(t),n=function(o){return e.querySelectorAll('input[type="radio"][name="'+o+'"]')},s;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")s=n(window.CSS.escape(t.name));else try{s=n(t.name)}catch(a){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",a.message),!1}var r=gs(s,t.form);return!r||r===t},vs=function(t){return hn(t)&&t.type==="radio"},bs=function(t){return vs(t)&&!ys(t)},ws=function(t){var e,n=t&&Jt(t),s=(e=n)===null||e===void 0?void 0:e.host,r=!1;if(n&&n!==t){var a,o,l;for(r=!!((a=s)!==null&&a!==void 0&&(o=a.ownerDocument)!==null&&o!==void 0&&o.contains(s)||t!=null&&(l=t.ownerDocument)!==null&&l!==void 0&&l.contains(t));!r&&s;){var c,f,m;n=Jt(s),s=(c=n)===null||c===void 0?void 0:c.host,r=!!((f=s)!==null&&f!==void 0&&(m=f.ownerDocument)!==null&&m!==void 0&&m.contains(s))}}return r},ni=function(t){var e=t.getBoundingClientRect(),n=e.width,s=e.height;return n===0&&s===0},Ss=function(t,e){var n=e.displayCheck,s=e.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var r=ht.call(t,"details>summary:first-of-type"),a=r?t.parentElement:t;if(ht.call(a,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof s=="function"){for(var o=t;t;){var l=t.parentElement,c=Jt(t);if(l&&!l.shadowRoot&&s(l)===!0)return ni(t);t.assignedSlot?t=t.assignedSlot:!l&&c!==t.ownerDocument?t=c.host:t=l}t=o}if(ws(t))return!t.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return ni(t);return!1},Es=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if(e.tagName==="FIELDSET"&&e.disabled){for(var n=0;n<e.children.length;n++){var s=e.children.item(n);if(s.tagName==="LEGEND")return ht.call(e,"fieldset[disabled] *")?!0:!s.contains(t)}return!0}e=e.parentElement}return!1},Zt=function(t,e){return!(e.disabled||Qt(e)||ps(e)||Ss(e,t)||ms(e)||Es(e))},Ae=function(t,e){return!(bs(e)||ct(e)<0||!Zt(t,e))},Cs=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},xs=function i(t){var e=[],n=[];return t.forEach(function(s,r){var a=!!s.scopeParent,o=a?s.scopeParent:s,l=us(o,a),c=a?i(s.candidates):o;l===0?a?e.push.apply(e,c):e.push(o):n.push({documentOrder:r,tabIndex:l,item:s,isScope:a,content:c})}),n.sort(fs).reduce(function(s,r){return r.isScope?s.push.apply(s,r.content):s.push(r.content),s},[]).concat(e)},As=function(t,e){e=e||{};var n;return e.getShadowRoot?n=cn([t],e.includeContainer,{filter:Ae.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:Cs}):n=ln(t,e.includeContainer,Ae.bind(null,e)),xs(n)},Ts=function(t,e){e=e||{};var n;return e.getShadowRoot?n=cn([t],e.includeContainer,{filter:Zt.bind(null,e),flatten:!0,getShadowRoot:e.getShadowRoot}):n=ln(t,e.includeContainer,Zt.bind(null,e)),n},yt=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return ht.call(t,Yt)===!1?!1:Ae(e,t)},Ls=an.concat("iframe").join(","),me=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return ht.call(t,Ls)===!1?!1:Zt(e,t)};/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function si(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);t&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(i,s).enumerable})),e.push.apply(e,n)}return e}function ri(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?si(Object(e),!0).forEach(function(n){Is(i,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):si(Object(e)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(e,n))})}return i}function Is(i,t,e){return t=Ms(t),t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function ks(i,t){if(typeof i!="object"||i===null)return i;var e=i[Symbol.toPrimitive];if(e!==void 0){var n=e.call(i,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(i)}function Ms(i){var t=ks(i,"string");return typeof t=="symbol"?t:String(t)}var ai={activateTrap:function(t,e){if(t.length>0){var n=t[t.length-1];n!==e&&n.pause()}var s=t.indexOf(e);s===-1||t.splice(s,1),t.push(e)},deactivateTrap:function(t,e){var n=t.indexOf(e);n!==-1&&t.splice(n,1),t.length>0&&t[t.length-1].unpause()}},Os=function(t){return t.tagName&&t.tagName.toLowerCase()==="input"&&typeof t.select=="function"},Ps=function(t){return(t==null?void 0:t.key)==="Escape"||(t==null?void 0:t.key)==="Esc"||(t==null?void 0:t.keyCode)===27},Rt=function(t){return(t==null?void 0:t.key)==="Tab"||(t==null?void 0:t.keyCode)===9},Rs=function(t){return Rt(t)&&!t.shiftKey},Ds=function(t){return Rt(t)&&t.shiftKey},oi=function(t){return setTimeout(t,0)},li=function(t,e){var n=-1;return t.every(function(s,r){return e(s)?(n=r,!1):!0}),n},Mt=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),s=1;s<e;s++)n[s-1]=arguments[s];return typeof t=="function"?t.apply(void 0,n):t},Ut=function(t){return t.target.shadowRoot&&typeof t.composedPath=="function"?t.composedPath()[0]:t.target},qs=[],_s=function(t,e){var n=(e==null?void 0:e.document)||document,s=(e==null?void 0:e.trapStack)||qs,r=ri({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Rs,isKeyBackward:Ds},e),a={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},o,l=function(h,u,w){return h&&h[u]!==void 0?h[u]:r[w||u]},c=function(h,u){var w=typeof(u==null?void 0:u.composedPath)=="function"?u.composedPath():void 0;return a.containerGroups.findIndex(function(A){var T=A.container,L=A.tabbableNodes;return T.contains(h)||(w==null?void 0:w.includes(T))||L.find(function(O){return O===h})})},f=function(h){var u=r[h];if(typeof u=="function"){for(var w=arguments.length,A=new Array(w>1?w-1:0),T=1;T<w;T++)A[T-1]=arguments[T];u=u.apply(void 0,A)}if(u===!0&&(u=void 0),!u){if(u===void 0||u===!1)return u;throw new Error("`".concat(h,"` was specified but was not a node, or did not return a node"))}var L=u;if(typeof u=="string"&&(L=n.querySelector(u),!L))throw new Error("`".concat(h,"` as selector refers to no known node"));return L},m=function(){var h=f("initialFocus");if(h===!1)return!1;if(h===void 0||!me(h,r.tabbableOptions))if(c(n.activeElement)>=0)h=n.activeElement;else{var u=a.tabbableGroups[0],w=u&&u.firstTabbableNode;h=w||f("fallbackFocus")}if(!h)throw new Error("Your focus-trap needs to have at least one focusable element");return h},y=function(){if(a.containerGroups=a.containers.map(function(h){var u=As(h,r.tabbableOptions),w=Ts(h,r.tabbableOptions),A=u.length>0?u[0]:void 0,T=u.length>0?u[u.length-1]:void 0,L=w.find(function(R){return yt(R)}),O=w.slice().reverse().find(function(R){return yt(R)}),N=!!u.find(function(R){return ct(R)>0});return{container:h,tabbableNodes:u,focusableNodes:w,posTabIndexesFound:N,firstTabbableNode:A,lastTabbableNode:T,firstDomTabbableNode:L,lastDomTabbableNode:O,nextTabbableNode:function(ot){var Lt=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,et=u.indexOf(ot);return et<0?Lt?w.slice(w.indexOf(ot)+1).find(function(It){return yt(It)}):w.slice(0,w.indexOf(ot)).reverse().find(function(It){return yt(It)}):u[et+(Lt?1:-1)]}}}),a.tabbableGroups=a.containerGroups.filter(function(h){return h.tabbableNodes.length>0}),a.tabbableGroups.length<=0&&!f("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(a.containerGroups.find(function(h){return h.posTabIndexesFound})&&a.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},v=function k(h){var u=h.activeElement;if(u)return u.shadowRoot&&u.shadowRoot.activeElement!==null?k(u.shadowRoot):u},b=function k(h){if(h!==!1&&h!==v(document)){if(!h||!h.focus){k(m());return}h.focus({preventScroll:!!r.preventScroll}),a.mostRecentlyFocusedNode=h,Os(h)&&h.select()}},d=function(h){var u=f("setReturnFocus",h);return u||(u===!1?!1:h)},g=function(h){var u=h.target,w=h.event,A=h.isBackward,T=A===void 0?!1:A;u=u||Ut(w),y();var L=null;if(a.tabbableGroups.length>0){var O=c(u,w),N=O>=0?a.containerGroups[O]:void 0;if(O<0)T?L=a.tabbableGroups[a.tabbableGroups.length-1].lastTabbableNode:L=a.tabbableGroups[0].firstTabbableNode;else if(T){var R=li(a.tabbableGroups,function(ue){var fe=ue.firstTabbableNode;return u===fe});if(R<0&&(N.container===u||me(u,r.tabbableOptions)&&!yt(u,r.tabbableOptions)&&!N.nextTabbableNode(u,!1))&&(R=O),R>=0){var ot=R===0?a.tabbableGroups.length-1:R-1,Lt=a.tabbableGroups[ot];L=ct(u)>=0?Lt.lastTabbableNode:Lt.lastDomTabbableNode}else Rt(w)||(L=N.nextTabbableNode(u,!1))}else{var et=li(a.tabbableGroups,function(ue){var fe=ue.lastTabbableNode;return u===fe});if(et<0&&(N.container===u||me(u,r.tabbableOptions)&&!yt(u,r.tabbableOptions)&&!N.nextTabbableNode(u))&&(et=O),et>=0){var It=et===a.tabbableGroups.length-1?0:et+1,ti=a.tabbableGroups[It];L=ct(u)>=0?ti.firstTabbableNode:ti.firstDomTabbableNode}else Rt(w)||(L=N.nextTabbableNode(u))}}else L=f("fallbackFocus");return L},p=function(h){var u=Ut(h);if(!(c(u,h)>=0)){if(Mt(r.clickOutsideDeactivates,h)){o.deactivate({returnFocus:r.returnFocusOnDeactivate});return}Mt(r.allowOutsideClick,h)||h.preventDefault()}},S=function(h){var u=Ut(h),w=c(u,h)>=0;if(w||u instanceof Document)w&&(a.mostRecentlyFocusedNode=u);else{h.stopImmediatePropagation();var A,T=!0;if(a.mostRecentlyFocusedNode)if(ct(a.mostRecentlyFocusedNode)>0){var L=c(a.mostRecentlyFocusedNode),O=a.containerGroups[L].tabbableNodes;if(O.length>0){var N=O.findIndex(function(R){return R===a.mostRecentlyFocusedNode});N>=0&&(r.isKeyForward(a.recentNavEvent)?N+1<O.length&&(A=O[N+1],T=!1):N-1>=0&&(A=O[N-1],T=!1))}}else a.containerGroups.some(function(R){return R.tabbableNodes.some(function(ot){return ct(ot)>0})})||(T=!1);else T=!1;T&&(A=g({target:a.mostRecentlyFocusedNode,isBackward:r.isKeyBackward(a.recentNavEvent)})),b(A||a.mostRecentlyFocusedNode||m())}a.recentNavEvent=void 0},E=function(h){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;a.recentNavEvent=h;var w=g({event:h,isBackward:u});w&&(Rt(h)&&h.preventDefault(),b(w))},C=function(h){if(Ps(h)&&Mt(r.escapeDeactivates,h)!==!1){h.preventDefault(),o.deactivate();return}(r.isKeyForward(h)||r.isKeyBackward(h))&&E(h,r.isKeyBackward(h))},x=function(h){var u=Ut(h);c(u,h)>=0||Mt(r.clickOutsideDeactivates,h)||Mt(r.allowOutsideClick,h)||(h.preventDefault(),h.stopImmediatePropagation())},I=function(){if(a.active)return ai.activateTrap(s,o),a.delayInitialFocusTimer=r.delayInitialFocus?oi(function(){b(m())}):b(m()),n.addEventListener("focusin",S,!0),n.addEventListener("mousedown",p,{capture:!0,passive:!1}),n.addEventListener("touchstart",p,{capture:!0,passive:!1}),n.addEventListener("click",x,{capture:!0,passive:!1}),n.addEventListener("keydown",C,{capture:!0,passive:!1}),o},_=function(){if(a.active)return n.removeEventListener("focusin",S,!0),n.removeEventListener("mousedown",p,!0),n.removeEventListener("touchstart",p,!0),n.removeEventListener("click",x,!0),n.removeEventListener("keydown",C,!0),o},W=function(h){var u=h.some(function(w){var A=Array.from(w.removedNodes);return A.some(function(T){return T===a.mostRecentlyFocusedNode})});u&&b(m())},B=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(W):void 0,tt=function(){B&&(B.disconnect(),a.active&&!a.paused&&a.containers.map(function(h){B.observe(h,{subtree:!0,childList:!0})}))};return o={get active(){return a.active},get paused(){return a.paused},activate:function(h){if(a.active)return this;var u=l(h,"onActivate"),w=l(h,"onPostActivate"),A=l(h,"checkCanFocusTrap");A||y(),a.active=!0,a.paused=!1,a.nodeFocusedBeforeActivation=n.activeElement,u==null||u();var T=function(){A&&y(),I(),tt(),w==null||w()};return A?(A(a.containers.concat()).then(T,T),this):(T(),this)},deactivate:function(h){if(!a.active)return this;var u=ri({onDeactivate:r.onDeactivate,onPostDeactivate:r.onPostDeactivate,checkCanReturnFocus:r.checkCanReturnFocus},h);clearTimeout(a.delayInitialFocusTimer),a.delayInitialFocusTimer=void 0,_(),a.active=!1,a.paused=!1,tt(),ai.deactivateTrap(s,o);var w=l(u,"onDeactivate"),A=l(u,"onPostDeactivate"),T=l(u,"checkCanReturnFocus"),L=l(u,"returnFocus","returnFocusOnDeactivate");w==null||w();var O=function(){oi(function(){L&&b(d(a.nodeFocusedBeforeActivation)),A==null||A()})};return L&&T?(T(d(a.nodeFocusedBeforeActivation)).then(O,O),this):(O(),this)},pause:function(h){if(a.paused||!a.active)return this;var u=l(h,"onPause"),w=l(h,"onPostPause");return a.paused=!0,u==null||u(),_(),tt(),w==null||w(),this},unpause:function(h){if(!a.paused||!a.active)return this;var u=l(h,"onUnpause"),w=l(h,"onPostUnpause");return a.paused=!1,u==null||u(),y(),I(),tt(),w==null||w(),this},updateContainerElements:function(h){var u=[].concat(h).filter(Boolean);return a.containers=u.map(function(w){return typeof w=="string"?n.querySelector(w):w}),a.active&&y(),tt(),this}},o.updateContainerElements(t),o};window.activeModals=[];class Ft extends HTMLElement{constructor(){super();const t=this.attachShadow({mode:"open"});t.appendChild(document.querySelector("#template-modal").content.cloneNode(!0)),this.open=!1,this.overlay=t.querySelector(".overlay"),this.panelWrapper=t.querySelector(".panel-wrapper"),this.panel=t.querySelector(".panel"),this.panelInner=t.querySelector(".panel-inner"),this.loadingOverlay=t.querySelector(".loading-overlay"),this.spinner=t.querySelector(".spinner"),this.activator=t.querySelector("slot[name=activator]"),this.useFocusTrap=!0,this.useLockScroll=!0,this.useOverlay=!0}get appendToBody(){return this.getAttribute("append-to-body")!==null}connectedCallback(){this.hasAttribute("disable-focus-trap")&&(this.useFocusTrap=!1,this.focusTrap=null),this.content=this.shadowRoot.querySelector("slot[name=content]").assignedElements()[0]??null,this.activatorElement=this.activator.assignedElements()[0]??null,!this.activatorElement&&this.id&&(this.activatorElement=document.querySelector(`modal-trigger[target="#${this.id}"] > *`)),this.activator.addEventListener("click",t=>this.onActivatorClick(t)),this.overlay.addEventListener("click",()=>this.hide()),this.addEventListener("keydown",this.onEscapeKeyDown.bind(this)),this.registerCloseButton(),this.getAttribute("show-on")&&this.content.querySelector(this.getAttribute("show-on"))&&this.show(),this.getAttribute("hide-scrollbar")!==null&&this.panel.classList.add("hide-scrollbar"),window.Shopify.designMode&&this.appendToBody&&this.closest(".shopify-section").addEventListener("shopify:section:unload",()=>{this.remove()})}disconnectedCallback(){this.designModeInit&&this.disableDesignMode()}onEscapeKeyDown(t){this.open&&t.key==="Escape"&&(t.stopPropagation(),this.hide())}saveComponentState(){return{open:this.open}}loadComponentState(t){t!=null&&t.open&&this.show({anim:!1})}setActivatorElement(t){this.activatorElement=t}registerCloseButton(){this.addEventListener("click",t=>{t.target.closest("[data-button-close]")&&(t.stopPropagation(),this.hide())}),this.addEventListener("keydown",t=>{t.target.getAttribute("data-button-close")!==null&&t.key==="Enter"&&(t.stopPropagation(),this.hide())})}onActivatorClick(t){t.preventDefault(),this.show()}async beforeShow(){var e;const t=window.activeModals[window.activeModals.length-1];t?(this.inModalMode=!0,this.setZIndex(t.getZIndexMax()+100),this.parentModal=t):this.inModalMode=!1,!this.focusTrap&&this.useFocusTrap&&!window.Shopify.designMode&&(this.focusTrap=_s(this.content,{allowOutsideClick:!0,escapeDeactivates:!1,preventScroll:!0,initialFocus:this.getInitialFocus()})),window.activeModals.push(this),this.appendToBody&&(this.storeOriginalPosition(),document.body.appendChild(this)),this.open=!0,(e=this.activatorElement)==null||e.classList.add("active"),this.dispatchEvent(new CustomEvent("show")),this.lockScroll()}getInitialFocus(){const t=this.getAttribute("initial-focus");return t?this.content.querySelector(t):this.content}async show(t=!0){this.open||(await this.beforeShow(),await this.showElements(t),await this.afterShow())}async showElements({anim:t=!0}={}){this.fadeInOverlay(t),this.panelWrapper.style.display="flex",t&&await this.animatePanelIn()}async animatePanelIn(){await P(this.panel,"fade-in",{duration:250,y:"3rem"}).finished}async afterShow(){this.useFocusTrap&&this.focusTrap?this.focusTrap.activate():this.content.getAttribute("tabindex")&&this.content.focus()}async beforeHide(){var t;window.activeModals=window.activeModals.filter(e=>e!==this),this.focusTrap&&this.focusTrap.deactivate(),this.open=!1,(t=this.activatorElement)==null||t.classList.remove("active"),this.dispatchEvent(new CustomEvent("hide"))}async hide(){this.open&&(await this.beforeHide(),await this.hideElements(),await this.afterHide())}async hideElements(t=!0){t&&await this.animatePanelOut(),this.panelWrapper.style.display=null,await this.fadeOutOverlay()}async animatePanelOut(){await P(this.panel,"fade-out",{easing:"linear",duration:150}).finished}async afterHide(){var t;this.content.querySelectorAll(".modal").forEach(e=>e.hide&&e.hide()),this.unlockScroll(),this.appendToBody&&((t=this.originalPosition)!=null&&t.parent)&&this.restoreOriginalPosition(),this.dispatchEvent(new CustomEvent("hide-end"))}storeOriginalPosition(){this.originalPosition={parent:this.parentElement,nextSibling:this.nextSibling}}restoreOriginalPosition(){this.originalPosition.parent.insertBefore(this,this.originalPosition.nextSibling)}async fadeInOverlay(t=!0){this.useOverlay&&(this.overlay.style.display="block",t&&await this.overlay.animate({opacity:[0,1]},{duration:200}).finished)}async fadeOutOverlay(){this.useOverlay&&(await this.overlay.animate({opacity:[1,0]},{duration:200}).finished,this.overlay.style.display="none")}lockScroll(){this.useLockScroll&&rn()}unlockScroll(){this.useLockScroll&&(!this.inModalMode||this.parentModal&&!this.parentModal.useLockScroll)&&(_e(),setTimeout(Re,250))}showLoadingOverlay(){this.loadingOverlay.style.display=null}hideLoadingOverlay(){this.loadingOverlay.style.display="none"}setZIndex(t){this.overlay.style.zIndex=t,this.panelWrapper.style.zIndex=t+100}getZIndexMax(){return Number(getComputedStyle(this.panelWrapper).zIndex)}initDesignMode(){const t=this.closest(".shopify-section");this.onShopifySectionLoaded=this.onShopifySectionLoaded.bind(this),this.onShopifySectionSelected=this.onShopifySectionSelected.bind(this),this.onShopifySectionDeselected=this.onShopifySectionDeselected.bind(this),t.addEventListener("shopify:section:load",this.onShopifySectionLoaded),t.addEventListener("shopify:section:select",this.onShopifySectionSelected),t.addEventListener("shopify:section:deselect",this.onShopifySectionDeselected),this.designModeInit=!0}disableDesignMode(){const t=this.closest(".shopify-section");t.removeEventListener("shopify:section:load",this.onShopifySectionLoaded),t.removeEventListener("shopify:section:select",this.onShopifySectionSelected),t.removeEventListener("shopify:section:deselect",this.onShopifySectionDeselected),this.designModeInit=!1}onShopifySectionLoaded(){this.show()}onShopifySectionSelected({detail:{load:t}}){this.show({anim:!t,load:t})}onShopifySectionDeselected(){this.hide()}}customElements.define("x-modal",Ft);const Ns={sm:"576px",md:"768px",lg:"992px",xl:"1200px",hd:"1400px"},vt=Object.fromEntries(Object.entries(Ns).map(([i,t])=>[i,parseInt(t)]));function Hs(){return Object.keys(vt).sort((i,t)=>vt[t]-vt[i])}function Bs(i){const t=window.innerWidth;for(let e=0;e<i.length;e++){const n=i[e],s=vt[n];if(t>=s)return n}return null}function Te(i,t){const e=Hs(),n=Bs(e);if(!n)return i.getAttribute(t);const s=e.indexOf(n);for(let r=s;r<e.length;r++){const a=`${e[r]}:${t}`;if(i.hasAttribute(a))return i.getAttribute(a)}return i.getAttribute(t)}function un(i){return window.innerWidth<vt[i]}function Fs(i){return window.innerWidth>=vt[i]}class At extends Ft{constructor(){super(),this.isRoot=!0,this.isChild=!1,this.fadeOnMobile=!0,this.parentDrawer=null,this.activeChildDrawer=null}connectedCallback(){super.connectedCallback(),this.getAttribute("child")!==null&&(this.parentDrawer=this.parentNode.closest("modal-drawer"),this.isChild=!0)}onEscapeKeyDown(t){this.isChild||super.onEscapeKeyDown(t)}getPosition(){return Te(this,"position")??"right"}getPositionInverse(){switch(this.getPosition()){case"top":return"bottom";case"bottom":return"top";case"left":return"right";case"right":return"left"}}async show({anim:t=!0,focusTrap:e=!0}={}){if(!this.open){if(await this.beforeShow(),this.isChild&&this.parentDrawer)if(Object.assign(this.parentDrawer.panel.style,{borderTopRightRadius:0,borderBottomRightRadius:0,overflow:"visible"}),Object.assign(this.panel.style,{borderTopLeftRadius:0,borderBottomLeftRadius:0}),this.parentDrawer.activeChildDrawer){await this.parentDrawer.activeChildDrawer.animOutContentsAndHide(),this.parentDrawer.activeChildDrawer=this,await this.animInPanelContents();return}else this.parentDrawer.activeChildDrawer=this;this.isChild||(this.fadeInOverlay(t),this.lockScroll()),t?await this.animInPanel():this.panelWrapper.style.display="flex",!this.isChild&&e&&this.focusTrap&&this.focusTrap.activate()}}async hide({anim:t=!0}={}){if(!this.open)return;await this.beforeHide();const e=[];t?e.push(this.animOutPanel()):this.panelWrapper.style.display=null,this.activeChildDrawer&&(e.push(this.activeChildDrawer.hide({anim:t})),this.activeChildDrawer=null),await Promise.all(e),this.panel.removeAttribute("style"),this.isChild||await this.fadeOutOverlay(),this.afterHide()}async animInPanel(){this.panelWrapper.style.display="flex",this.clientHeight,this.panel.scrollTo(0,0);let t=null;t=P(this.panel,"fade-in",{...this.getFadeXY("50%"),duration:400,easing:"ease"}),P(this.panelInner,"fade-in",{...this.getFadeXY("1rem"),duration:400,easing:"ease"}),await t.finished}getCornerRadius(){return this.isChild?"0":"var(--block-corner-radius)"}async animInPanelContents(){this.panelWrapper.style.display="flex",await this.panelInner.animate({opacity:[0,1],transform:["translateY(-.5rem)","none"]},{duration:125,easing:"ease-out",fill:"backwards"}).finished}async animOutPanel(){this.isChild?(await j(175),this.panelWrapper.style.display=null):(await P(this.panel,"fade-out",{duration:150,easing:"linear",...this.getFadeXY()}).finished,this.panelWrapper.style.display=null)}getFadeXY(t="1rem"){switch(this.getPosition()){case"top":return{x:0,y:`-${t}`};case"bottom":return{x:0,y:`${t}`};case"left":return{x:`-${t}`,y:0};case"right":return{x:`${t}`,y:0}}}async animOutContentsAndHide(){await this.panelInner.animate({opacity:[1,0],transform:["none","translateY(.5rem)"]},{duration:125,easing:"ease-in"}).finished,this.hide({anim:!1})}get animation(){return this.getAttribute("animation")}}customElements.define("modal-drawer",At);class $s extends At{constructor(){super(),this.reloadContent=this.reloadContent.bind(this)}connectedCallback(){super.connectedCallback(),document.addEventListener("product:added-to-cart",this.reloadContent),this.addEventListener("order-note:updated",this.onOrderNoteUpdated.bind(this)),window.Shopify.designMode&&this.initDesignMode()}disconnectedCallback(){super.disconnectedCallback&&super.disconnectedCallback(),document.removeEventListener("product:added-to-cart",this.reloadContent)}get sectionId(){return re(this.closest(".shopify-section"))}async reloadContent(){const e=await(await fetch(`${window.location.pathname}?section_id=${this.sectionId}`)).text();this.updateFromSectionHtml(e)}updateFromSectionHtml(t){const e=pt(t);tn(this,()=>{this.content.replaceChildren(...e.querySelector("[slot=content]").children)})}onOrderNoteUpdated(t){const e=this.content.querySelector(".order-note-button-label");t.detail.note.trim().length>0?e.textContent=window._t.sections.cart.edit_note:e.textContent=window._t.sections.cart.add_note}}customElements.define("cart-modal",$s);class Ws extends HTMLElement{connectedCallback(){this.addEventListener("click",this.copyToClipboard.bind(this))}async copyToClipboard(){await navigator.clipboard.writeText(this.dataset.text),await Ji({text:window._t.share.copied_to_clipboard,time:3e3})}}customElements.define("copy-to-clipboard",Ws);class fn extends HTMLElement{constructor(){super(),this.poster=this.querySelector("[data-poster]"),this.poster&&this.poster.addEventListener("click",this.loadContent.bind(this))}loadContent(t=!0){if(this.poster.style.display="none",Ui(),!this.loaded){const e=document.createElement("div");e.appendChild(this.querySelector("template").content.firstElementChild.cloneNode(!0)),this.loaded=!0,this.setAttribute("loaded",!0);const n=this.appendChild(e.querySelector("video, model-viewer, iframe"));t&&n.focus(),n.tagName==="VIDEO"&&n.play()}}}customElements.define("deferred-media",fn);class Ne extends HTMLElement{constructor(){super(),this.innerHTML='<div class="dots-indicator"></div>',this.itemCount=0,this.dotsContainer=null,this.controlledElement=null}connectedCallback(){this.itemCount=this.getAttribute("item-count"),this.dotsContainer=this.querySelector(".dots-indicator"),this.createDots();const t=this.getAttribute("aria-controls");t&&(this.controlledElement=document.getElementById(t),this.controlledElement&&this.controlledElement.addEventListener("carousel:change",e=>{this.updateActiveDot(e.detail.index)}))}createDots(){for(let t=0;t<this.itemCount;t++){const e=document.createElement("button");e.classList.add("dots-indicator__dot"),e.addEventListener("click",()=>this.onDotClick(t)),this.dotsContainer.appendChild(e)}this.updateActiveDot(0)}onDotClick(t){this.controlledElement&&this.controlledElement.dispatchEvent(new CustomEvent("control:change",{detail:{index:t}})),this.updateActiveDot(t)}updateActiveDot(t){const e=this.dotsContainer.querySelector(".dots-indicator__dot.active");e&&e.classList.remove("active");const n=this.dotsContainer.children[t];n&&n.classList.add("active")}}customElements.define("dots-indicator",Ne);const St=Math.min,F=Math.max,te=Math.round,Gt=Math.floor,nt=i=>({x:i,y:i}),Vs={left:"right",right:"left",bottom:"top",top:"bottom"},js={start:"end",end:"start"};function ci(i,t,e){return F(i,St(t,e))}function $t(i,t){return typeof i=="function"?i(t):i}function st(i){return i.split("-")[0]}function Wt(i){return i.split("-")[1]}function pn(i){return i==="x"?"y":"x"}function mn(i){return i==="y"?"height":"width"}function Vt(i){return["top","bottom"].includes(st(i))?"y":"x"}function gn(i){return pn(Vt(i))}function zs(i,t,e){e===void 0&&(e=!1);const n=Wt(i),s=gn(i),r=mn(s);let a=s==="x"?n===(e?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(a=ee(a)),[a,ee(a)]}function Us(i){const t=ee(i);return[Le(i),t,Le(t)]}function Le(i){return i.replace(/start|end/g,t=>js[t])}function Gs(i,t,e){const n=["left","right"],s=["right","left"],r=["top","bottom"],a=["bottom","top"];switch(i){case"top":case"bottom":return e?t?s:n:t?n:s;case"left":case"right":return t?r:a;default:return[]}}function Ks(i,t,e,n){const s=Wt(i);let r=Gs(st(i),e==="start",n);return s&&(r=r.map(a=>a+"-"+s),t&&(r=r.concat(r.map(Le)))),r}function ee(i){return i.replace(/left|right|bottom|top/g,t=>Vs[t])}function Xs(i){return{top:0,right:0,bottom:0,left:0,...i}}function Ys(i){return typeof i!="number"?Xs(i):{top:i,right:i,bottom:i,left:i}}function ie(i){return{...i,top:i.y,left:i.x,right:i.x+i.width,bottom:i.y+i.height}}function di(i,t,e){let{reference:n,floating:s}=i;const r=Vt(t),a=gn(t),o=mn(a),l=st(t),c=r==="y",f=n.x+n.width/2-s.width/2,m=n.y+n.height/2-s.height/2,y=n[o]/2-s[o]/2;let v;switch(l){case"top":v={x:f,y:n.y-s.height};break;case"bottom":v={x:f,y:n.y+n.height};break;case"right":v={x:n.x+n.width,y:m};break;case"left":v={x:n.x-s.width,y:m};break;default:v={x:n.x,y:n.y}}switch(Wt(t)){case"start":v[a]-=y*(e&&c?-1:1);break;case"end":v[a]+=y*(e&&c?-1:1);break}return v}const Js=async(i,t,e)=>{const{placement:n="bottom",strategy:s="absolute",middleware:r=[],platform:a}=e,o=r.filter(Boolean),l=await(a.isRTL==null?void 0:a.isRTL(t));let c=await a.getElementRects({reference:i,floating:t,strategy:s}),{x:f,y:m}=di(c,n,l),y=n,v={},b=0;for(let d=0;d<o.length;d++){const{name:g,fn:p}=o[d],{x:S,y:E,data:C,reset:x}=await p({x:f,y:m,initialPlacement:n,placement:y,strategy:s,middlewareData:v,rects:c,platform:a,elements:{reference:i,floating:t}});if(f=S??f,m=E??m,v={...v,[g]:{...v[g],...C}},x&&b<=50){b++,typeof x=="object"&&(x.placement&&(y=x.placement),x.rects&&(c=x.rects===!0?await a.getElementRects({reference:i,floating:t,strategy:s}):x.rects),{x:f,y:m}=di(c,y,l)),d=-1;continue}}return{x:f,y:m,placement:y,strategy:s,middlewareData:v}};async function He(i,t){var e;t===void 0&&(t={});const{x:n,y:s,platform:r,rects:a,elements:o,strategy:l}=i,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:m="floating",altBoundary:y=!1,padding:v=0}=$t(t,i),b=Ys(v),g=o[y?m==="floating"?"reference":"floating":m],p=ie(await r.getClippingRect({element:(e=await(r.isElement==null?void 0:r.isElement(g)))==null||e?g:g.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(o.floating)),boundary:c,rootBoundary:f,strategy:l})),S=m==="floating"?{...a.floating,x:n,y:s}:a.reference,E=await(r.getOffsetParent==null?void 0:r.getOffsetParent(o.floating)),C=await(r.isElement==null?void 0:r.isElement(E))?await(r.getScale==null?void 0:r.getScale(E))||{x:1,y:1}:{x:1,y:1},x=ie(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({rect:S,offsetParent:E,strategy:l}):S);return{top:(p.top-x.top+b.top)/C.y,bottom:(x.bottom-p.bottom+b.bottom)/C.y,left:(p.left-x.left+b.left)/C.x,right:(x.right-p.right+b.right)/C.x}}const Qs=function(i){return i===void 0&&(i={}),{name:"flip",options:i,async fn(t){var e,n;const{placement:s,middlewareData:r,rects:a,initialPlacement:o,platform:l,elements:c}=t,{mainAxis:f=!0,crossAxis:m=!0,fallbackPlacements:y,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:d=!0,...g}=$t(i,t);if((e=r.arrow)!=null&&e.alignmentOffset)return{};const p=st(s),S=st(o)===o,E=await(l.isRTL==null?void 0:l.isRTL(c.floating)),C=y||(S||!d?[ee(o)]:Us(o));!y&&b!=="none"&&C.push(...Ks(o,d,b,E));const x=[o,...C],I=await He(t,g),_=[];let W=((n=r.flip)==null?void 0:n.overflows)||[];if(f&&_.push(I[p]),m){const h=zs(s,a,E);_.push(I[h[0]],I[h[1]])}if(W=[...W,{placement:s,overflows:_}],!_.every(h=>h<=0)){var B,tt;const h=(((B=r.flip)==null?void 0:B.index)||0)+1,u=x[h];if(u)return{data:{index:h,overflows:W},reset:{placement:u}};let w=(tt=W.filter(A=>A.overflows[0]<=0).sort((A,T)=>A.overflows[1]-T.overflows[1])[0])==null?void 0:tt.placement;if(!w)switch(v){case"bestFit":{var k;const A=(k=W.map(T=>[T.placement,T.overflows.filter(L=>L>0).reduce((L,O)=>L+O,0)]).sort((T,L)=>T[1]-L[1])[0])==null?void 0:k[0];A&&(w=A);break}case"initialPlacement":w=o;break}if(s!==w)return{reset:{placement:w}}}return{}}}};async function Zs(i,t){const{placement:e,platform:n,elements:s}=i,r=await(n.isRTL==null?void 0:n.isRTL(s.floating)),a=st(e),o=Wt(e),l=Vt(e)==="y",c=["left","top"].includes(a)?-1:1,f=r&&l?-1:1,m=$t(t,i);let{mainAxis:y,crossAxis:v,alignmentAxis:b}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...m};return o&&typeof b=="number"&&(v=o==="end"?b*-1:b),l?{x:v*f,y:y*c}:{x:y*c,y:v*f}}const tr=function(i){return i===void 0&&(i=0),{name:"offset",options:i,async fn(t){var e,n;const{x:s,y:r,placement:a,middlewareData:o}=t,l=await Zs(t,i);return a===((e=o.offset)==null?void 0:e.placement)&&(n=o.arrow)!=null&&n.alignmentOffset?{}:{x:s+l.x,y:r+l.y,data:{...l,placement:a}}}}},er=function(i){return i===void 0&&(i={}),{name:"shift",options:i,async fn(t){const{x:e,y:n,placement:s}=t,{mainAxis:r=!0,crossAxis:a=!1,limiter:o={fn:g=>{let{x:p,y:S}=g;return{x:p,y:S}}},...l}=$t(i,t),c={x:e,y:n},f=await He(t,l),m=Vt(st(s)),y=pn(m);let v=c[y],b=c[m];if(r){const g=y==="y"?"top":"left",p=y==="y"?"bottom":"right",S=v+f[g],E=v-f[p];v=ci(S,v,E)}if(a){const g=m==="y"?"top":"left",p=m==="y"?"bottom":"right",S=b+f[g],E=b-f[p];b=ci(S,b,E)}const d=o.fn({...t,[y]:v,[m]:b});return{...d,data:{x:d.x-e,y:d.y-n}}}}},hi=function(i){return i===void 0&&(i={}),{name:"size",options:i,async fn(t){const{placement:e,rects:n,platform:s,elements:r}=t,{apply:a=()=>{},...o}=$t(i,t),l=await He(t,o),c=st(e),f=Wt(e),m=Vt(e)==="y",{width:y,height:v}=n.floating;let b,d;c==="top"||c==="bottom"?(b=c,d=f===(await(s.isRTL==null?void 0:s.isRTL(r.floating))?"start":"end")?"left":"right"):(d=c,b=f==="end"?"top":"bottom");const g=v-l[b],p=y-l[d],S=!t.middlewareData.shift;let E=g,C=p;if(m){const I=y-l.left-l.right;C=f||S?St(p,I):I}else{const I=v-l.top-l.bottom;E=f||S?St(g,I):I}if(S&&!f){const I=F(l.left,0),_=F(l.right,0),W=F(l.top,0),B=F(l.bottom,0);m?C=y-2*(I!==0||_!==0?I+_:F(l.left,l.right)):E=v-2*(W!==0||B!==0?W+B:F(l.top,l.bottom))}await a({...t,availableWidth:C,availableHeight:E});const x=await s.getDimensions(r.floating);return y!==x.width||v!==x.height?{reset:{rects:!0}}:{}}}};function rt(i){return yn(i)?(i.nodeName||"").toLowerCase():"#document"}function $(i){var t;return(i==null||(t=i.ownerDocument)==null?void 0:t.defaultView)||window}function Y(i){var t;return(t=(yn(i)?i.ownerDocument:i.document)||window.document)==null?void 0:t.documentElement}function yn(i){return i instanceof Node||i instanceof $(i).Node}function X(i){return i instanceof Element||i instanceof $(i).Element}function U(i){return i instanceof HTMLElement||i instanceof $(i).HTMLElement}function ui(i){return typeof ShadowRoot>"u"?!1:i instanceof ShadowRoot||i instanceof $(i).ShadowRoot}function jt(i){const{overflow:t,overflowX:e,overflowY:n,display:s}=V(i);return/auto|scroll|overlay|hidden|clip/.test(t+n+e)&&!["inline","contents"].includes(s)}function ir(i){return["table","td","th"].includes(rt(i))}function Be(i){const t=Fe(),e=V(i);return e.transform!=="none"||e.perspective!=="none"||(e.containerType?e.containerType!=="normal":!1)||!t&&(e.backdropFilter?e.backdropFilter!=="none":!1)||!t&&(e.filter?e.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(e.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(e.contain||"").includes(n))}function nr(i){let t=Et(i);for(;U(t)&&!ae(t);){if(Be(t))return t;t=Et(t)}return null}function Fe(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ae(i){return["html","body","#document"].includes(rt(i))}function V(i){return $(i).getComputedStyle(i)}function oe(i){return X(i)?{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}:{scrollLeft:i.pageXOffset,scrollTop:i.pageYOffset}}function Et(i){if(rt(i)==="html")return i;const t=i.assignedSlot||i.parentNode||ui(i)&&i.host||Y(i);return ui(t)?t.host:t}function vn(i){const t=Et(i);return ae(t)?i.ownerDocument?i.ownerDocument.body:i.body:U(t)&&jt(t)?t:vn(t)}function _t(i,t,e){var n;t===void 0&&(t=[]),e===void 0&&(e=!0);const s=vn(i),r=s===((n=i.ownerDocument)==null?void 0:n.body),a=$(s);return r?t.concat(a,a.visualViewport||[],jt(s)?s:[],a.frameElement&&e?_t(a.frameElement):[]):t.concat(s,_t(s,[],e))}function bn(i){const t=V(i);let e=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const s=U(i),r=s?i.offsetWidth:e,a=s?i.offsetHeight:n,o=te(e)!==r||te(n)!==a;return o&&(e=r,n=a),{width:e,height:n,$:o}}function $e(i){return X(i)?i:i.contextElement}function bt(i){const t=$e(i);if(!U(t))return nt(1);const e=t.getBoundingClientRect(),{width:n,height:s,$:r}=bn(t);let a=(r?te(e.width):e.width)/n,o=(r?te(e.height):e.height)/s;return(!a||!Number.isFinite(a))&&(a=1),(!o||!Number.isFinite(o))&&(o=1),{x:a,y:o}}const sr=nt(0);function wn(i){const t=$(i);return!Fe()||!t.visualViewport?sr:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function rr(i,t,e){return t===void 0&&(t=!1),!e||t&&e!==$(i)?!1:t}function ut(i,t,e,n){t===void 0&&(t=!1),e===void 0&&(e=!1);const s=i.getBoundingClientRect(),r=$e(i);let a=nt(1);t&&(n?X(n)&&(a=bt(n)):a=bt(i));const o=rr(r,e,n)?wn(r):nt(0);let l=(s.left+o.x)/a.x,c=(s.top+o.y)/a.y,f=s.width/a.x,m=s.height/a.y;if(r){const y=$(r),v=n&&X(n)?$(n):n;let b=y.frameElement;for(;b&&n&&v!==y;){const d=bt(b),g=b.getBoundingClientRect(),p=V(b),S=g.left+(b.clientLeft+parseFloat(p.paddingLeft))*d.x,E=g.top+(b.clientTop+parseFloat(p.paddingTop))*d.y;l*=d.x,c*=d.y,f*=d.x,m*=d.y,l+=S,c+=E,b=$(b).frameElement}}return ie({width:f,height:m,x:l,y:c})}function ar(i){let{rect:t,offsetParent:e,strategy:n}=i;const s=U(e),r=Y(e);if(e===r)return t;let a={scrollLeft:0,scrollTop:0},o=nt(1);const l=nt(0);if((s||!s&&n!=="fixed")&&((rt(e)!=="body"||jt(r))&&(a=oe(e)),U(e))){const c=ut(e);o=bt(e),l.x=c.x+e.clientLeft,l.y=c.y+e.clientTop}return{width:t.width*o.x,height:t.height*o.y,x:t.x*o.x-a.scrollLeft*o.x+l.x,y:t.y*o.y-a.scrollTop*o.y+l.y}}function or(i){return Array.from(i.getClientRects())}function Sn(i){return ut(Y(i)).left+oe(i).scrollLeft}function lr(i){const t=Y(i),e=oe(i),n=i.ownerDocument.body,s=F(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),r=F(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let a=-e.scrollLeft+Sn(i);const o=-e.scrollTop;return V(n).direction==="rtl"&&(a+=F(t.clientWidth,n.clientWidth)-s),{width:s,height:r,x:a,y:o}}function cr(i,t){const e=$(i),n=Y(i),s=e.visualViewport;let r=n.clientWidth,a=n.clientHeight,o=0,l=0;if(s){r=s.width,a=s.height;const c=Fe();(!c||c&&t==="fixed")&&(o=s.offsetLeft,l=s.offsetTop)}return{width:r,height:a,x:o,y:l}}function dr(i,t){const e=ut(i,!0,t==="fixed"),n=e.top+i.clientTop,s=e.left+i.clientLeft,r=U(i)?bt(i):nt(1),a=i.clientWidth*r.x,o=i.clientHeight*r.y,l=s*r.x,c=n*r.y;return{width:a,height:o,x:l,y:c}}function fi(i,t,e){let n;if(t==="viewport")n=cr(i,e);else if(t==="document")n=lr(Y(i));else if(X(t))n=dr(t,e);else{const s=wn(i);n={...t,x:t.x-s.x,y:t.y-s.y}}return ie(n)}function En(i,t){const e=Et(i);return e===t||!X(e)||ae(e)?!1:V(e).position==="fixed"||En(e,t)}function hr(i,t){const e=t.get(i);if(e)return e;let n=_t(i,[],!1).filter(o=>X(o)&&rt(o)!=="body"),s=null;const r=V(i).position==="fixed";let a=r?Et(i):i;for(;X(a)&&!ae(a);){const o=V(a),l=Be(a);!l&&o.position==="fixed"&&(s=null),(r?!l&&!s:!l&&o.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||jt(a)&&!l&&En(i,a))?n=n.filter(f=>f!==a):s=o,a=Et(a)}return t.set(i,n),n}function ur(i){let{element:t,boundary:e,rootBoundary:n,strategy:s}=i;const a=[...e==="clippingAncestors"?hr(t,this._c):[].concat(e),n],o=a[0],l=a.reduce((c,f)=>{const m=fi(t,f,s);return c.top=F(m.top,c.top),c.right=St(m.right,c.right),c.bottom=St(m.bottom,c.bottom),c.left=F(m.left,c.left),c},fi(t,o,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function fr(i){return bn(i)}function pr(i,t,e){const n=U(t),s=Y(t),r=e==="fixed",a=ut(i,!0,r,t);let o={scrollLeft:0,scrollTop:0};const l=nt(0);if(n||!n&&!r)if((rt(t)!=="body"||jt(s))&&(o=oe(t)),n){const c=ut(t,!0,r,t);l.x=c.x+t.clientLeft,l.y=c.y+t.clientTop}else s&&(l.x=Sn(s));return{x:a.left+o.scrollLeft-l.x,y:a.top+o.scrollTop-l.y,width:a.width,height:a.height}}function pi(i,t){return!U(i)||V(i).position==="fixed"?null:t?t(i):i.offsetParent}function Cn(i,t){const e=$(i);if(!U(i))return e;let n=pi(i,t);for(;n&&ir(n)&&V(n).position==="static";)n=pi(n,t);return n&&(rt(n)==="html"||rt(n)==="body"&&V(n).position==="static"&&!Be(n))?e:n||nr(i)||e}const mr=async function(i){let{reference:t,floating:e,strategy:n}=i;const s=this.getOffsetParent||Cn,r=this.getDimensions;return{reference:pr(t,await s(e),n),floating:{x:0,y:0,...await r(e)}}};function gr(i){return V(i).direction==="rtl"}const yr={convertOffsetParentRelativeRectToViewportRelativeRect:ar,getDocumentElement:Y,getClippingRect:ur,getOffsetParent:Cn,getElementRects:mr,getClientRects:or,getDimensions:fr,getScale:bt,isElement:X,isRTL:gr};function vr(i,t){let e=null,n;const s=Y(i);function r(){clearTimeout(n),e&&e.disconnect(),e=null}function a(o,l){o===void 0&&(o=!1),l===void 0&&(l=1),r();const{left:c,top:f,width:m,height:y}=i.getBoundingClientRect();if(o||t(),!m||!y)return;const v=Gt(f),b=Gt(s.clientWidth-(c+m)),d=Gt(s.clientHeight-(f+y)),g=Gt(c),S={rootMargin:-v+"px "+-b+"px "+-d+"px "+-g+"px",threshold:F(0,St(1,l))||1};let E=!0;function C(x){const I=x[0].intersectionRatio;if(I!==l){if(!E)return a();I?a(!1,I):n=setTimeout(()=>{a(!1,1e-7)},100)}E=!1}try{e=new IntersectionObserver(C,{...S,root:s.ownerDocument})}catch{e=new IntersectionObserver(C,S)}e.observe(i)}return a(!0),r}function br(i,t,e,n){n===void 0&&(n={});const{ancestorScroll:s=!0,ancestorResize:r=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:o=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,c=$e(i),f=s||r?[...c?_t(c):[],..._t(t)]:[];f.forEach(p=>{s&&p.addEventListener("scroll",e,{passive:!0}),r&&p.addEventListener("resize",e)});const m=c&&o?vr(c,e):null;let y=-1,v=null;a&&(v=new ResizeObserver(p=>{let[S]=p;S&&S.target===c&&v&&(v.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{v&&v.observe(t)})),e()}),c&&!l&&v.observe(c),v.observe(t));let b,d=l?ut(i):null;l&&g();function g(){const p=ut(i);d&&(p.x!==d.x||p.y!==d.y||p.width!==d.width||p.height!==d.height)&&e(),d=p,b=requestAnimationFrame(g)}return e(),()=>{f.forEach(p=>{s&&p.removeEventListener("scroll",e),r&&p.removeEventListener("resize",e)}),m&&m(),v&&v.disconnect(),v=null,l&&cancelAnimationFrame(b)}}const wr=(i,t,e)=>{const n=new Map,s={platform:yr,...e},r={...s.platform,_c:n};return Js(i,t,{...s,platform:r})};window.activeDropdowns=[];class We extends HTMLElement{constructor(){super(),this.state="close",this.currentAnim=null,this.hideTimer=null,this.onWindowClickBound=this.onWindowClick.bind(this),this.dynamicPosition=!0,this.hoverShowDelay=150,this.hideDelay=250}get isMinWidthFull(){return this.getAttribute("min-width-full")!==null}get isHideMenuOnClick(){return this.getAttribute("hide-on-menu-click")!==null}get maxWidth(){return Number(this.getAttribute("max-width")??Number.MAX_SAFE_INTEGER)}get maxHeight(){const t=this.getAttribute("max-height");return t==="false"?Number.MAX_SAFE_INTEGER:Number(t??500)}connectedCallback(){this.details||(this.details=this.querySelector("details"),this.summary=this.querySelector("details > summary"),this.menu=this.summary.nextElementSibling,this.submenus=this.menu.querySelectorAll("details"),this.getAttribute("hover-show-delay")!==null&&(this.hoverShowDelay=Number(this.getAttribute("hover-show-delay"))),"ontouchstart"in window?this.setInteractionHandler("click"):this.setInteractionHandler(this.getAttribute("interaction-handler")??"click"),this.isHideMenuOnClick&&this.menu.addEventListener("click",this.hide.bind(this)))}disconnectedCallback(){this.detachInteractionHandler()}setState(t){switch(this.state=t,this.classList.remove("fading-in","fading-out"),this.state){case"close":this.submenus.forEach(e=>e.open=!1),this.details.offsetTop,this.details.open=!1;break;case"fading-in":this.classList.add("fading-in"),this.details.open=!0;break;case"open":this.details.open=!0;break;case"fading-out":this.classList.add("fading-out"),this.details.open=!0;break}}setInteractionHandler(t){this.interactionHandlerType=t,this.attachInteractionHandler()}attachInteractionHandler(){this.summary.addEventListener("click",this.onSummaryClick.bind(this)),this.interactionHandlerType==="click"?window.addEventListener("click",this.onWindowClickBound):this.interactionHandlerType==="hover"&&(this.summary.addEventListener("mouseenter",this.onSummaryMouseEnter.bind(this)),this.menu.addEventListener("mouseenter",this.cancelHide.bind(this)),this.summary.addEventListener("mouseleave",this.onMouseLeave.bind(this)),this.menu.addEventListener("mouseleave",this.onMouseLeave.bind(this)),this.menu.addEventListener("click",this.onMenuClick.bind(this)))}detachInteractionHandler(){this.interactionHandlerType==="click"&&window.removeEventListener("click",this.onWindowClickBound)}onSummaryMouseEnter(){this.cancelHide(),this.hoverShowDelay?this.showTimer=setTimeout(()=>{this.show({trigger:"hover"})},this.hoverShowDelay):this.show({trigger:"hover"})}onMenuClick(){this.menuJustClicked=!0,clearTimeout(this.menuJustClickedTimeout),this.menuJustClickedTimeout=setTimeout(()=>this.menuJustClicked=!1,500)}onMouseLeave(){this.cancelShow(),!this.menuJustClicked&&(this.hideTimer=setTimeout(()=>{this.hide()},this.hideDelay))}cancelHide(){clearTimeout(this.hideTimer),this.hideTimer=null}cancelShow(){clearTimeout(this.showTimer),this.showTimer=null}saveComponentState(){return{state:this.state}}loadComponentState(t){this.setState(t.state??"close"),this.updatePosition()}onWindowClick(t){this.state==="open"&&!this.details.contains(t.target)&&this.onSummaryClick(t,!1)}onSummaryClick(t,e=!0){var n,s;switch(e&&this.interactionHandlerType==="click"&&t.preventDefault(),this.state){case"close":case"fading-out":(n=this.currentAnim)==null||n.cancel(),this.state="close",this.show({trigger:"click"});break;case"open":case"fading-in":(s=this.currentAnim)==null||s.cancel(),this.state="open",this.hide();break}}get offset(){return Number(this.getAttribute("offset")??16)}get offsetCross(){return Number(Te(this,"offset-cross")??0)}get placement(){return Te(this,"placement")??"bottom-start"}get appendToBody(){return this.getAttribute("append-to-body")!==null}async updatePosition(){const t=this,e=[tr({mainAxis:this.offset,crossAxis:this.offsetCross}),er({padding:16}),hi({padding:16,apply({availableWidth:r,availableHeight:a,elements:o}){Object.assign(o.floating.style,{maxHeight:`${Math.max(250,Math.min(a,t.maxHeight))}px`,maxWidth:`${Math.max(150,Math.min(r,t.maxWidth))}px`})}}),Qs({fallbackStrategy:"initialPlacement"})];this.isMinWidthFull&&e.unshift(hi({apply({rects:r,elements:a}){Object.assign(a.floating.style,{minWidth:`${r.reference.width}px`})}}));const{x:n,y:s}=await wr(this.summary,this.menu,{middleware:e,placement:this.placement});Object.assign(this.menu.style,{left:`${n}px`,top:`${s}px`})}async show({anim:t=!0}={}){if(!(this.state==="open"||this.state==="fading-in")){if(window.activeDropdowns.forEach(e=>e.hide()),window.activeDropdowns.push(this),this.setState("fading-in"),this.appendToBody&&document.body.append(this.menu),this.dynamicPosition&&(this.autoUpdatePositionEnd=br(this.summary,this.menu,()=>this.updatePosition())),t){this.currentAnim=this.runShowAnimation();try{await this.currentAnim.finished}catch{}}this.setState("open")}}async hide(){if(!(this.state==="close"||this.state==="fading-out")){window.activeDropdowns=window.activeDropdowns.filter(t=>t!==this),this.cancelHide(),this.autoUpdatePositionEnd&&this.autoUpdatePositionEnd(),this.setState("fading-out"),this.currentAnim=this.runHideAnimation();try{await this.currentAnim.finished}catch{}this.appendToBody&&this.summary.after(this.menu),this.setState("close")}}runShowAnimation(){switch(this.getAttribute("animation")??"fade-down"){case"fade-down":return P(this.menu,"fade-in",{y:"-1rem",duration:100,easing:H});case"fade-up":return P(this.menu,"fade-in",{y:"1rem",duration:100,easing:H});case"scale":return P(this.menu,"scale-in",{duration:150})}}runHideAnimation(){switch(this.getAttribute("animation")??"fade-down"){case"fade-down":return P(this.menu,"fade-out",{y:"-1rem",duration:150,easing:"ease-in-out"});case"fade-up":return P(this.menu,"fade-out",{y:"1rem",duration:150,easing:"ease-in-out"});case"scale":return P(this.menu,"scale-out",{duration:150,easing:H})}}}customElements.define("dropdown-element",We);class xn extends We{connectedCallback(){super.connectedCallback(),this.setupElements(),this.setupEventListeners(),this.activeOption&&this.updateInputAttributes(this.activeOption)}setupElements(){this.valueInput=this.querySelector("[data-list-value]"),this.dropdownActivator=this.querySelector("[data-dropdown-activator]"),this.options=Array.from(this.querySelectorAll("[data-value]"))}setupEventListeners(){this.options.forEach(t=>t.addEventListener("click",this.onOptionClick.bind(this))),this.addEventListener("keydown",this.onKeyDownHandler.bind(this)),this.focusOnly&&this.appendToBody&&this.menu.addEventListener("keydown",this.onKeyDownHandler.bind(this))}get focusOnly(){return this.getAttribute("focus-only")!==null}get selectedOption(){return this.options.find(t=>t.dataset.value===this.valueInput.value)}async onOptionClick(t){if(this.focusOnly){await this.hide();return}t.preventDefault(),this.updateSelected(t.currentTarget),await this.hide()}onKeyDownHandler(t){t.key==="ArrowUp"?(t.preventDefault(),this.updateSelected(this.getPrevOption()),this.setActiveClass()):t.key==="ArrowDown"?(t.preventDefault(),this.updateSelected(this.getNextOption()),this.setActiveClass()):this.state==="open"&&(t.key==="Tab"||t.key==="Escape")&&(t.stopPropagation(),this.hide(),this.focusOnly&&this.dropdownActivator.focus())}getNextOption(){const t=this.getActiveElement();return this.options[dt(this.options.findIndex(e=>e===t)+1,this.options.length)]}getPrevOption(){const t=this.getActiveElement();return this.options[dt(this.options.findIndex(e=>e===t)-1,this.options.length)]}getActiveElement(){return this.focusOnly?this.menu.contains(document.activeElement)?document.activeElement:null:this.querySelector("[data-value].active")}async show(){var t;this.state==="open"||this.state==="fading-in"||(this.startValue=(t=this.valueInput)==null?void 0:t.value,this.setActiveClass(),this.shouldShowMobileModal()?await this.showMobileModal():await super.show())}async hide(){this.state==="close"||this.state==="fading-out"||(this.emitChange(),this.mobileModal?await this.hideMobileModal():await super.hide())}emitChange(){this.focusOnly||this.valueInput.value!==this.startValue&&(this.startValue=this.valueInput.value,this.valueInput.dispatchEvent(new Event("change",{bubbles:!0})))}shouldShowMobileModal(){return window.innerWidth<768}async showMobileModal(){this.state="open",this.menu.removeAttribute("style"),Object.assign(this.menu.style,{position:"static",width:"100%"});const t=pt(`
      <x-modal class="modal bottom-modal modal-w-full">
        <div slot="content" tabindex="-1" class="flex flex-col items-stretch max-h-[60vh]">
          <div class="flex items-center px-6 py-4">
            <h3 class="h6">${this.getAttribute("list-title")??""}</h3>
            <button class="modal-close ml-auto" data-button-close>${window.svgs.times}</button>
          </div>
          <div data-content class="grow overflow-y-auto custom-scrollbar"></div>
        </div>
      </x-modal>
    `);t.querySelector("[data-content]").append(this.menu),document.body.append(t),this.mobileModal=t,t.addEventListener("hide-end",()=>{this.emitChange(),this.cleanupMobileModal()}),await t.show()}async hideMobileModal(){await this.mobileModal.hide(),this.cleanupMobileModal()}cleanupMobileModal(){var t;(t=this.mobileModal)==null||t.remove(),this.mobileModal=null,Object.assign(this.menu.style,{position:null,width:null}),this.summary.after(this.menu),this.state="close"}onWindowClick(t){this.mobileModal||super.onWindowClick(t)}setActiveClass(){var t;this.focusOnly||(this.options.forEach(e=>e.classList.remove("active")),(t=this.activeOption)==null||t.classList.add("active"))}get activeOption(){return this.valueInput?this.options.find(t=>t.dataset.value===this.valueInput.value):null}updateSelected(t){if(this.focusOnly){t.focus();return}this.dropdownActivator&&(this.dropdownActivator.textContent=t.dataset.text??t.textContent),this.updateInputAttributes(t)}updateInputAttributes(t){if(this.valueInput){for(let e in this.valueInput.dataset)delete this.valueInput.dataset[e];Object.assign(this.valueInput.dataset,t.dataset),this.valueInput.value=t.dataset.value}}get isMinWidthFull(){return!0}get offset(){return Number(this.getAttribute("offset")??8)}}customElements.define("dropdown-list",xn);class Sr extends HTMLElement{constructor(){super()}connectedCallback(){this.facetForm=this.querySelector("form"),this.getAttribute("manual")===null&&this.facetForm.addEventListener("change",this.onSubmit.bind(this)),this.parentSection=this.closest(".shopify-section"),this.otherForms=Array.from(document.querySelectorAll(`facet-filters-form:not([type="${this.getAttribute("type")}"])`)).filter(t=>t!==this)}async onSubmit(){var n;if(this.parentSection.classList.add("loading"),this.abortController){this.abortController.abort();const s=new AbortController;if(this.abortController=s,await j(500),s.signal.aborted)return}else this.abortController=new AbortController;const t=this.otherForms.reduce((s,r)=>{const a=new FormData(r.facetForm);for(const o of a.entries())s.append(o[0],o[1]);return s},new FormData(this.facetForm)),e=`${window.location.origin}${window.location.pathname}?${new URLSearchParams([...t.entries()].filter(s=>s[1])).toString()}`;try{await xt(this.parentSection,{url:e,signal:this.abortController.signal})}catch(s){s.name!=="AbortError"&&console.error(s);return}this.parentSection.offsetTop,this.parentSection.classList.remove("loading"),Fs("lg")&&Ki(document.querySelector(".active-facets")??this.parentSection,((n=document.querySelector(".header"))==null?void 0:n.clientHeight)+16),history.replaceState({},"",e),this.abortController=null}}customElements.define("facet-filters-form",Sr);class Er extends At{constructor(){super(),this.formChanged=!1}show(t){return this.querySelector("facet-filters-form").addEventListener("input",this.handleFormChange.bind(this)),super.show(t)}hide(t){const e=this.querySelector("facet-filters-form");return e.removeEventListener("input",this.handleFormChange.bind(this)),this.formChanged&&e.onSubmit(),this.formChanged=!1,super.hide(t)}handleFormChange(){this.formChanged=!0}}customElements.define("facets-drawer-modal",Er);class Cr extends HTMLElement{constructor(){super(),this.isMoving=!1,this.progress=.5,this.startMove=this.startMove.bind(this),this.stopMove=this.stopMove.bind(this),this.move=this.move.bind(this),this.handleKeydown=this.handleKeydown.bind(this)}connectedCallback(){this.handle=this.querySelector(".image-comparison__handle"),this.bar=this.querySelector(".image-comparison__bar"),this.handle.addEventListener("mousedown",this.startMove),this.handle.addEventListener("touchstart",this.startMove),document.addEventListener("mouseup",this.stopMove),document.addEventListener("touchend",this.stopMove),document.addEventListener("mousemove",this.move),document.addEventListener("touchmove",this.move),this.addEventListener("keydown",this.handleKeydown)}disconnectedCallback(){this.handle.removeEventListener("mousedown",this.startMove),this.handle.removeEventListener("touchstart",this.startMove),document.removeEventListener("mouseup",this.stopMove),document.removeEventListener("touchend",this.stopMove),document.removeEventListener("mousemove",this.move),document.removeEventListener("touchmove",this.move),this.removeEventListener("keydown",this.handleKeydown)}startMove(t){t.preventDefault(),this.isMoving=!0}stopMove(){this.isMoving=!1}move(t){if(!this.isMoving)return;let e=t.type==="touchmove"?t.touches[0].clientX:t.clientX,n=this.getBoundingClientRect(),s=(e-n.left)/n.width;this.setProgress(Math.max(0,Math.min(1,s)))}handleKeydown(t){t.key==="ArrowLeft"?this.setProgress(this.progress-.1):t.key==="ArrowRight"&&this.setProgress(this.progress+.1)}setProgress(t){this.progress=Math.max(0,Math.min(1,t)),this.style.setProperty("--image-comparison-progress",this.progress*100+"%")}}customElements.define("image-comparison",Cr);class xr extends HTMLElement{constructor(){super(),this.input=this.querySelector('input[name="locale_code"], input[name="country_code"]'),this.querySelectorAll("button").forEach(t=>t.addEventListener("click",e=>{e.preventDefault();const n=this.querySelector("form");this.input.value=e.currentTarget.dataset.value,n&&n.submit()}))}}customElements.define("localization-form",xr);class Ar extends HTMLElement{constructor(){super(),setTimeout(()=>{const t=Array.from(this.querySelectorAll("img"));if(this.actualImage=t[0],this.placeholderImage=t[1],!!this.actualImage){if(this.actualImage.complete){this.classList.add("loaded"),this.placeholderImage&&this.placeholderImage.remove();return}this.classList.add("loading"),(!this.actualImage.complete||this.placeholderImage&&!this.placeholderImage.complete)&&(this.loadingBar=this.appendChild(Jn(document.createElement("div"),e=>{e.classList.add("loading-bar")}))),this.placeholderImage&&xe(this.placeholderImage).then(()=>this.showPlaceholder()),xe(this.actualImage).then(()=>this.showActaulImage())}})}showPlaceholder(){var t,e;(t=this.placeholderImage)==null||t.animate({opacity:[0,1]},{duration:200}).finished.finally(()=>{this.placeholderImage&&(this.placeholderImage.style.opacity="1")}),(e=this.loadingBar)==null||e.animate({opacity:[1,0]},{duration:100}).finished.finally(()=>{var n;(n=this.loadingBar)==null||n.remove()})}showActaulImage(){var t;(t=this.loadingBar)==null||t.remove(),this.actualImage.animate({opacity:[0,1]},{duration:200}).finished.finally(()=>{this.classList.remove("loading"),this.classList.add("loaded"),this.actualImage.style.opacity="1",this.placeholderImage&&(this.placeholderImage.remove(),this.placeholderImage=null)})}}customElements.define("lqip-element",Ar);var Tr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ne={exports:{}};ne.exports;(function(i,t){(function(e,n){var s={};e.PubSub?(s=e.PubSub,console.warn("PubSub already loaded, using existing version")):(e.PubSub=s,n(s)),i!==void 0&&i.exports&&(t=i.exports=s),t.PubSub=s,i.exports=t=s})(typeof window=="object"&&window||Tr,function(e){var n={},s=-1,r="*";function a(d){var g;for(g in d)if(Object.prototype.hasOwnProperty.call(d,g))return!0;return!1}function o(d){return function(){throw d}}function l(d,g,p){try{d(g,p)}catch(S){setTimeout(o(S),0)}}function c(d,g,p){d(g,p)}function f(d,g,p,S){var E=n[g],C=S?c:l,x;if(Object.prototype.hasOwnProperty.call(n,g))for(x in E)Object.prototype.hasOwnProperty.call(E,x)&&C(E[x],d,p)}function m(d,g,p){return function(){var E=String(d),C=E.lastIndexOf(".");for(f(d,d,g,p);C!==-1;)E=E.substr(0,C),C=E.lastIndexOf("."),f(d,E,g,p);f(d,r,g,p)}}function y(d){var g=String(d),p=!!(Object.prototype.hasOwnProperty.call(n,g)&&a(n[g]));return p}function v(d){for(var g=String(d),p=y(g)||y(r),S=g.lastIndexOf(".");!p&&S!==-1;)g=g.substr(0,S),S=g.lastIndexOf("."),p=y(g);return p}function b(d,g,p,S){d=typeof d=="symbol"?d.toString():d;var E=m(d,g,S),C=v(d);return C?(p===!0?E():setTimeout(E,0),!0):!1}e.publish=function(d,g){return b(d,g,!1,e.immediateExceptions)},e.publishSync=function(d,g){return b(d,g,!0,e.immediateExceptions)},e.subscribe=function(d,g){if(typeof g!="function")return!1;d=typeof d=="symbol"?d.toString():d,Object.prototype.hasOwnProperty.call(n,d)||(n[d]={});var p="uid_"+String(++s);return n[d][p]=g,p},e.subscribeAll=function(d){return e.subscribe(r,d)},e.subscribeOnce=function(d,g){var p=e.subscribe(d,function(){e.unsubscribe(p),g.apply(this,arguments)});return e},e.clearAllSubscriptions=function(){n={}},e.clearSubscriptions=function(g){var p;for(p in n)Object.prototype.hasOwnProperty.call(n,p)&&p.indexOf(g)===0&&delete n[p]},e.countSubscriptions=function(g){var p,S,E=0;for(p in n)if(Object.prototype.hasOwnProperty.call(n,p)&&p.indexOf(g)===0){for(S in n[p])E++;break}return E},e.getSubscriptions=function(g){var p,S=[];for(p in n)Object.prototype.hasOwnProperty.call(n,p)&&p.indexOf(g)===0&&S.push(p);return S},e.unsubscribe=function(d){var g=function(W){var B;for(B in n)if(Object.prototype.hasOwnProperty.call(n,B)&&B.indexOf(W)===0)return!0;return!1},p=typeof d=="string"&&(Object.prototype.hasOwnProperty.call(n,d)||g(d)),S=!p&&typeof d=="string",E=typeof d=="function",C=!1,x,I,_;if(p){e.clearSubscriptions(d);return}for(x in n)if(Object.prototype.hasOwnProperty.call(n,x)){if(I=n[x],S&&I[d]){delete I[d],C=d;break}if(E)for(_ in I)Object.prototype.hasOwnProperty.call(I,_)&&I[_]===d&&(delete I[_],C=!0)}return C}})})(ne,ne.exports);var q=ne.exports;const le="product.gallery.changed",zt="product.gallery.indicator_changed",mi="collapse.changed";class Lr extends HTMLElement{constructor(){super(),this.currentIndex=0;const t=this.attachShadow({mode:"open"});t.innerHTML=`
      <style>
        :host {
          display: block;
        }

        .container {
          display: flex;
          align-items: center;
          width: 100%;
          height: .25rem;
          background-color: rgb(0 0 0 / 8%);
          position: relative;
        }

        .indicator {
          position: absolute;
          height: .25rem;
          background-color: rgb(var(--color-base-accent));
          transition: transform 300ms;
        }
      </style>
      <div class="container">
        <div class="indicator"></div>
      </div>
    `}connectedCallback(){var t;this.sectionId=(t=this.closest(".shopify-section"))==null?void 0:t.id,this.mediaCount=this.getAttribute("media-count"),this.indicator=this.shadowRoot.querySelector(".indicator"),this.container=this.shadowRoot.querySelector(".container"),this.indicator.style.width=`${100/this.mediaCount}%`,this.resizeObserver=new ResizeObserver(()=>this.calculateX(this.currentIndex)),this.resizeObserver.observe(this),this.container.addEventListener("click",e=>this.onContainerClick(e)),this.subs=[],this.subs.push(q.subscribe(le,(e,n)=>{n.sectionId===this.sectionId&&this.onIndexChanged(n)}))}disconnectedCallback(){this.resizeObserver.disconnect(),this.subs.forEach(t=>q.unsubscribe(t))}onContainerClick(t){const e=this.container.clientWidth,n=this.indicator.clientWidth,s=t.clientX-this.container.getBoundingClientRect().left,r=Math.floor(s/(e-n)*(this.mediaCount-1));this.currentIndex=r,q.publish(zt,{index:r,sectionId:this.sectionId}),this.calculateX(r)}onIndexChanged({index:t}){this.currentIndex=t,this.calculateX(t)}calculateX(t){const e=(this.clientWidth-this.indicator.clientWidth)*(t/(this.mediaCount-1));this.indicator.style.transform=`translateX(${e}px)`}}customElements.define("media-carousel-bar",Lr);class Ir extends Ne{constructor(){super(),this.subs=[]}connectedCallback(){var t;super.connectedCallback(),this.sectionId=(t=this.closest(".shopify-section"))==null?void 0:t.id,this.subs.push(q.subscribe(le,(e,n)=>{n.sectionId===this.sectionId&&this.onIndexChanged(n)}))}disconnectedCallback(){this.subs.forEach(t=>q.unsubscribe(t))}onDotClick(t){super.onDotClick(t),q.publish(zt,{index:t,sectionId:this.sectionId})}onIndexChanged({index:t}){this.updateActiveDot(t)}}customElements.define("media-carousel-dots",Ir);const kr="modulepreload",Mr=function(i){return"/"+i},gi={},yi=function(t,e,n){if(!e||e.length===0)return t();const s=document.getElementsByTagName("link");return Promise.all(e.map(r=>{if(r=Mr(r),r in gi)return;gi[r]=!0;const a=r.endsWith(".css"),o=a?'[rel="stylesheet"]':"";if(!!n)for(let f=s.length-1;f>=0;f--){const m=s[f];if(m.href===r&&(!a||m.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${r}"]${o}`))return;const c=document.createElement("link");if(c.rel=a?"stylesheet":kr,a||(c.as="script",c.crossOrigin=""),c.href=r,document.head.appendChild(c),a)return new Promise((f,m)=>{c.addEventListener("load",f),c.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>t()).catch(r=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=r,window.dispatchEvent(a),!a.defaultPrevented)throw r})};class An{constructor(t){this.container=t,this.cssLoaded=!1,this.loading=!1}async showLightbox({mediaId:t=null,init:e=!0}={}){if(this.loading)return;this.loading=!0;const n=[];n.push(yi(()=>import("./photoswipe.esm.mjs"),[])),this.cssLoaded||n.push(this.loadCss());const s=(await Promise.all(n))[0].default;this.loading=!1,this.cssLoaded=!0;const r=[...this.container.querySelectorAll("[data-media-type=image]")].map(o=>({src:o.querySelector("img").src,alt:o.querySelector("img").alt,width:o.dataset.mediaWidth,height:o.dataset.mediaHeight,mediaId:o.dataset.mediaId})),a=new s({bgOpacity:1,mainClass:"pswp--custom-bg",dataSource:r,index:r.findIndex(o=>o.mediaId===t)??0,returnFocus:!1,arrowPrevSVG:window.svgs.chevron,arrowNextSVG:window.svgs.chevron,closeSVG:window.svgs.times,zoomSVG:window.svgs.zoom_in});return a.addFilter("thumbEl",(o,l)=>this.container.querySelector(`[data-media-id="${l.mediaId}"] img`)??o),a.addFilter("thumbBounds",(o,l)=>{const f=this.container.querySelector(`[data-media-id="${l.mediaId}"]`).getBoundingClientRect();return this.calculateThumbBounds(l,f)}),a.addFilter("placeholderSrc",(o,l)=>{const c=this.container.querySelector(`[data-media-id="${l.data.mediaId}"] img`);return c?c.currentSrc:o}),e&&a.init(),a}calculateThumbBounds(t,e){const n=parseInt(t.width,10),s=parseInt(t.height,10),r=e.width/e.height,a=n/s;let o,l;a>r?(o=e.width,l=e.width/a):(l=e.height,o=e.height*a);const c=e.x+(e.width-o)/2,f=e.y+(e.height-l)/2;return{x:c,y:f,w:o}}async loadCss(){if(document.querySelector("[data-pswp-css]"))return;const{default:t}=await yi(()=>import("./photoswipe.mjs"),[]);document.head.insertAdjacentHTML("beforeend",`<style data-pswp-css>${t}</style>`)}}class Or extends An{constructor(t){super(t),t.querySelectorAll("[data-media-type=image]").forEach(e=>e.addEventListener("click",()=>this.showLightbox()))}async showLightbox(){const t=this.container.items[this.container.currentIndex];try{const e=await super.showLightbox({mediaId:t.dataset.mediaId,init:!1});if(!e)return;const n=this.container.scrollBehavior;return this.container.scrollBehavior="instant",this.container.pauseMedia(),e.on("change",()=>{q.publish(zt,{mediaId:e.currSlide.data.mediaId,sectionId:this.container.sectionId})}),e.on("close",()=>{this.container.scrollBehavior=n}),e.init(),e}finally{}}}class Pr extends Yi{constructor(){super(),this.currentIndex=0,this.subs=[],this.scrollBehavior="auto",this.handleVariantChange=this.handleVariantChange.bind(this)}connectedCallback(){this.scrollLeft=0,super.connectedCallback(),this.loader=this.querySelector("[data-media-carousel-loading-overlay]"),this.quickAdd=this.closest(".quick-add-modal"),this.lightboxEnabled&&(this.lightbox=new Or(this)),this.setupLoadListeners(),this.updateMaxHeight(),this.preloadNextMedia(),this.updateItemOffsets(),this.connectWithThumbnails(),this.section.addEventListener("variant:change",this.handleVariantChange)}disconnectedCallback(){super.disconnectedCallback(),this.subs.forEach(t=>q.unsubscribe(t)),this.section.removeEventListener("variant:change",this.handleVariantChange)}get lightboxEnabled(){return this.getAttribute("lightbox")!=="false"}get videoAutoplay(){return this.getAttribute("video-autoplay")==="true"}get section(){return this.closest(".shopify-section")}handleVariantChange(t){var e,n;(n=(e=t.detail.variant)==null?void 0:e.featured_media)!=null&&n.id&&this.goToMediaId(t.detail.variant.featured_media.id)}updateMaxHeight(){if(this.getAttribute("adaptive-height")!==null){const t=Math.max(un("lg")||this.quickAdd?0:this.items[this.currentIndex].clientWidth,this.items[this.currentIndex].clientHeight);t>0&&(this.style.maxHeight=`${t}px`)}else this.style.maxHeight=null}updateItemOffsets(){this.itemOffsets=this.items.map(t=>t.parentElement.offsetLeft)}connectWithThumbnails(){this.subs.push(q.subscribe(zt,(t,e)=>{e.sectionId===this.sectionId&&("index"in e?(this.updateCurrentIndex(e.index),this.syncScroll()):e.mediaId&&this.goToMediaId(e.mediaId))}))}calculateCurrentIndex(){const t=this.scrollLeft;let e=0,n=Math.abs(t-this.itemOffsets[0]);for(let s=1;s<this.itemOffsets.length;s++){const r=this.itemOffsets[s],a=Math.abs(t-r);a<n&&(n=a,e=s)}return e}onScroll(){super.onScroll(),this.userScroll&&this.updateCurrentIndex(this.calculateCurrentIndex())}onScrollEnd(){super.onScrollEnd(),this.touching||this.updateMaxHeight()}onResize(){super.onResize(),this.updateMaxHeight(),this.updateItemOffsets()}goPrev(){this.updateCurrentIndex(this.currentIndex-1),this.syncScroll()}goNext(){this.updateCurrentIndex(this.currentIndex+1),this.syncScroll()}goToMediaId(t){const e=this.items.findIndex(n=>n.dataset.mediaId==t);e===-1||e===this.currentIndex||(this.updateCurrentIndex(e),this.syncScroll())}updateCurrentIndex(t){var s,r;const e=this.loop?dt(t,this.items.length):Math.max(0,Math.min(this.items.length-1,t));if(e===this.currentIndex)return;this.pauseMedia(),this.currentIndex=e,this.preloadNextMedia();const n=this.items[this.currentIndex];if(["video","external_video"].includes(n.dataset.mediaType)&&this.videoAutoplay&&this.handleVideoAutoplay(n),n.dataset.mediaType==="model"){const a=(s=this.closest(".shopify-section"))==null?void 0:s.querySelector("[data-shopify-xr]");a&&(a.setAttribute("data-shopify-model3d-id",n.dataset.mediaId),(r=window.ShopifyXR)==null||r.setupXRElements())}q.publish(le,{index:this.currentIndex,mediaId:n.dataset.mediaId,sectionId:this.sectionId})}handleVideoAutoplay(t){t.play()}async syncScroll(){const t=this.currentIndex,e=this.items[t];if(e.dataset.loaded)this.setLoadingOverlay(!1);else{const n=e.querySelector("img");if(n&&!n.complete){this.setLoadingOverlay(!0);const s=e.querySelector("[data-loader]");s&&s.remove();const r=Date.now();await new Promise(o=>{n.addEventListener("load",()=>o()),n.setAttribute("loading","eager")});const a=Date.now()-r;if(a<133&&await j(133-a),t!==this.currentIndex)return;this.setLoadingOverlay(!1)}}this.triggerScroll(e.parentElement.offsetLeft)}setLoadingOverlay(t){this.loader.style.opacity=t?1:null}pauseMedia(){this.querySelectorAll("video-player").forEach(t=>t.pause()),this.querySelectorAll("product-model").forEach(t=>{t.modelViewerUI&&t.modelViewerUI.pause()})}preloadNextMedia(){var t,e;(e=(t=this.items[this.currentIndex])==null?void 0:t.parentElement.nextElementSibling)==null||e.querySelectorAll("img[loading=lazy]").forEach(n=>n.setAttribute("loading","eager"))}setupLoadListeners(){this.items.filter(t=>!t.querySelector("img")).forEach(t=>t.dataset.loaded=!0),this.querySelectorAll("img").forEach(t=>{const e=t.closest(this.itemSelector);t.complete&&e?e.dataset.loaded=!0:t.addEventListener("load",()=>{e&&(e.dataset.loaded=!0)})})}}customElements.define("media-carousel",Pr);customElements.get("media-gallery")||customElements.define("media-gallery",class extends HTMLElement{constructor(){super()}setActiveMedia(t){const e=this.querySelector(`[data-media-id="${t}"]`);e&&e.parentElement.prepend(e)}});class Rr extends HTMLElement{constructor(){super(),this.handleVariantChange=this.handleVariantChange.bind(this)}connectedCallback(){var t;this.media=this.querySelectorAll("[data-media-position]"),this.parentSection=this.closest(".shopify-section"),(t=this.parentSection)==null||t.addEventListener("variant:change",this.handleVariantChange)}disconnectedCallback(){var t;(t=this.parentSection)==null||t.removeEventListener("variant:change",this.handleVariantChange)}handleVariantChange(t){var r;const e=(r=t.detail.variant.featured_media)==null?void 0:r.id;if(!e)return;const n=Array.from(this.media),s=n.findIndex(a=>a.dataset.mediaId==e);if(s>-1){const a=n.splice(s,1)[0];n.sort((l,c)=>parseInt(l.dataset.mediaPosition)-parseInt(c.dataset.mediaPosition)),n.unshift(a);const o=this;n.forEach(l=>o.appendChild(l))}}}customElements.define("media-grid",Rr);class Dr extends HTMLElement{constructor(){super(),this.lightbox=new An(this)}connectedCallback(){this.querySelectorAll("[data-media-type=image]").forEach(t=>t.addEventListener("click",this.onImageClick.bind(this)))}onImageClick(t){this.lightbox.showLightbox({mediaId:t.currentTarget.dataset.mediaId})}}customElements.define("media-lightbox",Dr);window.activeMegaMenuHorizontal=null;class qr extends We{constructor(){super(),this.dynamicPosition=!1,this.hoverShowDelay=null,this.hideDelay=150}connectedCallback(){super.connectedCallback(),this.headerComponent=this.closest("sticky-header"),this.inner=this.querySelector(".mega-menu-horizontal__inner"),this.container=this.querySelector(".container")}onWindowClick(t){t.target.closest("mega-menu-horizontal")||super.onWindowClick(t)}async show({anim:t=!0,trigger:e}={}){var n;if(this.state==="close"){if((n=this.currentAnim)==null||n.cancel(),t&&window.activeMegaMenuHorizontal&&!window.activeMegaMenuHorizontal.hoverDelay){const s=window.activeMegaMenuHorizontal;window.activeMegaMenuHorizontal=this,await this.transitionWith(s);return}if(window.activeMegaMenuHorizontal=this,t&&e==="hover"){if(this.hoverDelay=!0,await j(100),this.hoverDelay=!1,window.activeMegaMenuHorizontal!==this)return;if(this.hideTimer){window.activeMegaMenuHorizontal===this&&(window.activeMegaMenuHorizontal=null);return}}this.adjustHeaderTransition(),this.headerComponent.showOverlay(),this.headerComponent.disableTransparent(),rn(),await super.show(...arguments)}}async transitionWith(t){this.cancelHide(),t.cancelHide(),this.getAnimations({subtree:!0}).forEach(r=>r.cancel()),t.getAnimations({subtree:!0}).forEach(r=>r.cancel()),t.setState("open");const e=t.inner.clientHeight;this.setState("open");const n=this.inner.clientHeight;this.setState("close");const s=t.container.animate({opacity:[1,0]},{duration:120});t.container.style.opacity="0",e!=n?(t.inner.style.overflow="hidden",await t.inner.animate({height:[`${e}px`,`${n}px`]},{duration:200,easing:H}).finished,t.inner.style.overflow=null):await s.finished,t.setState("close"),t.container.style.opacity=null,this.setState("open"),await this.container.animate({opacity:[0,1]},{duration:120}).finished}async hide(){var t;this.state==="open"&&((t=this.currentAnim)==null||t.cancel(),window.activeMegaMenuHorizontal===this&&(window.activeMegaMenuHorizontal=null),this.adjustHeaderTransition(),this.headerComponent.enableTransparent(),await super.hide(),window.activeMegaMenuHorizontal||(this.headerComponent.hideOverlay(),(window.activeModals.length===0||!window.activeModals[window.activeModals.length-1].useLockScroll)&&_e()))}runShowAnimation(){return P(this.inner,"fade-in",{duration:200,easing:H,y:"-1rem"})}runHideAnimation(){return P(this.inner,"fade-out",{duration:150,easing:Xt,y:"-1rem"})}adjustHeaderTransition(){this.adjustHeaderTransitionTimeout&&clearTimeout(this.adjustHeaderTransitionTimeout);const t=this.headerComponent.querySelector(".header");Object.assign(t.style,{transitionDuration:"140ms",transitionProperty:"background-color, color"}),this.adjustHeaderTransitionTimeout=setTimeout(()=>{Object.assign(t.style,{transitionDuration:null,transitionProperty:null}),this.adjustHeaderTransitionTimeout=null},300)}}customElements.define("mega-menu-horizontal",qr);class _r extends HTMLButtonElement{constructor(){super(),this.active=!1,this.addEventListener("click",()=>{this.active=!this.active,this.update()})}connectedCallback(){this.mobileMenu=document.querySelector("[data-mobile-menu]"),this.mobileMenu.addEventListener("hide",()=>this.active=!1)}update(){this.mobileMenu&&(this.active?this.mobileMenu.show():this.mobileMenu.hide())}}customElements.define("menu-hamburger",_r,{extends:"button"});class Nr extends HTMLElement{constructor(){super(),this.handleVariantChange=this.handleVariantChange.bind(this)}connectedCallback(){var t;this.media=[...this.querySelectorAll("[data-media-id]")],this.parentSection=this.closest(".shopify-section"),(t=this.parentSection)==null||t.addEventListener("variant:change",this.handleVariantChange)}disconnectedCallback(){var t;(t=this.parentSection)==null||t.removeEventListener("variant:change",this.handleVariantChange)}handleVariantChange(t){var n;if(!t.detail.variant)return;const e=(n=t.detail.variant.featured_media)==null?void 0:n.id;this.media.forEach(s=>s.classList.toggle("hidden",s.dataset.mediaId!=e)),this.media.every(s=>s.classList.contains("hidden"))&&this.media[0].classList.remove("hidden")}}customElements.define("mini-gallery",Nr);class Hr extends HTMLElement{constructor(){super(),this.open=!1,this.attachShadow({mode:"open"}).appendChild(document.querySelector("#template-mobile-menu-page").content.cloneNode(!0))}connectedCallback(){this.setupElements(this.shadowRoot),this.setupEventListeners()}setupElements(t){this.content=t.querySelector(".content"),this.wrapper=t.querySelector(".wrapper"),this.backButton=this.querySelector(".back-button"),this.activator=t.querySelector("slot[name=activator]")}setupEventListeners(){this.activator.addEventListener("click",t=>this.onActivatorClick(t)),this.backButton.addEventListener("click",this.hide.bind(this))}onActivatorClick(t){t.preventDefault(),this.show()}getAnimInElements(){return this.querySelectorAll("[data-menu-anim-in]:not(:scope [data-menu-anim-in] [data-menu-anim-in])")}async show(){if(this.open)return;this.open=!0,this.wrapper.style.display=null,this.wrapper.style.transform="translateX(100%)",this.getAnimInElements().forEach((n,s)=>P(n,"fade-in",{delay:s*60+120,fill:"backwards"}));const t=this.closest(".mobile-menu-content"),e=[{transform:["none","translateX(-100%)"]},{duration:251,easing:"ease"}];await t.animate(...e).finished.catch(()=>{}),this.wrapper.style.transform=null}async hide({anim:t=!0}={}){if(this.open){if(this.open=!1,t){this.wrapper.style.transform="translateX(100%)";const e=this.closest(".mobile-menu-content"),n=[{transform:["translateX(-100%)","none"]},{duration:250,easing:"ease"}];await e.animate(...n).finished.catch(()=>{})}this.wrapper.style.display="none"}}hideMenu(){this.closest("[data-mobile-menu]").hide()}isRootPage(){return!this.parentElement.closest("mobile-menu-page")}}customElements.define("mobile-menu-page",Hr);class Br extends At{afterHide(){super.afterHide(),this.querySelectorAll("mobile-menu-page").forEach(t=>t.hide({anim:!1}))}getAnimInElements(){return this.querySelectorAll("[data-menu-anim-in]:not(:scope [data-menu-anim-in] [data-menu-anim-in])")}async show(){if(this.open)return;const t=this.querySelector(".mobile-menu-inner");t.classList.add("anim-in");const e=[];this.getAnimInElements().forEach((n,s)=>e.push(P(n,"fade-in",{delay:s*60+70,fill:"backwards"}))),Promise.all(e.map(n=>n.finished)).finally(()=>t.classList.remove("anim-in")),await super.show(...arguments)}}customElements.define("mobile-menu",Br);class Fr extends HTMLElement{connectedCallback(){this.activator=this.children[0],this.activator.addEventListener("click",this.onClickHandler.bind(this))}onClickHandler(t){if(this.getAttribute("enabled")==="false")return;const e=this.getTarget();e&&e.show&&(t.preventDefault(),e.setActivatorElement(this.activator),e.show())}getTarget(){return document.querySelector(this.getAttribute("target"))}}customElements.define("modal-trigger",Fr);const vi="essence:newsletter-popup-dismissed",$r="essence:newsletter-subscribed";class Wr extends Ft{connectedCallback(){if(super.connectedCallback(),this.getAttribute("overlay")==="false"&&(this.useFocusTrap=!1,this.useLockScroll=!1,this.useOverlay=!1),this.getAttribute("test-mode")==="true"&&!window.Shopify.designMode){this.show();return}if(window.location.pathname!=="/challenge"){if(window.Shopify.designMode){this.initDesignMode();return}if(this.content.querySelector(".message-success, .message-error"))this.show();else if(!this.isSubscribed()){const t=localStorage.getItem(vi);(!t||this.isTimestampExpired(t))&&(this.preloadImages(),setTimeout(()=>this.show(),this.showDelay))}}}isTimestampExpired(t){return(new Date().getTime()-t)/864e5>=this.frequency}isSubscribed(){return localStorage.getItem($r)}async show(){await super.show(...arguments)}afterHide(){return localStorage.setItem(vi,new Date().getTime()),super.afterHide(...arguments)}preloadImages(){this.querySelectorAll("img[loading=lazy]").forEach(t=>t.setAttribute("loading","eager"))}get showDelay(){return Number(this.getAttribute("show-delay")??5e3)}get frequency(){return Number(this.getAttribute("frequency")??30)}}customElements.define("newsletter-modal",Wr);class Vr extends HTMLElement{constructor(){super(),this.currentToken=null}connectedCallback(){this.notification=this.firstElementChild}async showNotification({text:t,type:e=null,time:n=4e3}){const s=Symbol();switch(this.currentToken=s,e){case"danger":this.notification.innerHTML=`<div class="message-danger text-danger">${t}</div>`;break;default:this.notification.textContent=t;break}this.style.display=null,await this.animate({transform:["translateY(150%)","none"]},{duration:250,easing:H}).finished,!(this.currentToken!==s||(await j(n),this.currentToken!==s)||(await this.animate({transform:["none","translateY(150%)"]},{duration:250,easing:se}).finished,this.currentToken!==s))&&(this.style.display="none")}}customElements.define("notification-wrapper",Vr);function D(i,t){const e=`<svg
      aria-hidden="true"
      focusable="false"
      class="spinner"
      viewBox="0 0 66 66"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
    </svg>
  `;if(t&&!i.dataset.loading){for(const n of i.children)n.style.opacity="0";i.style.color="transparent",i.insertAdjacentHTML("beforeend",e),i.classList.add("pointer-events-none"),i.dataset.loading=!0,i.setAttribute("aria-loading",!0)}else if(!t&&i.dataset.loading){const n=i.querySelector(".spinner");n==null||n.remove(),i.classList.remove("pointer-events-none"),i.style.color=null;for(const s of i.children)s.style.opacity=null;delete i.dataset.loading,i.removeAttribute("aria-loading")}}function jr(){document.addEventListener("submit",i=>{if("formButtonLoading"in i.target.dataset){const t=i.target.querySelector('button[type="submit"], input[type="submit"]');t&&D(t,!0)}})}class zr extends At{connectedCallback(){super.connectedCallback(),this.submitButton=this.content.querySelector("[type=submit]"),this.content.addEventListener("submit",this.onSubmit.bind(this))}async onSubmit(t){t.preventDefault(),D(this.submitButton,!0),t.target.querySelector(".message-danger").classList.add("hidden");const e=Object.fromEntries([...new FormData(t.target)]),n=await fetch(window.routes.cart_update_url,{...qt(),body:JSON.stringify(e)});D(this.submitButton,!1),n.ok?(this.hide(),this.dispatchEvent(new CustomEvent("order-note:updated",{detail:e,bubbles:!0}))):t.target.querySelector(".message-danger").classList.remove("hidden")}}customElements.define("order-note-drawer",zr);class Tn extends HTMLElement{constructor(){super()}connectedCallback(){this.parentSection=this.closest(".shopify-section"),this.querySelectorAll("a").forEach(t=>t.addEventListener("click",this.onLinkClicked.bind(this)))}async onLinkClicked(t){t.preventDefault(),this.updateActive(t);const e=t.currentTarget.href;this.parentSection.classList.add("loading"),await xt(this.parentSection,{url:e}),this.parentSection.offsetTop,this.parentSection.classList.remove("loading"),history.replaceState({},"",e)}updateActive(t){var e;(e=t.currentTarget.parentElement.querySelector(".active"))==null||e.classList.remove("active"),t.currentTarget.classList.add("active")}}customElements.define("section-dynamic-links",Tn);class Ur extends Tn{constructor(){super()}connectedCallback(){super.connectedCallback(),this.paginationEl=this.querySelector(".pagination"),this.headerSection=document.querySelector(".section-site-header")}async onLinkClicked(t){const n=this.paginationEl.querySelector(".active").compareDocumentPosition(t.currentTarget)&Node.DOCUMENT_POSITION_FOLLOWING;await super.onLinkClicked(t);const s=this.getAttribute("scroll-to-target");if(s&&n){const r=document.querySelector(s);r&&r.getBoundingClientRect().top<0&&window.scrollTo({top:window.scrollY+r.getBoundingClientRect().top-this.headerSection.clientHeight-64,behavior:"smooth"})}}updateActive(t){const e=this.paginationEl.querySelector(".active");e==null||e.classList.remove("active"),t.currentTarget.classList.contains("pagination-prev")?e.previousElementSibling.classList.add("active"):t.currentTarget.classList.contains("pagination-next")?e.nextElementSibling.classList.add("active"):t.currentTarget.parentElement.classList.add("active")}}customElements.define("pagination-dynamic",Ur);class Gr extends HTMLElement{connectedCallback(){this.loadMoreButton=this.querySelector(".button"),this.messageError=this.querySelector(".message-danger"),this.loadMoreButton.addEventListener("click",this.onLoadMoreClick.bind(this))}async onLoadMoreClick(t){t.preventDefault(),this.messageError.textContent="",D(this.loadMoreButton,!0);const e=this.closest(".shopify-section"),n=e.id,s=e.querySelectorAll("[data-container-load-more] > *");try{const r=it();await xt(this.closest(".shopify-section"),{url:this.loadMoreButton.href}),document.getElementById(n).querySelector("[data-container-load-more]").prepend(...Array.from(s)),it()!==r&&window.scrollTo(0,r)}catch(r){console.error(r),this.messageError.textContent=window._t.pagination.load_more_error}finally{D(this.loadMoreButton,!1)}}}customElements.define("pagination-load-more",Gr);class Kr extends HTMLElement{constructor(){super(),this.rangeMin=this.querySelector("[data-range-min]"),this.rangeMax=this.querySelector("[data-range-max]"),this.rangeGroup=this.querySelector(".range-group"),this.inputMin=this.querySelector("[data-input-min]"),this.inputMax=this.querySelector("[data-input-max]"),this.rangeMin.addEventListener("input",this.onRangeMinInput.bind(this)),this.rangeMax.addEventListener("input",this.onRangeMaxInput.bind(this)),this.inputMin.addEventListener("change",this.onInputMinChange.bind(this)),this.inputMax.addEventListener("change",this.onInputMaxChange.bind(this)),this.updateRangeBackground()}onRangeMinInput(t){t.target.value=Math.min(t.target.value,Number(this.rangeMax.value)-1),this.updateRangeBackground(),this.inputMin.value=t.target.value}onRangeMaxInput(t){t.target.value=Math.max(t.target.value,Number(this.rangeMin.value)+1),this.updateRangeBackground(),this.inputMax.value=t.target.value}onInputMinChange(t){t.target.value&&Number(t.target.value)>=Number(this.inputMax.value||this.inputMax.max)&&(t.target.value=Number(this.inputMax.value||this.inputMax.max)-1),this.rangeMin.value=t.target.value||this.rangeMin.min,this.updateRangeBackground()}onInputMaxChange(t){t.target.value&&Number(t.target.value)<=Number(this.inputMin.value||this.inputMin.min)&&(t.target.value=Number(this.inputMin.value||this.inputMin.min)+1),this.rangeMax.value=t.target.value||this.rangeMax.max,this.updateRangeBackground()}getRangePercentage(t){return(t.value-t.min)/(t.max-t.min)*100}updateRangeBackground(){this.rangeGroup.style.setProperty("--range-min",this.getRangePercentage(this.rangeMin)+"%"),this.rangeGroup.style.setProperty("--range-max",this.getRangePercentage(this.rangeMax)+"%")}}customElements.define("price-range",Kr);class Xr extends Ft{constructor(){super(),this.useFocusTrap=!1,this.useLockScroll=!1,this.useOverlay=!1}connectedCallback(){super.connectedCallback(),this.buttonAccept=this.querySelector(".button-accept"),this.buttonDecline=this.querySelector(".button-decline"),window.Shopify.designMode?this.initDesignMode():(this.buttonAccept.addEventListener("click",()=>this.handleAccept()),this.buttonDecline.addEventListener("click",()=>this.handleDecline()),this.loadShopifyFeatures())}loadShopifyFeatures(){window.Shopify.loadFeatures([{name:"consent-tracking-api",version:"0.1"}],t=>{if(t)throw t;this.api=window.Shopify.customerPrivacy,this.checkForBanner()})}async checkForBanner(){const t=this.api.currentVisitorConsent();if(this.api.shouldShowBanner()&&(t.marketing===""||t.analytics===""||t.preferences==="")){if(await j(1500),document.getElementById("shopify-pc__banner"))return;this.show()}}handleAccept(){D(this.buttonAccept,!0),this.api.setTrackingConsent({analytics:!0,marketing:!0,preferences:!0},()=>{this.hide(),D(this.buttonAccept,!1)})}handleDecline(){D(this.buttonDecline,!0),this.api.setTrackingConsent({analytics:!1,marketing:!1,preferences:!1},()=>{this.hide(),D(this.buttonDecline,!1)})}}customElements.define("privacy-banner",Xr);class Yr extends HTMLElement{constructor(){super(),this.currentToken=null,this.loadedImages=new Map,this.preloadedTemplates=new Set}connectedCallback(){this.target=this.closest(".product-card"),this.addEventListener("change",t=>{t.target.type==="radio"&&this.changeProductImage(t.target)}),this.querySelectorAll("input[type=radio] + label").forEach(t=>t.addEventListener("mouseover",e=>{if("ontouchstart"in window)return;const n=document.getElementById(e.currentTarget.htmlFor);this.preloadTemplate(n.parentNode.querySelector("template"))}))}changeProductImage(t){var a,o;t.dataset.url&&this.updateCardHref(t);const e=t.parentNode.querySelector("template");if(!e)return;const n=Symbol();this.currentToken=n;const s=this.target.querySelector(".product-card__image");if(s&&this.loadedImages.has(e.id)){s.replaceWith(this.loadedImages.get(e.id));return}const r=(a=e.content.querySelector("img"))==null?void 0:a.cloneNode(!0);r&&(this.target.classList.add("product-card--media-loading"),s.replaceWith(r),(o=this.target.querySelector(".product-card__second-image"))==null||o.remove(),r.addEventListener("load",()=>{this.loadedImages.set(e.id,r),n===this.currentToken&&this.target.classList.remove("product-card--media-loading")}))}async preloadTemplate(t){if(!t||this.preloadedTemplates.has(t.id))return;const e=t.content.querySelector("img");e&&(De(e),this.preloadedTemplates.add(t.id))}updateCardHref(t){this.target.querySelectorAll("a:not([data-size-link])").forEach(e=>e.href=t.dataset.url)}}customElements.define("product-card-swatches",Yr);class Jr extends HTMLElement{constructor(){super(),this.form=this.querySelector("form"),this.form.querySelector("[name=id]").disabled=!1,this.form.addEventListener("submit",this.onSubmitHandler.bind(this)),this.submitButton=this.querySelector('[type="submit"]')}connectedCallback(){this.quickAddModal=this.closest("#quick-add-modal")}async onSubmitHandler(t){if(t.preventDefault(),!t.target.reportValidity())return;const e=this.querySelector("[name=id]").value;this.setErrorMessage(),D(this.submitButton,!0);const n=new FormData(this.form),s=["cart-bubble"];(window.addedToCartNotification!=="drawer"||this.quickAddModal)&&s.push("variant-added"),n.append("sections",s),n.append("sections_url",`${window.Shopify.routes.root}variants/${e}`);try{const a=await(await fetch(`${window.routes.cart_add_url}`,{...qt("form-data"),body:n})).json();if(a.status){this.setErrorMessage(a.description),this.error=!0;return}if(document.dispatchEvent(new CustomEvent("product:added-to-cart",{detail:{id:e,quantity:n.get("quantity")}})),a.sections["cart-bubble"]&&qe(a.sections["cart-bubble"].match(/\d+/)),this.quickAddModal){const o=document.querySelector(".section-main-cart");o&&xt(o),this.isCartModalOpen()?this.quickAddModal.hide():await this.quickAddModal.showAddedToCartView({html:Qi(a.sections["variant-added"]).innerHTML,key:a.key})}else window.addedToCartNotification==="drawer"?await en():await nn({html:a.sections["variant-added"],key:a.key});this.error=!1}catch(r){console.error(r)}finally{D(this.submitButton,!1)}}setErrorMessage(t=!1){this.errorMessage=this.errorMessage||this.querySelector(".message-danger"),this.errorMessage&&(this.errorMessage.classList.toggle("hidden",!t),typeof t=="object"?this.errorMessage.replaceChildren(...Object.values(t).map(e=>[document.createTextNode(e),document.createElement("br")]).flat().slice(0,-1)):this.errorMessage.textContent=t||"",t&&Ki(this.errorMessage))}isCartModalOpen(){var t;return(t=document.querySelector("cart-modal"))==null?void 0:t.open}}customElements.define("product-form",Jr);class Qr extends fn{constructor(){super()}loadContent(){super.loadContent(),window.Shopify.loadFeatures([{name:"model-viewer-ui",version:"1.0",onLoad:this.setupModelViewerUI.bind(this)}])}setupModelViewerUI(t){t||(this.modelViewerUI=new window.Shopify.ModelViewerUI(this.querySelector("model-viewer")))}}customElements.define("product-model",Qr);window.ProductModel={loadShopifyXR(){window.Shopify.loadFeatures([{name:"shopify-xr",version:"1.0",onLoad:this.setupShopifyXR.bind(this)}])},setupShopifyXR(i){if(!i){if(!window.ShopifyXR){document.addEventListener("shopify_xr_initialized",()=>this.setupShopifyXR());return}document.querySelectorAll('[id^="Product-JSON-"]').forEach(t=>{window.ShopifyXR.addModels(JSON.parse(t.textContent)),t.remove()}),window.ShopifyXR.setupXRElements()}}};window.addEventListener("DOMContentLoaded",()=>{window.ProductModel&&window.ProductModel.loadShopifyXR()});class Zr extends HTMLElement{connectedCallback(){this.setupObserver()}disconnectedCallback(){this.observer.disconnect()}setupObserver(){this.observer&&this.observer.disconnect(),this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:`0px 0px ${this.threshold} 0px`}),this.observer.observe(this)}async handleIntersection([t],e){var n;if(t.isIntersecting){e.unobserve(this);try{(n=window.Shopify)!=null&&n.designMode&&await j(250);const r=await(await fetch(this.getURL())).text();this.replaceChildren(...pt(r).querySelectorAll("product-recommendations > *"))}catch(s){console.error(s)}}}getURL(){const t=new URL(window.routes.product_recommendations_url,window.location.origin);return t.searchParams.append("section_id",this.getAttribute("section-id")),t.searchParams.append("product_id",this.getAttribute("product-id")),t.searchParams.append("limit",this.getAttribute("limit")),t.searchParams.append("intent",this.getAttribute("intent")),t}get threshold(){return`${this.getAttribute("threshold")??600}px`}}customElements.define("product-recommendations",Zr);class ta extends HTMLElement{constructor(){super()}connectedCallback(){}}customElements.define("product-section",ta);class ea extends HTMLElement{constructor(){super(),this.subs=[]}connectedCallback(){var t;this.sectionId=(t=this.closest(".shopify-section"))==null?void 0:t.id,this.thumbs=Array.from(this.querySelectorAll("button")),this.thumbs.forEach(e=>e.addEventListener("click",this.onThumbnailClick.bind(this))),this.connectWithCarousel()}disconnectedCallback(){this.subs.forEach(t=>q.unsubscribe(t))}connectWithCarousel(){this.carousel=document.querySelector("media-carousel"),this.carousel&&this.subs.push(q.subscribe(le,(t,e)=>{e.sectionId===this.sectionId&&this.updateActiveThumbnail(e.mediaId)}))}onThumbnailClick(t){this.updateActiveThumbnail(t.currentTarget.dataset.mediaId),q.publish(zt,{mediaId:t.currentTarget.dataset.mediaId,sectionId:this.sectionId})}updateActiveThumbnail(t){const e=this.thumbs.find(n=>n.dataset.mediaId===t);this.thumbs.forEach(n=>n.classList.remove("active")),e.classList.add("active"),this.dispatchEvent(new CustomEvent("carousel:update-active",{detail:{el:e},bubbles:!0}))}}customElements.define("product-thumbnails",ea);class ia extends HTMLElement{constructor(){super()}connectedCallback(){this.countrySelect=this.querySelector("[data-country-select]"),this.provinceSelect=this.querySelector("[data-province-select]"),this.provinceWrapper=this.provinceSelect.parentElement,this.addEventListeners(),this.populateProvinces()}addEventListeners(){this.countrySelect.addEventListener("change",()=>{this.populateProvinces()})}populateProvinces(){const t=this.countrySelect.options[this.countrySelect.selectedIndex],e=JSON.parse(t.getAttribute("data-provinces"));this.provinceSelect.innerHTML="",e.length===0?this.provinceWrapper.classList.add("hidden"):(e.forEach(n=>{const s=document.createElement("option");s.value=n[0],s.text=n[1],this.provinceSelect.add(s)}),this.provinceWrapper.classList.remove("hidden"))}}customElements.define("province-selector",ia);class na extends HTMLElement{connectedCallback(){this.decButton=this.querySelector("[data-dec]"),this.incButton=this.querySelector("[data-inc]"),this.input=this.querySelector(":scope > input");const t=new Event("change",{bubbles:!0});this.decButton.addEventListener("click",()=>{this.input.stepDown(),this.input.dispatchEvent(t)}),this.incButton.addEventListener("click",()=>{this.input.stepUp(),this.input.dispatchEvent(t)}),this.input.addEventListener("change",e=>{this.input.value.trim()===""&&(e.stopPropagation(),this.input.value="1",this.input.dispatchEvent(t))})}}customElements.define("qty-selector",na);async function sa(i,t=null){let e=`${window.Shopify.routes.root}products/${i}`;return t&&(e+=`?variant=${t}`),await(await fetch(e)).text()}function Ie(i){const t=new DOMParser().parseFromString(i,"text/html").querySelector(".section-main-product"),e=re(t);t.innerHTML=t.innerHTML.replace(new RegExp(e,"g"),"product-quick-add-modal");const n=t.querySelector("variant-picker");return n&&(n.dataset.section=e),t}class ra extends HTMLElement{constructor(){super(),this.addEventListener("click",this.onClickHandler.bind(this))}get button(){return this.querySelector("button")}get inCartModal(){return this.closest("cart-modal")}get inProductCard(){return this.closest(".product-card")}async onClickHandler(){const t=this.getAttribute("variant-id");try{if(D(this.button,!0),t){const e=await os({id:t,quantity:1});if(!e)return;this.inCartModal||await ls({html:e.sections["variant-added"],key:e.key})}else{const e=await sa(this.getAttribute("handle"),this.getSelectedVariantId()),n=Ie(e).querySelector(".product-info");cs(n)}}catch(e){console.error(e)}finally{D(this.button,!1)}}getSelectedVariantId(){var t,e;return(e=(t=this.inProductCard)==null?void 0:t.querySelector("[data-variant-id]:checked"))==null?void 0:e.getAttribute("data-variant-id")}}customElements.define("quick-add-button",ra);class aa extends Ft{constructor(){super(),this.productInfoContainer=this.querySelector("[data-product-info-container]"),this.mediaContainer=this.querySelector("[data-media-container]")}get cartModal(){return document.querySelector("cart-modal")}connectedCallback(){super.connectedCallback(),this.closeButton=this.querySelector("[data-button-close]")}setProductInfo(t){var r,a;t.querySelectorAll(`.product-info-inner > *:not(
        .product-quick-add-header-template,
        .product-variant-picker,
        .product-stock-info,
        .product-buy-buttons
      )`).forEach(o=>o.remove());const e=t.querySelector(".product-quick-add-header-template");e&&((r=t.querySelector(".product-info-inner"))==null||r.prepend(e.content.firstElementChild.cloneNode(!0)));const n=t.querySelector("#quick-add-media");n.remove(),this.mediaContainer.replaceChildren(...n.content.children),this.productInfoContainer.innerHTML=t.outerHTML,this.mediaContainer.style.display=null,this.content.style.display=null,this.panel.classList.remove("md:max-w-[640px]"),(a=window.Shopify)==null||a.PaymentButton.init();const s=this.querySelector("media-carousel");s&&!this.open&&(this.panelWrapper.style.display="flex",s.updateMaxHeight(),this.panelWrapper.style.display=null)}async showAddedToCartView({html:t,key:e}){var n,s;if(window.addedToCartNotification==="drawer"){await((n=this.cartModal)==null?void 0:n.reloadContent()),await this.hide(),await((s=this.cartModal)==null?void 0:s.show());return}pt(t).querySelectorAll("img").forEach(r=>De(r)),await P(this.panel,"fade-out",{duration:250,y:"1rem",easing:se}).finished,this.mediaContainer.style.display="none",this.content.style.display="block",this.productInfoContainer.innerHTML=`<div class="w-full">${t}</div>`,this.panel.classList.add("md:max-w-[640px]"),e&&this.productInfoContainer.querySelectorAll(`[data-cart-key]:not([data-cart-key="${e}"])`).forEach(r=>r.remove()),await P(this.panel,"fade-in",{duration:250,delay:70,fill:"backwards",y:window.matchMedia("(min-width: 768px)").matches?"-1rem":"1rem",easing:H}).finished}afterHide(){super.afterHide(),this.closeButton.style.opacity=null}}customElements.define("quick-add-modal",aa);class oa extends HTMLElement{connectedCallback(){this.getViewedProductIds().length!==0&&this.setupObserver()}disconnectedCallback(){this.observer.disconnect()}setupObserver(){this.observer&&this.observer.disconnect(),this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:`0px 0px ${this.threshold} 0px`}),this.observer.observe(this)}async handleIntersection([t],e){var n;if(t.isIntersecting){e.unobserve(this);try{(n=window.Shopify)!=null&&n.designMode&&await j(250);const r=await(await fetch(this.getURL())).text();this.replaceChildren(...pt(r).querySelectorAll("recently-viewed-products > *"))}catch(s){console.error(s)}}}getViewedProductIds(){const t=new Set(this.getAttribute("product-id").split(","));return JSON.parse(localStorage.getItem("essence:recently-viewed-products")??"[]").filter(e=>!t.has(e.toString()))}getSearchQuery(){return this.getViewedProductIds().map(t=>`id:${t}`).join(" OR ")}getURL(){const t=new URL(window.routes.search_url,window.location.origin);return t.searchParams.append("section_id",this.getAttribute("section-id")),t.searchParams.append("q",this.getSearchQuery()),t.searchParams.append("type","product"),t}get threshold(){return`${this.getAttribute("threshold")??600}px`}}customElements.define("recently-viewed-products",oa);class la extends HTMLElement{connectedCallback(){this.checkboxInput=this.querySelector(".recipient-form__input-checkbox"),this.checkboxInput.disabled=!1,this.hiddenControlField=this.querySelector(".recipient-form__input-hidden-control"),this.hiddenControlField.disabled=!0,this.offsetProperty=this.querySelector(".recipient-form__input-offset"),this.offsetProperty&&(this.offsetProperty.value=new Date().getTimezoneOffset().toString()),this.inputsWrapper=this.querySelector(".recipient-form__inputs"),this.addEventListener("change",this.onChange.bind(this)),this.onChange()}onChange(){this.checkboxInput.checked?(this.enableInputFields(),this.showForm()):(this.disableInputFields(),this.hideForm())}disableableFields(){return[...this.querySelectorAll(".input"),this.offsetProperty]}enableInputFields(){this.disableableFields().forEach(t=>t.disabled=!1)}disableInputFields(){this.disableableFields().forEach(t=>t.disabled=!0)}showForm(){this.inputsWrapper.style.display="block"}hideForm(){this.inputsWrapper.style.display=null}}customElements.define("recipient-form",la);var ca=typeof global=="object"&&global&&global.Object===Object&&global;const Ln=ca;var da=typeof self=="object"&&self&&self.Object===Object&&self,ha=Ln||da||Function("return this")();const J=ha;var ua=J.Symbol;const at=ua;var In=Object.prototype,fa=In.hasOwnProperty,pa=In.toString,Ot=at?at.toStringTag:void 0;function ma(i){var t=fa.call(i,Ot),e=i[Ot];try{i[Ot]=void 0;var n=!0}catch{}var s=pa.call(i);return n&&(t?i[Ot]=e:delete i[Ot]),s}var ga=Object.prototype,ya=ga.toString;function va(i){return ya.call(i)}var ba="[object Null]",wa="[object Undefined]",bi=at?at.toStringTag:void 0;function Tt(i){return i==null?i===void 0?wa:ba:bi&&bi in Object(i)?ma(i):va(i)}function Ct(i){return i!=null&&typeof i=="object"}var Sa="[object Symbol]";function Ve(i){return typeof i=="symbol"||Ct(i)&&Tt(i)==Sa}function Ea(i,t){for(var e=-1,n=i==null?0:i.length,s=Array(n);++e<n;)s[e]=t(i[e],e,i);return s}var Ca=Array.isArray;const G=Ca;var xa=1/0,wi=at?at.prototype:void 0,Si=wi?wi.toString:void 0;function kn(i){if(typeof i=="string")return i;if(G(i))return Ea(i,kn)+"";if(Ve(i))return Si?Si.call(i):"";var t=i+"";return t=="0"&&1/i==-xa?"-0":t}function je(i){var t=typeof i;return i!=null&&(t=="object"||t=="function")}function Aa(i){return i}var Ta="[object AsyncFunction]",La="[object Function]",Ia="[object GeneratorFunction]",ka="[object Proxy]";function Mn(i){if(!je(i))return!1;var t=Tt(i);return t==La||t==Ia||t==Ta||t==ka}var Ma=J["__core-js_shared__"];const ge=Ma;var Ei=function(){var i=/[^.]+$/.exec(ge&&ge.keys&&ge.keys.IE_PROTO||"");return i?"Symbol(src)_1."+i:""}();function Oa(i){return!!Ei&&Ei in i}var Pa=Function.prototype,Ra=Pa.toString;function mt(i){if(i!=null){try{return Ra.call(i)}catch{}try{return i+""}catch{}}return""}var Da=/[\\^$.*+?()[\]{}|]/g,qa=/^\[object .+?Constructor\]$/,_a=Function.prototype,Na=Object.prototype,Ha=_a.toString,Ba=Na.hasOwnProperty,Fa=RegExp("^"+Ha.call(Ba).replace(Da,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function $a(i){if(!je(i)||Oa(i))return!1;var t=Mn(i)?Fa:qa;return t.test(mt(i))}function Wa(i,t){return i==null?void 0:i[t]}function gt(i,t){var e=Wa(i,t);return $a(e)?e:void 0}var Va=gt(J,"WeakMap");const ke=Va;function ja(){}var za=function(){try{var i=gt(Object,"defineProperty");return i({},"",{}),i}catch{}}();const Ci=za;function Ua(i,t,e,n){for(var s=i.length,r=e+(n?1:-1);n?r--:++r<s;)if(t(i[r],r,i))return r;return-1}function Ga(i){return i!==i}function Ka(i,t,e){for(var n=e-1,s=i.length;++n<s;)if(i[n]===t)return n;return-1}function Xa(i,t,e){return t===t?Ka(i,t,e):Ua(i,Ga,e)}function Ya(i,t){var e=i==null?0:i.length;return!!e&&Xa(i,t,0)>-1}var Ja=9007199254740991,Qa=/^(?:0|[1-9]\d*)$/;function On(i,t){var e=typeof i;return t=t??Ja,!!t&&(e=="number"||e!="symbol"&&Qa.test(i))&&i>-1&&i%1==0&&i<t}function Za(i,t,e){t=="__proto__"&&Ci?Ci(i,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):i[t]=e}function Pn(i,t){return i===t||i!==i&&t!==t}var to=9007199254740991;function ze(i){return typeof i=="number"&&i>-1&&i%1==0&&i<=to}function Rn(i){return i!=null&&ze(i.length)&&!Mn(i)}var eo=Object.prototype;function io(i){var t=i&&i.constructor,e=typeof t=="function"&&t.prototype||eo;return i===e}function no(i,t){for(var e=-1,n=Array(i);++e<i;)n[e]=t(e);return n}var so="[object Arguments]";function xi(i){return Ct(i)&&Tt(i)==so}var Dn=Object.prototype,ro=Dn.hasOwnProperty,ao=Dn.propertyIsEnumerable,oo=xi(function(){return arguments}())?xi:function(i){return Ct(i)&&ro.call(i,"callee")&&!ao.call(i,"callee")};const qn=oo;function lo(){return!1}var _n=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ai=_n&&typeof module=="object"&&module&&!module.nodeType&&module,co=Ai&&Ai.exports===_n,Ti=co?J.Buffer:void 0,ho=Ti?Ti.isBuffer:void 0,uo=ho||lo;const Me=uo;var fo="[object Arguments]",po="[object Array]",mo="[object Boolean]",go="[object Date]",yo="[object Error]",vo="[object Function]",bo="[object Map]",wo="[object Number]",So="[object Object]",Eo="[object RegExp]",Co="[object Set]",xo="[object String]",Ao="[object WeakMap]",To="[object ArrayBuffer]",Lo="[object DataView]",Io="[object Float32Array]",ko="[object Float64Array]",Mo="[object Int8Array]",Oo="[object Int16Array]",Po="[object Int32Array]",Ro="[object Uint8Array]",Do="[object Uint8ClampedArray]",qo="[object Uint16Array]",_o="[object Uint32Array]",M={};M[Io]=M[ko]=M[Mo]=M[Oo]=M[Po]=M[Ro]=M[Do]=M[qo]=M[_o]=!0;M[fo]=M[po]=M[To]=M[mo]=M[Lo]=M[go]=M[yo]=M[vo]=M[bo]=M[wo]=M[So]=M[Eo]=M[Co]=M[xo]=M[Ao]=!1;function No(i){return Ct(i)&&ze(i.length)&&!!M[Tt(i)]}function Ho(i){return function(t){return i(t)}}var Nn=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Dt=Nn&&typeof module=="object"&&module&&!module.nodeType&&module,Bo=Dt&&Dt.exports===Nn,ye=Bo&&Ln.process,Fo=function(){try{var i=Dt&&Dt.require&&Dt.require("util").types;return i||ye&&ye.binding&&ye.binding("util")}catch{}}();const Li=Fo;var Ii=Li&&Li.isTypedArray,$o=Ii?Ho(Ii):No;const Hn=$o;var Wo=Object.prototype,Vo=Wo.hasOwnProperty;function jo(i,t){var e=G(i),n=!e&&qn(i),s=!e&&!n&&Me(i),r=!e&&!n&&!s&&Hn(i),a=e||n||s||r,o=a?no(i.length,String):[],l=o.length;for(var c in i)(t||Vo.call(i,c))&&!(a&&(c=="length"||s&&(c=="offset"||c=="parent")||r&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||On(c,l)))&&o.push(c);return o}function zo(i,t){return function(e){return i(t(e))}}var Uo=zo(Object.keys,Object);const Go=Uo;var Ko=Object.prototype,Xo=Ko.hasOwnProperty;function Yo(i){if(!io(i))return Go(i);var t=[];for(var e in Object(i))Xo.call(i,e)&&e!="constructor"&&t.push(e);return t}function Ue(i){return Rn(i)?jo(i):Yo(i)}var Jo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Qo=/^\w*$/;function Ge(i,t){if(G(i))return!1;var e=typeof i;return e=="number"||e=="symbol"||e=="boolean"||i==null||Ve(i)?!0:Qo.test(i)||!Jo.test(i)||t!=null&&i in Object(t)}var Zo=gt(Object,"create");const Nt=Zo;function tl(){this.__data__=Nt?Nt(null):{},this.size=0}function el(i){var t=this.has(i)&&delete this.__data__[i];return this.size-=t?1:0,t}var il="__lodash_hash_undefined__",nl=Object.prototype,sl=nl.hasOwnProperty;function rl(i){var t=this.__data__;if(Nt){var e=t[i];return e===il?void 0:e}return sl.call(t,i)?t[i]:void 0}var al=Object.prototype,ol=al.hasOwnProperty;function ll(i){var t=this.__data__;return Nt?t[i]!==void 0:ol.call(t,i)}var cl="__lodash_hash_undefined__";function dl(i,t){var e=this.__data__;return this.size+=this.has(i)?0:1,e[i]=Nt&&t===void 0?cl:t,this}function ft(i){var t=-1,e=i==null?0:i.length;for(this.clear();++t<e;){var n=i[t];this.set(n[0],n[1])}}ft.prototype.clear=tl;ft.prototype.delete=el;ft.prototype.get=rl;ft.prototype.has=ll;ft.prototype.set=dl;function hl(){this.__data__=[],this.size=0}function ce(i,t){for(var e=i.length;e--;)if(Pn(i[e][0],t))return e;return-1}var ul=Array.prototype,fl=ul.splice;function pl(i){var t=this.__data__,e=ce(t,i);if(e<0)return!1;var n=t.length-1;return e==n?t.pop():fl.call(t,e,1),--this.size,!0}function ml(i){var t=this.__data__,e=ce(t,i);return e<0?void 0:t[e][1]}function gl(i){return ce(this.__data__,i)>-1}function yl(i,t){var e=this.__data__,n=ce(e,i);return n<0?(++this.size,e.push([i,t])):e[n][1]=t,this}function Q(i){var t=-1,e=i==null?0:i.length;for(this.clear();++t<e;){var n=i[t];this.set(n[0],n[1])}}Q.prototype.clear=hl;Q.prototype.delete=pl;Q.prototype.get=ml;Q.prototype.has=gl;Q.prototype.set=yl;var vl=gt(J,"Map");const Ht=vl;function bl(){this.size=0,this.__data__={hash:new ft,map:new(Ht||Q),string:new ft}}function wl(i){var t=typeof i;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?i!=="__proto__":i===null}function de(i,t){var e=i.__data__;return wl(t)?e[typeof t=="string"?"string":"hash"]:e.map}function Sl(i){var t=de(this,i).delete(i);return this.size-=t?1:0,t}function El(i){return de(this,i).get(i)}function Cl(i){return de(this,i).has(i)}function xl(i,t){var e=de(this,i),n=e.size;return e.set(i,t),this.size+=e.size==n?0:1,this}function Z(i){var t=-1,e=i==null?0:i.length;for(this.clear();++t<e;){var n=i[t];this.set(n[0],n[1])}}Z.prototype.clear=bl;Z.prototype.delete=Sl;Z.prototype.get=El;Z.prototype.has=Cl;Z.prototype.set=xl;var Al="Expected a function";function Ke(i,t){if(typeof i!="function"||t!=null&&typeof t!="function")throw new TypeError(Al);var e=function(){var n=arguments,s=t?t.apply(this,n):n[0],r=e.cache;if(r.has(s))return r.get(s);var a=i.apply(this,n);return e.cache=r.set(s,a)||r,a};return e.cache=new(Ke.Cache||Z),e}Ke.Cache=Z;var Tl=500;function Ll(i){var t=Ke(i,function(n){return e.size===Tl&&e.clear(),n}),e=t.cache;return t}var Il=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,kl=/\\(\\)?/g,Ml=Ll(function(i){var t=[];return i.charCodeAt(0)===46&&t.push(""),i.replace(Il,function(e,n,s,r){t.push(s?r.replace(kl,"$1"):n||e)}),t});const Ol=Ml;function Pl(i){return i==null?"":kn(i)}function Bn(i,t){return G(i)?i:Ge(i,t)?[i]:Ol(Pl(i))}var Rl=1/0;function he(i){if(typeof i=="string"||Ve(i))return i;var t=i+"";return t=="0"&&1/i==-Rl?"-0":t}function Fn(i,t){t=Bn(t,i);for(var e=0,n=t.length;i!=null&&e<n;)i=i[he(t[e++])];return e&&e==n?i:void 0}function Dl(i,t,e){var n=i==null?void 0:Fn(i,t);return n===void 0?e:n}function ql(i,t){for(var e=-1,n=t.length,s=i.length;++e<n;)i[s+e]=t[e];return i}function _l(){this.__data__=new Q,this.size=0}function Nl(i){var t=this.__data__,e=t.delete(i);return this.size=t.size,e}function Hl(i){return this.__data__.get(i)}function Bl(i){return this.__data__.has(i)}var Fl=200;function $l(i,t){var e=this.__data__;if(e instanceof Q){var n=e.__data__;if(!Ht||n.length<Fl-1)return n.push([i,t]),this.size=++e.size,this;e=this.__data__=new Z(n)}return e.set(i,t),this.size=e.size,this}function K(i){var t=this.__data__=new Q(i);this.size=t.size}K.prototype.clear=_l;K.prototype.delete=Nl;K.prototype.get=Hl;K.prototype.has=Bl;K.prototype.set=$l;function Wl(i,t){for(var e=-1,n=i==null?0:i.length,s=0,r=[];++e<n;){var a=i[e];t(a,e,i)&&(r[s++]=a)}return r}function Vl(){return[]}var jl=Object.prototype,zl=jl.propertyIsEnumerable,ki=Object.getOwnPropertySymbols,Ul=ki?function(i){return i==null?[]:(i=Object(i),Wl(ki(i),function(t){return zl.call(i,t)}))}:Vl;const Gl=Ul;function Kl(i,t,e){var n=t(i);return G(i)?n:ql(n,e(i))}function Mi(i){return Kl(i,Ue,Gl)}var Xl=gt(J,"DataView");const Oe=Xl;var Yl=gt(J,"Promise");const Pe=Yl;var Jl=gt(J,"Set");const wt=Jl;var Oi="[object Map]",Ql="[object Object]",Pi="[object Promise]",Ri="[object Set]",Di="[object WeakMap]",qi="[object DataView]",Zl=mt(Oe),tc=mt(Ht),ec=mt(Pe),ic=mt(wt),nc=mt(ke),lt=Tt;(Oe&&lt(new Oe(new ArrayBuffer(1)))!=qi||Ht&&lt(new Ht)!=Oi||Pe&&lt(Pe.resolve())!=Pi||wt&&lt(new wt)!=Ri||ke&&lt(new ke)!=Di)&&(lt=function(i){var t=Tt(i),e=t==Ql?i.constructor:void 0,n=e?mt(e):"";if(n)switch(n){case Zl:return qi;case tc:return Oi;case ec:return Pi;case ic:return Ri;case nc:return Di}return t});const _i=lt;var sc=J.Uint8Array;const Ni=sc;var rc="__lodash_hash_undefined__";function ac(i){return this.__data__.set(i,rc),this}function oc(i){return this.__data__.has(i)}function Bt(i){var t=-1,e=i==null?0:i.length;for(this.__data__=new Z;++t<e;)this.add(i[t])}Bt.prototype.add=Bt.prototype.push=ac;Bt.prototype.has=oc;function lc(i,t){for(var e=-1,n=i==null?0:i.length;++e<n;)if(t(i[e],e,i))return!0;return!1}function $n(i,t){return i.has(t)}var cc=1,dc=2;function Wn(i,t,e,n,s,r){var a=e&cc,o=i.length,l=t.length;if(o!=l&&!(a&&l>o))return!1;var c=r.get(i),f=r.get(t);if(c&&f)return c==t&&f==i;var m=-1,y=!0,v=e&dc?new Bt:void 0;for(r.set(i,t),r.set(t,i);++m<o;){var b=i[m],d=t[m];if(n)var g=a?n(d,b,m,t,i,r):n(b,d,m,i,t,r);if(g!==void 0){if(g)continue;y=!1;break}if(v){if(!lc(t,function(p,S){if(!$n(v,S)&&(b===p||s(b,p,e,n,r)))return v.push(S)})){y=!1;break}}else if(!(b===d||s(b,d,e,n,r))){y=!1;break}}return r.delete(i),r.delete(t),y}function hc(i){var t=-1,e=Array(i.size);return i.forEach(function(n,s){e[++t]=[s,n]}),e}function Xe(i){var t=-1,e=Array(i.size);return i.forEach(function(n){e[++t]=n}),e}var uc=1,fc=2,pc="[object Boolean]",mc="[object Date]",gc="[object Error]",yc="[object Map]",vc="[object Number]",bc="[object RegExp]",wc="[object Set]",Sc="[object String]",Ec="[object Symbol]",Cc="[object ArrayBuffer]",xc="[object DataView]",Hi=at?at.prototype:void 0,ve=Hi?Hi.valueOf:void 0;function Ac(i,t,e,n,s,r,a){switch(e){case xc:if(i.byteLength!=t.byteLength||i.byteOffset!=t.byteOffset)return!1;i=i.buffer,t=t.buffer;case Cc:return!(i.byteLength!=t.byteLength||!r(new Ni(i),new Ni(t)));case pc:case mc:case vc:return Pn(+i,+t);case gc:return i.name==t.name&&i.message==t.message;case bc:case Sc:return i==t+"";case yc:var o=hc;case wc:var l=n&uc;if(o||(o=Xe),i.size!=t.size&&!l)return!1;var c=a.get(i);if(c)return c==t;n|=fc,a.set(i,t);var f=Wn(o(i),o(t),n,s,r,a);return a.delete(i),f;case Ec:if(ve)return ve.call(i)==ve.call(t)}return!1}var Tc=1,Lc=Object.prototype,Ic=Lc.hasOwnProperty;function kc(i,t,e,n,s,r){var a=e&Tc,o=Mi(i),l=o.length,c=Mi(t),f=c.length;if(l!=f&&!a)return!1;for(var m=l;m--;){var y=o[m];if(!(a?y in t:Ic.call(t,y)))return!1}var v=r.get(i),b=r.get(t);if(v&&b)return v==t&&b==i;var d=!0;r.set(i,t),r.set(t,i);for(var g=a;++m<l;){y=o[m];var p=i[y],S=t[y];if(n)var E=a?n(S,p,y,t,i,r):n(p,S,y,i,t,r);if(!(E===void 0?p===S||s(p,S,e,n,r):E)){d=!1;break}g||(g=y=="constructor")}if(d&&!g){var C=i.constructor,x=t.constructor;C!=x&&"constructor"in i&&"constructor"in t&&!(typeof C=="function"&&C instanceof C&&typeof x=="function"&&x instanceof x)&&(d=!1)}return r.delete(i),r.delete(t),d}var Mc=1,Bi="[object Arguments]",Fi="[object Array]",Kt="[object Object]",Oc=Object.prototype,$i=Oc.hasOwnProperty;function Pc(i,t,e,n,s,r){var a=G(i),o=G(t),l=a?Fi:_i(i),c=o?Fi:_i(t);l=l==Bi?Kt:l,c=c==Bi?Kt:c;var f=l==Kt,m=c==Kt,y=l==c;if(y&&Me(i)){if(!Me(t))return!1;a=!0,f=!1}if(y&&!f)return r||(r=new K),a||Hn(i)?Wn(i,t,e,n,s,r):Ac(i,t,l,e,n,s,r);if(!(e&Mc)){var v=f&&$i.call(i,"__wrapped__"),b=m&&$i.call(t,"__wrapped__");if(v||b){var d=v?i.value():i,g=b?t.value():t;return r||(r=new K),s(d,g,e,n,r)}}return y?(r||(r=new K),kc(i,t,e,n,s,r)):!1}function Ye(i,t,e,n,s){return i===t?!0:i==null||t==null||!Ct(i)&&!Ct(t)?i!==i&&t!==t:Pc(i,t,e,n,Ye,s)}var Rc=1,Dc=2;function qc(i,t,e,n){var s=e.length,r=s,a=!n;if(i==null)return!r;for(i=Object(i);s--;){var o=e[s];if(a&&o[2]?o[1]!==i[o[0]]:!(o[0]in i))return!1}for(;++s<r;){o=e[s];var l=o[0],c=i[l],f=o[1];if(a&&o[2]){if(c===void 0&&!(l in i))return!1}else{var m=new K;if(n)var y=n(c,f,l,i,t,m);if(!(y===void 0?Ye(f,c,Rc|Dc,n,m):y))return!1}}return!0}function Vn(i){return i===i&&!je(i)}function _c(i){for(var t=Ue(i),e=t.length;e--;){var n=t[e],s=i[n];t[e]=[n,s,Vn(s)]}return t}function jn(i,t){return function(e){return e==null?!1:e[i]===t&&(t!==void 0||i in Object(e))}}function Nc(i){var t=_c(i);return t.length==1&&t[0][2]?jn(t[0][0],t[0][1]):function(e){return e===i||qc(e,i,t)}}function Hc(i,t){return i!=null&&t in Object(i)}function Bc(i,t,e){t=Bn(t,i);for(var n=-1,s=t.length,r=!1;++n<s;){var a=he(t[n]);if(!(r=i!=null&&e(i,a)))break;i=i[a]}return r||++n!=s?r:(s=i==null?0:i.length,!!s&&ze(s)&&On(a,s)&&(G(i)||qn(i)))}function Fc(i,t){return i!=null&&Bc(i,t,Hc)}var $c=1,Wc=2;function Vc(i,t){return Ge(i)&&Vn(t)?jn(he(i),t):function(e){var n=Dl(e,i);return n===void 0&&n===t?Fc(e,i):Ye(t,n,$c|Wc)}}function jc(i){return function(t){return t==null?void 0:t[i]}}function zc(i){return function(t){return Fn(t,i)}}function Uc(i){return Ge(i)?jc(he(i)):zc(i)}function zn(i){return typeof i=="function"?i:i==null?Aa:typeof i=="object"?G(i)?Vc(i[0],i[1]):Nc(i):Uc(i)}function Gc(i,t,e,n){for(var s=-1,r=i==null?0:i.length;++s<r;){var a=i[s];t(n,a,e(a),i)}return n}function Kc(i){return function(t,e,n){for(var s=-1,r=Object(t),a=n(t),o=a.length;o--;){var l=a[i?o:++s];if(e(r[l],l,r)===!1)break}return t}}var Xc=Kc();const Yc=Xc;function Jc(i,t){return i&&Yc(i,t,Ue)}function Qc(i,t){return function(e,n){if(e==null)return e;if(!Rn(e))return i(e,n);for(var s=e.length,r=t?s:-1,a=Object(e);(t?r--:++r<s)&&n(a[r],r,a)!==!1;);return e}}var Zc=Qc(Jc);const td=Zc;function ed(i,t,e,n){return td(i,function(s,r,a){t(n,s,e(s),a)}),n}function id(i,t){return function(e,n){var s=G(e)?Gc:ed,r=t?t():{};return s(e,i,zn(n),r)}}function nd(i,t,e){for(var n=-1,s=i==null?0:i.length;++n<s;)if(e(t,i[n]))return!0;return!1}var sd=Object.prototype,rd=sd.hasOwnProperty,ad=id(function(i,t,e){rd.call(i,e)?i[e].push(t):Za(i,e,[t])});const od=ad;var ld=1/0,cd=wt&&1/Xe(new wt([,-0]))[1]==ld?function(i){return new wt(i)}:ja;const dd=cd;var hd=200;function ud(i,t,e){var n=-1,s=Ya,r=i.length,a=!0,o=[],l=o;if(e)a=!1,s=nd;else if(r>=hd){var c=t?null:dd(i);if(c)return Xe(c);a=!1,s=$n,l=new Bt}else l=t?[]:o;t:for(;++n<r;){var f=i[n],m=t?t(f):f;if(f=e||f!==0?f:0,a&&m===m){for(var y=l.length;y--;)if(l[y]===m)continue t;t&&l.push(m),o.push(f)}else s(l,m,e)||(l!==o&&l.push(m),o.push(f))}return o}function fd(i,t){return i&&i.length?ud(i,zn(t)):[]}class pd extends HTMLElement{get threshold(){return Number(this.getAttribute("threshold")??0)}get rootMargin(){return this.getAttribute("root-margin")??"0px"}get staggerDelay(){return Number(this.getAttribute("stagger-delay")??83.33)}constructor(){super(),this.animationTypes={default:"scroll-slide-in","fade-in":"fade-in"},this.init=this.init.bind(this)}connectedCallback(){window.enableRevealOnScrollAnimations&&(this.mainObserver=new IntersectionObserver(this.handleEntries.bind(this),{threshold:this.threshold,rootMargin:this.rootMargin}),this.initialLoadObserver=new IntersectionObserver(this.handleInitialLoad.bind(this),{threshold:0}),this.mutationObserver=new MutationObserver(this.handleMutations.bind(this)),document.readyState==="loading"?document.addEventListener("DOMContentLoaded",this.init):this.init())}disconnnectedCallback(){this.initialLoadObserver.disconnect(),this.mainObserver.disconnect(),this.mutationObserver.disconnect(),document.removeEventListener("DOMContentLoaded",this.init)}getTargetElements(){return[...this.querySelectorAll("[data-animation]")].filter(t=>t.closest("scroll-animate")===this)}init(){this.getTargetElements().forEach(t=>{this.initialLoadObserver.observe(t)}),this.mutationObserver.observe(this,{childList:!0,subtree:!0})}async handleEntries(t){const e=new Set,n=t.filter(s=>s.isIntersecting&&s.target.dataset.animationGroupLazy!==void 0);t=t.filter(s=>{if(!s.isIntersecting||s.target.dataset.animationGroupLazy!==void 0)return!1;const r=s.target.dataset.animationGroup;return r&&!e.has(r)?(e.add(r),!0):!r}),t.forEach(s=>this.animateElement(s.target)),n.length>0&&Object.values(od(n,s=>s.target.dataset.animationGroup)).forEach(s=>this.animateStagger(s.map(r=>r.target)))}handleInitialLoad(t){this.handleEntries(t),this.initialLoadObserver.disconnect(),this.getTargetElements().forEach(e=>{this.mainObserver.observe(e)})}async animateElement(t){if(t.dataset.animation===void 0||t.dataset.animationPlaying)return;const e=t.dataset.animationGroup,n=this.getAnimationFromElement(t);if(e){const s=this.querySelectorAll(`[data-animation-group="${e}"]`);this.animateStagger(s,n)}else t.dataset.animationPlaying=!0,await n(t,{delay:Number(t.dataset.animationDelay??0),...t.dataset.animationParams?JSON.parse(t.dataset.animationParams):{}}).finished,this.finishElement(t)}getAnimationFromElement(t){const e=t.dataset.animation||"default",n=this.animationTypes[e]??"scroll-slide-in";return sn(n)}animateStagger(t){var n;let e=0;(n=t[0])!=null&&n.closest("[data-animation-auto-order]")&&(t=this.orderElementsForAnimation([...t]));for(let s of t){if(s.dataset.animationPlaying)return;s.dataset.animationPlaying=!0;const r=this.getAnimationFromElement(s),a=s.dataset.animationParams?JSON.parse(s.dataset.animationParams):{};r(s,{...a,delay:(a.delay??0)+e}).finished.then(()=>this.finishElement(s)),e+=this.staggerDelay}}finishElement(t){this.mainObserver.unobserve(t),delete t.dataset.animation,delete t.dataset.animationGroup,delete t.dataset.animationPlaying}async handleMutations(t){t.forEach(e=>{e.type==="childList"&&e.addedNodes.forEach(n=>{if(n.nodeType===Node.ELEMENT_NODE){let s=[...n.querySelectorAll("[data-animation]")];n.hasAttribute("data-animation")&&s.push(n),s.filter(r=>r.closest("scroll-animate")===this).forEach(r=>{this.shouldAnimateAddedElement(r)?this.mainObserver.observe(r):this.finishElement(r)})}})})}shouldAnimateAddedElement(t){return t.dataset.animationOnInitOnly===void 0}orderElementsForAnimation(t){const e=(a,o,l,c)=>Math.sqrt(Math.pow(l-a,2)+Math.pow(c-o,2)),n=[],r=t[0].closest("[data-animation-auto-order]").getBoundingClientRect();for(t=t.map(a=>{const o=a.getBoundingClientRect();return{el:a,rect:o,distance:e(r.left,r.top,o.left,o.top)}}),t.sort((a,o)=>a.distance-o.distance),n.push(t[0]);t.length>1;){const a=n[n.length-1];t=t.slice(1).map(o=>({...o,distance:e(a.rect.left,a.rect.top,o.rect.left,o.rect.top)})),t.sort((o,l)=>o.distance-l.distance),n.push(t[0])}return n.map(a=>a.el)}}customElements.define("scroll-animate",pd);class md extends Yi{get rtl(){return getComputedStyle(this).direction==="rtl"}connectedCallback(){super.connectedCallback(),this.addEventListener("carousel:update-active",this.onCarouselUpdateActive.bind(this))}goPrev(){this.goItem(this.getPrevItemTarget())}goNext(){this.goItem(this.getNextItemTarget())}goItem(t){this.triggerScroll(this.scrollAmount()+this.getDistance(t))}getDistance(t){const e=t.getBoundingClientRect(),n=this.getBoundingClientRectWithoutPadding();return this.vertical?e.top-n.top:this.rtl?(n.right-e.right)*-1:e.left-n.left}getBoundingClientRectWithoutPadding(){const t=getComputedStyle(this),e=this.getBoundingClientRect().toJSON(),n=parseFloat(t.paddingLeft),s=parseFloat(t.paddingRight),r=parseFloat(t.paddingTop),a=parseFloat(t.paddingBottom);return{left:e.left+n,right:e.right-s,top:e.top+r,bottom:e.bottom-a,width:e.width-n-s,height:e.height-r-a}}detectSpacing(){if(this.items.length<2)return 0;const t=this.items[0].getBoundingClientRect(),e=this.items[1].getBoundingClientRect();return this.vertical?Math.abs(e.top-t.bottom):this.rtl?Math.abs(e.right-t.left):Math.abs(e.left-t.right)}getPrevItemTarget(){const t=this.getBoundingClientRectWithoutPadding(),e=this.detectSpacing();for(let n of this.items){const s=n.getBoundingClientRect();if(this.vertical){if(s.top+1>=t.top-t.height-e&&s.bottom+1>=t.top-t.height-e)return n}else{if(!this.rtl&&s.left+1>=t.left-t.width-e&&s.right+1>=t.left-t.width-e)return n;if(this.rtl&&s.right-1<=t.right+t.width+e&&s.left-1<=t.right+t.width+e)return n}}return this.items[0]}getNextItemTarget(){const t=this.getBoundingClientRectWithoutPadding();for(let e of this.items){const n=e.getBoundingClientRect();if(this.vertical){if(n.top-1>=t.bottom||n.bottom-1>=t.bottom)return e}else{if(!this.rtl&&(n.left-1>=t.right||n.right-1>=t.right))return e;if(this.rtl&&(n.right+1<=t.left||n.left+1<=t.left))return e}}return this.items[this.items.length-1]}onCarouselUpdateActive(t){t.stopPropagation();const e=t.detail.el.getBoundingClientRect(),n=this.getBoundingClientRectWithoutPadding();let s;const r=.1;this.vertical?s=e.top+r>=n.top&&e.bottom-r<=n.bottom:s=e.left+r>=n.left&&e.right-r<=n.right,s||this.goItem(t.detail.el)}}customElements.define("scroll-carousel",md);class gd extends At{constructor(){super(),this.performSearchDebounced=z(this.performSearch.bind(this),250),this.searchCache=new Map,this.fadeOnMobile=!1}connectedCallback(){super.connectedCallback(),this.setupElements(),this.setupListeners(),window.Shopify.designMode&&this.initDesignMode()}setupElements(){this.searchInput=this.content.querySelector("input[type=search]"),this.resultsContainer=this.content.querySelector("[data-results-container]"),this.skeleton=this.content.querySelector("[data-skeleton]"),this.placeholder=this.content.querySelector("[data-placeholder]")}setupListeners(){this.searchInput.addEventListener("input",this.onSearchInput.bind(this))}onSearchInput(){var t;if(!this.searchInput.value.trim()){(t=this.abortController)==null||t.abort(),this.hideSkeleton(),this.clearResults();return}this.trySearchCache()||(this.clearResults(),this.showSkeleton(),this.performSearchDebounced())}trySearchCache(){const t=this.getSearchQuery();return this.searchCache.has(t)?(this.showResults(this.searchCache.get(t)),!0):!1}async performSearch(){var s;(s=this.abortController)==null||s.abort(),this.abortController=new AbortController;const t=this.getSearchQuery();if(!t.trim())return;const n=await(await fetch(this.getSearchURL(t),{signal:this.abortController.signal})).text();this.searchCache.set(t,n),this.showResults(n)}getSearchQuery(){return this.searchInput.value}showSkeleton(){this.hidePlaceholder(),this.skeleton.classList.remove("hidden")}hideSkeleton(){this.skeleton.classList.add("hidden")}showPlaceholder(){var t;(t=this.placeholder)==null||t.classList.remove("hidden")}hidePlaceholder(){var t;(t=this.placeholder)==null||t.classList.add("hidden")}showResults(t){this.hideSkeleton(),this.hidePlaceholder(),this.resultsContainer.classList.remove("hidden"),this.resultsContainer.replaceChildren(...pt(t).children)}clearResults(){this.resultsContainer.classList.add("hidden"),this.resultsContainer.replaceChildren(),this.showPlaceholder()}clearInput(){this.searchInput.value=""}getSearchURL(t){const e=new URL(window.routes.predictive_search_url,window.location.origin);return e.searchParams.append("q",t),e.searchParams.append("section_id","predictive-search"),e}async afterHide(){await super.afterHide(),this.clearInput(),this.clearResults(),this.hideSkeleton(),this.showPlaceholder(),this.savedFilterValue=null}async show({load:t}={}){await super.show(...arguments),window.Shopify.designMode&&!t&&this.searchInput.focus()}getCornerRadius(){return un("xl")||this.classList.contains("modal-search--full-width")?"0":"0 0 var(--block-corner-radius) var(--block-corner-radius)"}}customElements.define("search-modal",gd);class yd extends HTMLElement{constructor(){super(),this.selectEl=this.querySelector("select"),this.updateSelectWidth=this.updateSelectWidth.bind(this),this.updateSelectWidthDebounce=z(this.updateSelectWidth,100)}connectedCallback(){this.selectEl.addEventListener("change",this.updateSelectWidth),window.addEventListener("load",this.updateSelectWidth),window.addEventListener("resize",this.updateSelectWidthDebounce),this.updateSelectWidth()}disconnectedCallback(){this.selectEl.removeEventListener("change",this.updateSelectWidth),window.removeEventListener("load",this.updateSelectWidth),window.removeEventListener("resize",this.updateSelectWidthDebounce)}updateSelectWidth(){const e=this.selectEl.options[this.selectEl.selectedIndex].cloneNode(!0),n=document.createElement("span");n.textContent=e.textContent;const s=window.getComputedStyle(this.selectEl);n.style.font=s.font,n.style.padding=s.padding,n.style.display="inline-block",n.style.visibility="hidden",n.style.position="absolute",n.style.top="-9999px",this.appendChild(n);const r=n.offsetWidth;this.removeChild(n),this.selectEl.style.width=r+"px"}}customElements.define("select-dynamic-width",yd);class vd extends xn{async show(){if(navigator.share){const t={title:this.getAttribute("text"),url:this.getAttribute("url")};await navigator.share(t).catch(()=>{});return}return super.show(...arguments)}}customElements.define("share-dropdown",vd);class Wi extends Error{}class bd extends HTMLElement{constructor(){super()}connectedCallback(){this.form=this.querySelector("form"),this.countrySelect=this.querySelector("[name=country]"),this.provinceSelect=this.querySelector("[name=province]"),this.zipcodeInput=this.querySelector("[name=zipcode]"),this.shippingRates=this.querySelector("[data-shipping-rates-container]"),this.errorMessageTemplate=this.querySelector("template"),this.submitButton=this.querySelector("button"),this.addEventListeners()}addEventListeners(){this.form.addEventListener("submit",async t=>{var e;try{t.preventDefault(),(e=this.errorMessage)==null||e.remove(),this.displayShippingRates(),D(this.submitButton,!0);const n=await this.getShippingRates();this.displayShippingRates(n)}catch(n){this.errorMessage=this.errorMessageTemplate.content.cloneNode(!0).children[0],n instanceof Wi&&(this.errorMessage.textContent=n.message),this.errorMessageTemplate.after(this.errorMessage)}finally{D(this.submitButton,!1)}})}async getShippingRates(){const t=new URL(window.location.origin+"/cart/shipping_rates.json");t.searchParams.append("shipping_address[country]",this.countrySelect.value),t.searchParams.append("shipping_address[province]",this.provinceSelect.value),t.searchParams.append("shipping_address[zip]",this.zipcodeInput.value);const e=await fetch(t),n=await e.json();if(!e.ok||!(n!=null&&n.shipping_rates))throw e.status===422&&(n!=null&&n.zip[0])?new Wi(n==null?void 0:n.zip[0]):new Error("Failed to fetch shipping rates.");return n.shipping_rates}displayShippingRates(t){if(!t){this.shippingRates.textContent="";return}if(t.length===0){this.shippingRates.textContent=window._t.sections.shipping_estimator.no_rates;return}this.shippingRates.textContent="",t.forEach(e=>{const n=document.createElement("li");n.textContent=`${e.name}: ${e.price} ${e.currency}`,this.shippingRates.appendChild(n)})}}customElements.define("shipping-estimator",bd);class wd extends HTMLElement{constructor(){super()}connectedCallback(){this.carousels=this.querySelectorAll("slide-carousel"),this.targets=this.querySelectorAll("[data-index]"),this.targets.forEach(t=>{t.addEventListener("click",e=>e.detail>0?this.changeSlide(e):null),"ontouchend"in window||t.addEventListener("mouseover",e=>this.changeSlide(e)),t.addEventListener("focus",e=>this.changeSlide(e))})}changeSlide(t){t.preventDefault(),this.targets.forEach(e=>e.classList.remove("is-active")),t.currentTarget.classList.add("is-active"),this.carousels.forEach(e=>{e.changeSlide(Number(t.currentTarget.dataset.index))})}}window.customElements.define("shoppable-image-controller",wd);class Sd extends HTMLElement{constructor(){super(),this.currentIndex=0,this.slides=[],this.autoplayTimer=null,this.animationDuration=200,this.interval=5e3,this.currentAnimation=null,this.observer=null}connectedCallback(){this.slides=Array.from(this.children),this.updateActive(),this.addEventListener("control:prev",this.prev.bind(this)),this.addEventListener("control:next",this.next.bind(this)),this.addEventListener("control:change",this.change.bind(this)),this.observer=new IntersectionObserver(t=>{t.forEach(e=>{e.isIntersecting?this.getAttribute("autoplay")==="true"&&this.startAutoplay(this.interval):this.stopAutoplay()})}),this.observer.observe(this),this.getAttribute("autoplay")==="true"&&(this.interval=this.getAttribute("interval")||this.interval,this.startAutoplay(this.interval))}disconnectedCallback(){this.stopAutoplay(),this.observer&&this.observer.disconnect()}async changeSlide(t){if(t==this.currentIndex)return;Gi(...this.slides),this.updateActive();const e=this.slides[this.currentIndex],n=this.slides[t];e.style.opacity=null,n.style.opacity=null,this.currentIndex=t,this.dispatchEvent(new CustomEvent("carousel:change",{detail:{index:this.currentIndex},bubbles:!0})),n.classList.add("is-active"),e.style.opacity=0,await Promise.all([this.animateOut(e),this.animateIn(n)]),e.style.opacity=null,e.classList.remove("is-active")}updateActive(){this.slides.forEach((t,e)=>e===this.currentIndex?t.classList.add("is-active"):t.classList.remove("is-active"))}async animateIn(t){await P(t,"fade-in",{x:0,y:0,duration:150,easing:"linear"}).finished}async animateOut(t){await P(t,"fade-out",{x:0,y:0,duration:150,easing:"linear"}).finished}next(){const t=(this.currentIndex+1)%this.slides.length;this.changeSlide(t)}prev(){const t=(this.currentIndex-1+this.slides.length)%this.slides.length;this.changeSlide(t)}change(t){this.changeSlide(t.detail.index)}startAutoplay(t){this.stopAutoplay(),this.autoplayTimer=setInterval(()=>{this.next()},t)}stopAutoplay(){this.autoplayTimer&&(clearInterval(this.autoplayTimer),this.autoplayTimer=null)}}customElements.define("slide-carousel",Sd);class Ed extends HTMLElement{connectedCallback(){this.init||(this.init=!0,this.parentSlideshow=this.closest("slideshow-element"),this.slideCounterProgressCircle=this.querySelector("[data-slide-counter] svg > circle:nth-child(2)"),this.slideCounterText=this.querySelector("[data-slider-counter-text]"),this.slideCounter=this.querySelector("[data-slide-counter]"),this.buttonPrev=this.querySelector("[data-slider-button-prev]"),this.buttonNext=this.querySelector("[data-slider-button-next]"),this.buttonPrev.addEventListener("click",ei(()=>{this.dispatchEvent(new CustomEvent("slideshow:controls:prev",{bubbles:!0}))},500)),this.buttonNext.addEventListener("click",ei(()=>{this.dispatchEvent(new CustomEvent("slideshow:controls:next",{bubbles:!0}))},500)),this.parentSlideshow.addEventListener("slideshow:update",t=>{const{currentIndex:e,slidesLength:n}=t.detail;this.updateView(e,n),this.parentSlideshow.playing&&this.spinTheWheel()}),this.parentSlideshow.addEventListener("slideshow:play",()=>{this.spinTheWheel()}))}spinTheWheel(){this.spinTheWheelAnim&&this.spinTheWheelAnim.cancel(),this.spinTheWheelAnim=this.slideCounterProgressCircle.animate({strokeDashoffset:[100,0]},{duration:this.parentSlideshow.speed})}updateView(t,e){this.slideCounterText.textContent=`${t+1}/${e}`}}customElements.define("slideshow-control-arrows",Ed);class Cd extends Ne{connectedCallback(){this.init||(this.init=!0,super.connectedCallback(),this.parentSlideshow=this.closest("slideshow-element"),this.parentSlideshow.addEventListener("slideshow:update",t=>{this.updateActiveDot(t.detail.currentIndex)}))}onDotClick(t){super.onDotClick(t),this.dispatchEvent(new CustomEvent("slideshow:controls:changed",{detail:{index:t},bubbles:!0}))}}customElements.define("slideshow-control-dots",Cd);class xd extends HTMLElement{constructor(){super(),this.currentIndex=0,this.slidesLength=0,this.handleSlideshowUpdate=this.handleSlideshowUpdate.bind(this)}connectedCallback(){this.closest(".shopify-section").addEventListener("slideshow:update",this.handleSlideshowUpdate)}disconnectedCallback(){this.closest(".shopify-section").removeEventListener("slideshow:update",this.handleSlideshowUpdate)}handleSlideshowUpdate(t){const{slide:e}=t.detail,n=e.getAttribute("block-background");this.style.backgroundColor=n||null}}customElements.define("slideshow-element-background",xd);class Ad extends HTMLElement{constructor(){super(),this.playing=!1,this.currentIndex=0,this.animateOnLoad=!0,this.onDocumentVisibilityChange=this.onDocumentVisibilityChange.bind(this),this.onResizeDebounced=z(this.onResize.bind(this),100)}async connectedCallback(){this.resizeObserver||(this.resizeObserver=new ResizeObserver(this.onResizeDebounced),this.resizeObserver.observe(this)),!this.init&&(this.init=!0,this.slides=[...this.querySelectorAll("slideshow-slide")],this.speed=window.parseInt(this.getAttribute("speed")??5e3),this.autoplay=this.getAttribute("autoplay")=="true",this.addEventListener("slideshow:controls:prev",()=>{this.pause(),this.prev()}),this.addEventListener("slideshow:controls:next",()=>{this.pause(),this.next()}),this.addEventListener("slideshow:controls:changed",t=>{t.detail.index!=this.currentIndex&&(this.pause(),this.swap(this.getCurrentSlide(),this.slides[t.detail.index],t.detail.index))}),document.addEventListener("visibilitychange",this.onDocumentVisibilityChange),this.autoplay&&(this.pauseObserver=new IntersectionObserver(t=>{t[0].isIntersecting?this.play():this.pause()}),this.pauseObserver.observe(this)),this.addEventListener("touchstart",t=>this.handleTouchStart(t),{passive:!0}),this.addEventListener("touchend",t=>this.handleTouchEnd(t)),window.Shopify.designMode?(this.slides[0].classList.remove("slide-loading"),this._initDesignMode(),await this.finishInit()):this.slides[0].querySelector("img")&&!this.slides[0].querySelector("video")?this.createImageObserver():await this.finishInit())}disconnectedCallback(){document.removeEventListener("visibilitychange",this.onDocumentVisibilityChange),this.resizeObserver.disconnect(),this.resizeObserver=null,this.pauseObserver&&this.pauseObserver.disconnect()}onDocumentVisibilityChange(){if(document.visibilityState=="visible"){const t=this.getCurrentSlide().getCurrentVideo();t&&t.paused&&t.play()}}onResize(){const t=this.getCurrentSlide().getCurrentVideo();t&&t.paused&&(this.getCurrentSlide().querySelectorAll("video").forEach(e=>e.pause()),t.play())}createImageObserver(){const t=new IntersectionObserver(async e=>{const n=e.filter(s=>s.isIntersecting);if(n.length!=0){t.disconnect();try{await ns(n.map(s=>s.target))}catch(s){console.error(s)}await this.finishInit()}});this.getCurrentSlide().querySelectorAll("img").forEach(e=>t.observe(e))}async finishInit(){await Promise.all([customElements.whenDefined("slideshow-slide"),customElements.whenDefined("slideshow-control-arrows"),customElements.whenDefined("slideshow-control-dots"),customElements.whenDefined("split-lines")]),await Promise.all(Array.from(this.querySelectorAll("split-lines")).map(e=>e.ready)),this.slides.length>1&&this.showControls(),this.slides[0].classList.remove("slide-loading"),this.startIndex&&(this.slides[0].classList.remove("active"),this.slides[this.startIndex].classList.add("active"),this.currentIndex=this.startIndex);const t=this.slides[this.currentIndex].getCurrentVideo();t&&t.play(),this.animateOnLoad&&this.getCurrentSlide().getContentAnimation().run(this.getCurrentSlide()),this.autoplay?this.play():this.pause(),this.emitUpdated(),this.isInitFinished=!0}showControls(){this.querySelector("[data-slideshow-controls]").classList.add("show")}play(){!this.playing&&this.slides.length>1&&(this.playing=!0,this.classList.remove("autoplay-paused"),this.autoplayInterval=setInterval(()=>this.autoplayLoop(),this.speed),this.dispatchEvent(new CustomEvent("slideshow:play")))}pause(){this.playing=!1,this.classList.add("autoplay-paused"),clearInterval(this.autoplayInterval)}prev(){this.swap(this.getCurrentSlide(),this.getPrevSlide(),this.getPrevIndex())}next(){this.swap(this.getCurrentSlide(),this.getNextSlide(),this.getNextIndex())}async swap(t,e,n,s=!0){if(t===e||n===this.currentIndex)return;const r=Symbol();if(this.swapToken=r,document.hidden){this.slides.forEach(l=>l.classList.remove("anim-in","anim-out","active")),e.classList.add("active"),this.currentIndex=n,this.emitUpdated();return}Gi(t,e),this.slides.forEach(l=>l.classList.remove("anim-in","anim-out","active")),s?(t.classList.add("active","anim-out"),e.classList.add("active","anim-in"),e.animateIn().then(()=>{r===this.swapToken&&(o&&o.pause(),t.classList.remove("active","anim-out"),e.classList.remove("anim-in"))}).catch(()=>{})):e.classList.add("active");const a=e.getCurrentVideo(),o=t.getCurrentVideo();a&&a.play(),this.currentIndex=n,this.emitUpdated(),this.slides[this.currentIndex+1]&&this.slides[this.currentIndex+1].classList.add("preload")}emitUpdated(){this.dispatchEvent(new CustomEvent("slideshow:update",{detail:{currentIndex:this.currentIndex,slidesLength:this.slides.length,slide:this.slides[this.currentIndex]},bubbles:!0}))}autoplayLoop(){this.playing&&this.next()}getPrevSlide(){return this.slides[this.getPrevIndex()]}getCurrentSlide(){return this.slides[this.currentIndex]}getNextSlide(){return this.slides[this.getNextIndex()]}getPrevIndex(){return dt(this.currentIndex-1,this.slides.length)}getNextIndex(){return dt(this.currentIndex+1,this.slides.length)}handleTouchStart(t){this.touchStart=t.changedTouches[0]}handleTouchEnd(t){this.touchEnd=t.changedTouches[0],this.handleSwipeGesture()}handleSwipeGesture(){if(!this.touchStart||!this.touchEnd)return;const t=this.touchEnd.screenX-this.touchStart.screenX,e=this.touchEnd.screenY-this.touchStart.screenY;Math.abs(t)>Math.abs(e)&&Math.abs(t)>10&&(this.pause(),t<0?this.next():this.prev())}_initDesignMode(){this.addEventListener("shopify:block:select",async t=>{if(!this.isInitFinished){this.startIndex=this.slides.indexOf(t.target),this.animateOnLoad=!t.detail.load,this.autoplay=!1;return}this.pause(),this.currentIndex!==this.slides.indexOf(t.target)&&this.swap(this.getCurrentSlide(),t.target,this.slides.indexOf(t.target),!t.detail.load)})}}customElements.define("slideshow-element",Ad);class Je{constructor(){this.duration=333}setDuration(t){this.duration=t}getContentAnimationDelay(){return this.duration}run(t,e=null){}}class Td extends Je{constructor(){super(),this.setDuration(500)}async run(t){this.animation=t.animate({clipPath:this.getClipPathKeyframes(t)},{duration:this.duration,easing:"ease-in-out"}),await this.animation.finished}getClipPathKeyframes(t){let n=50/(t.clientWidth/t.clientHeight);return[`polygon(${n+100}% 0, 100% 0, 100% 100%, 100% 100%)`,`polygon(0 0, 100% 0, 100% 100%, -${n}% 100%)`]}}class Ld extends Je{constructor(){super(),this.setDuration(375)}async run(t){this.animation=t.animate({opacity:[0,1]},{duration:this.duration,easing:"ease-in-out"}),await this.animation.finished}}class Id extends Je{constructor(){super(),this.setDuration(500)}async run(t){this.animation=t.animate({opacity:[0,1],transform:["scale(1.075)","none"]},{duration:this.duration,easing:H}),await this.animation.finished}}class Qe{constructor(t){this.slide=t}getAnimationElements(){return[...this.slide.getAnimationElements()].map(t=>t.tagName==="SPLIT-LINES"?[...t.shadowRoot.children]:t).flat()}run(t){throw new Error("Not implemented")}}class kd extends Qe{async run(t){var e;await t.querySelector(".slide-content").animate({opacity:[0,1],transform:["translateY(1rem)","none"]},{fill:"backwards",duration:500,delay:((e=t.backgroundAnimation)==null?void 0:e.getContentAnimationDelay())??0,easing:"cubic-bezier(0, 0, .1, 1)"}).finished}}class Md extends Qe{async run(t){const e=this.getAnimationElements().length;await Promise.all(this.getAnimationElements().map((n,s)=>{var r;return n.animate({opacity:[0,1],transform:["translateY(1rem)","none"]},{duration:600,delay:(((r=t.backgroundAnimation)==null?void 0:r.getContentAnimationDelay())??0)+200/Math.log(e*2+2)*s,fill:"backwards",easing:"cubic-bezier(0, 0, .1, 1)"}).finished}))}}class Od extends Qe{async run(t){const e=this.getAnimationElements().length;await Promise.all(this.getAnimationElements().map((n,s)=>{var a;const r=n.firstElementChild??n;return n.style.overflow="hidden",r.animate({transform:["translateY(calc(100% + 9px))","none"]},{duration:800,delay:(((a=t.backgroundAnimation)==null?void 0:a.getContentAnimationDelay())??0)+200/Math.log(e*2+2)*s,fill:"backwards",easing:"cubic-bezier(0, 1, .5, 1)"}).finished.finally(()=>{n.style.overflow=null})}))}}class Pd extends HTMLElement{constructor(){super(),this.backgroundAnimations={Fade:Ld,Clip:Td,ZoomOut:Id},this.contentAnimations={FadeIn:kd,AppearLineByLine:Md,ClipLineByLine:Od}}getAnimationElements(){return this.querySelectorAll("[data-slide-animation-element]")}getBackgroundAnimation(){if(!this.backgroundAnimation){const t=this.getAttribute("background-animation"),e=this.backgroundAnimations[t];this.backgroundAnimation=new e(this)}return this.backgroundAnimation}getContentAnimation(){if(!this.contentAnimation){const t=this.getAttribute("content-animation");if(t==="none")return null;const e=this.contentAnimations[t];this.contentAnimation=new e(this)}return this.contentAnimation}async animateIn(){this.cancelAnims();const t=this.getBackgroundAnimation(),e=this.getContentAnimation();await Promise.all([t.run(this),e==null?void 0:e.run(this)])}animateOut(){this.getAnimations().forEach(t=>t.cancel()),this.classList.remove("anim-in")}cancelAnims(){var t,e;this.getAnimations().forEach(n=>n.cancel()),(e=(t=this.getContentAnimation())==null?void 0:t.getAnimationElements())==null||e.forEach(n=>{n.getAnimations().forEach(s=>s.cancel())})}getCurrentVideo(){return[...this.querySelectorAll("video")].find(t=>getComputedStyle(t).display!=="none")}}customElements.define("slideshow-slide",Pd);class Rd extends HTMLElement{constructor(){super(),this.subs=[]}connectedCallback(){this.details||(this.details=this.querySelector("details"),this.summary=this.querySelector("details > summary"),this.content=this.querySelector("details > summary + *"),this.summary.addEventListener("click",async t=>await this.onClickSummary(t)),this.getAttribute("group")&&this.setupGroupListeners(),window.Shopify.designMode&&this.initDesignMode())}disconnectedCallback(){this.subs.forEach(t=>q.unsubscribe(t))}setupGroupListeners(){this.subs.push(q.subscribe(mi,(t,e)=>{e.el!==this&&e.group===this.getAttribute("group")&&e.open&&this.collapse()}))}getAnim(){return this.getAttribute("animation")??"fade-up"}async show(){this.getAnimations({subtree:!0}).forEach(n=>n.cancel()),this.content.style.overflow="hidden",this.details.open=!0;const t=this.content.offsetHeight;this.classList.remove("collapsing"),this.classList.add("expanding");const e=this.content.animate({height:[0,t+"px"]},{duration:250,easing:H});this.getAnim()==="fade-up"&&this.content.animate({opacity:[0,1],transform:["translateY(1rem)","none"]},{duration:250,delay:50,fill:"backwards",easing:Xt}),await e.finished,this.content.style.overflow=null,this.classList.remove("expanding"),this.details.open=!0}async collapse(){this.getAnimations({subtree:!0}).forEach(n=>n.cancel()),this.content.style.overflow="hidden";const t=this.content.offsetHeight;this.classList.remove("expanding"),this.classList.add("collapsing");const e=this.content.animate({height:[t+"px",0]},{duration:250,easing:H});this.getAnim()==="fade-up"&&this.content.animate({opacity:[1,0,0]},{duration:250,easing:Xt}),await e.finished,this.content.style.overflow=null,this.classList.remove("collapsing"),this.details.open=!1}async onClickSummary(t){t.preventDefault(),this.getAttribute("group")&&q.publish(mi,{el:this,group:this.getAttribute("group"),open:!this.details.open}),!this.details.open||this.classList.contains("collapsing")?await this.show():await this.collapse()}saveComponentState(){return{open:this.details.open}}loadComponentState(t){this.details.open=t.open}initDesignMode(){this.addEventListener("shopify:block:select",()=>{this.show()}),this.addEventListener("shopify:block:deselect",()=>{this.getAttribute("default-open")===null&&this.collapse()})}}customElements.define("smooth-collapse",Rd);class Dd extends HTMLElement{constructor(){super();const t=this.attachShadow({mode:"open"}),e=document.createElement("template");e.innerHTML="<slot></slot>",t.appendChild(e.content.cloneNode(!0)),this.splitDebounced=z(this.split.bind(this),250),this.ready=new Promise(n=>this._readyResolve=n)}connectedCallback(){this.split(),this.resizeObserver=new ResizeObserver(()=>{this.splitDebounced()}),this.resizeObserver.observe(this)}disconnectedCallback(){this.resizeObserver.disconnect()}async split(){if(this.splitRunning)return;this.splitRunning=!0;const t=getComputedStyle(this).font;await document.fonts.load(t).catch(()=>{}),await Promise.all(this.shadowRoot.getAnimations({subtree:!0}).map(s=>s.finished.catch(()=>{}))),await j(100),this.shadowRoot.innerHTML=this.textContent.trim().split(`
`).map(s=>s.split("").map(r=>`<span>${r}</span>`).join("")).join("<br />");const e=new Map,n=(s,r)=>e.has(s)?e.set(s,e.get(s)+r):e.set(s,r);[...this.shadowRoot.children].forEach(s=>n(s.getBoundingClientRect().y,s.textContent)),this.shadowRoot.innerHTML=[...e.values()].map(s=>`<div style="display:block;overflow:hidden;padding-bottom:8px;margin-bottom:-8px;">
            <div style="display: inline-block">${s}</div>
          </div>`).join(""),this.splitRunning=!1,this.splitAt=this.clientWidth,this._readyResolve&&(this._readyResolve(),this._readyResolve=null)}}customElements.define("split-lines",Dd);class qd extends HTMLElement{connectedCallback(){this.observer||(this.observer=new MutationObserver(this.observerCallback.bind(this)),this.observer.observe(this,{childList:!0,subtree:!0}),window.Shopify.designMode&&this.initDesignMode())}disconnectedCallback(){this.observer.disconnect(),this.observer=null}observerCallback(t){t.forEach(e=>Array.from(e.addedNodes).filter(n=>n.classList&&n.classList.contains("spr-review")).forEach(this.modifyReview.bind(this)))}modifyReview(t){var e;this.modifiedReview=!0,(e=t.querySelector(".spr-review-footer"))==null||e.prepend(t.querySelector(".spr-review-header-byline"))}initDesignMode(){this.closest(".shopify-section").addEventListener("shopify:section:load",()=>{window.SPR.loadProducts()})}}customElements.define("spr-reviews-custom",qd);class _d extends HTMLElement{constructor(){super();kt(this,"addToCartVisible",!1);kt(this,"footerVisible",!1);kt(this,"debouncedHide",z(()=>{this.classList.contains("show")&&(this.classList.add("hide"),this.classList.remove("show"))},500));this.observer=new IntersectionObserver(this.handleIntersections.bind(this),{threshold:.2})}get section(){return this.closest(".shopify-section")}get addToCartButton(){return this.section.querySelector(".button-add-to-cart")}get footer(){return document.querySelector(".footer")}connectedCallback(){this.addToCartButton&&this.observer.observe(this.addToCartButton),this.footer&&this.observer.observe(this.footer)}disconnectedCallback(){this.observer.disconnect()}handleIntersections(e){for(let n of e)n.target===this.addToCartButton?this.addToCartVisible=n.isIntersecting:n.target===this.footer&&(this.footerVisible=n.isIntersecting);this.setVisible(!this.addToCartVisible&&!this.footerVisible&&this.addToCartButton.getBoundingClientRect().y<0)}setVisible(e){e&&this.isAboveThreshold()?(this.debouncedHide.cancel(),this.classList.add("show"),this.classList.remove("hide")):this.debouncedHide()}isAboveThreshold(){const e=window.innerHeight,n=e*.75,s=this.addToCartButton.getBoundingClientRect();return this.footer.getBoundingClientRect().top-s.bottom-e>n}}customElements.define("sticky-add-to-cart",_d);const be=1,Vi=-1;class Nd extends HTMLElement{constructor(){super()}connectedCallback(){if(!this.section){switch(this.section=this.closest(".shopify-section"),this.header=this.querySelector(".header"),this.headerBounds={},this.lastScrollTop=it(),this.lastScrollDir=0,this.lastScrollDirChange=it(),this.overlay=this.querySelector(".header-overlay"),this.alwaysVisible=!1,this.disabled=!0,this.onScrollHandler=this.onScroll.bind(this),this.getAttribute("mode")){case"scroll-up":this.disabled=!1;break;case"always-visible":this.disabled=!1,this.alwaysVisible=!0;break}this.disabled||this.createObserver(),window.Shopify.designMode&&this.initShopifyDesignMode()}}disconnectedCallback(){window.removeEventListener("scroll",this.onScrollHandler)}addScrollListener(){window.addEventListener("scroll",this.onScrollHandler,!1)}async showOverlay(){var t,e;(t=this.overlayAnim)==null||t.finish(),await((e=this.overlayAnim)==null?void 0:e.finished),this.overlay.classList.add("active"),this.overlayAnim=this.overlay.animate({opacity:[0,1]},{duration:250}),await this.overlayAnim.finished.finally(()=>{this.overlay.classList.add("active")})}async hideOverlay(){var t,e;(t=this.overlayAnim)==null||t.finish(),await((e=this.overlayAnim)==null?void 0:e.finished),this.overlayAnim=this.overlay.animate({opacity:[1,0]},{duration:250}),await this.overlayAnim.finished.finally(()=>{this.overlay.classList.remove("active")})}createObserver(){let t=new IntersectionObserver(e=>{const n=e[0].target;this.headerBounds={top:n.offsetTop,bottom:n.offsetTop+n.clientHeight},t.disconnect(),this.addScrollListener(),!this.alwaysVisible&&it()>this.headerBounds.bottom&&this.hide()});t.observe(this.section)}onScroll(){const t=it(),e=t>this.lastScrollTop?be:Vi;e!=this.lastScrollDir&&(this.lastScrollDirChange=t),this.alwaysVisible?e==be&&t>this.headerBounds.top?requestAnimationFrame(this.reveal.bind(this)):t<=this.headerBounds.top&&requestAnimationFrame(this.reset.bind(this)):e==be&&t>this.headerBounds.bottom?t-this.lastScrollDirChange>=4&&requestAnimationFrame(this.hide.bind(this)):e==Vi&&t>this.headerBounds.bottom?this.lastScrollDirChange-t>=4&&requestAnimationFrame(this.reveal.bind(this)):t<=this.headerBounds.top&&requestAnimationFrame(this.reset.bind(this)),this.lastScrollTop=t,this.lastScrollDir=e}hide(){this.section.classList.add("section-site-header--hidden","section-site-header--sticky"),this.updateCssVar()}reveal(){this.section.classList.add("section-site-header--sticky","animate"),this.section.classList.remove("section-site-header--hidden"),this.updateCssVar()}reset(){this.updateCssVar(),this.section.classList.remove("section-site-header--hidden","section-site-header--sticky","animate")}updateCssVar(){this.section.classList.contains("section-site-header--sticky")&&!this.section.classList.contains("section-site-header--hidden")?document.documentElement.style.setProperty("--header-height-sticky","var(--header-height)"):document.documentElement.style.removeProperty("--header-height-sticky")}disableTransparent(){this.header.classList.contains("header--transparent")&&(this.header.classList.remove("header--transparent"),this.transparentDisabled=!0)}enableTransparent(){this.transparentDisabled&&(this.header.classList.add("header--transparent"),this.transparentDisabled=!1)}initShopifyDesignMode(){this.addEventListener("shopify:block:select",t=>{const e=this.querySelector(`[data-block-id="${t.detail.blockId}"]`);e&&e.show&&e.show({anim:!1})}),this.addEventListener("shopify:block:deselect",t=>{const e=this.querySelector(`[data-block-id="${t.detail.blockId}"]`);e&&e.hide&&e.hide({anim:!1})})}}customElements.define("sticky-header",Nd);const we="DIR_UP",Se="DIR_DOWN",Ee="STATE_DEFAULT",ji="STATE_STICKY_TOP",zi="STATE_STICKY_BOTTOM",Ce="STATE_STATIC";class Hd extends HTMLElement{connectedCallback(){this.init||(this.init=!0,this.state=Ee,this.enabled=!1,this.onScrollThis=this.onScroll.bind(this),this.onWindowResizeDebounced=z(this.onWindowResize.bind(this),50),this.header=document.querySelector(".section-site-header"),this.sidebar=this.firstElementChild,window.addEventListener("resize",this.onWindowResizeDebounced),this.updateEnabled(),this.resizeObserver=new ResizeObserver(()=>{this.state===Ce&&this.makeSidebarStickyTop()}),this.resizeObserver.observe(this))}disconnectedCallback(){window.removeEventListener("resize",this.onWindowResizeDebounced),window.removeEventListener("scroll",this.onScrollThis),this.resizeObserver.disconnect(),this.init=!1}setEnabled(t){this.enabled!==t&&(this.enabled=t,this.enabled?(window.addEventListener("scroll",this.onScrollThis,{passive:!0}),this.lastScrollTop=it(),this.sidebar.style.transition="top 250ms",this.onScroll()):(window.removeEventListener("scroll",this.onScrollThis),this.cleanUpSidebar()))}onWindowResize(){this.updateEnabled()}updateEnabled(){const t=this.getAttribute("disable-below");t===null&&this.setEnabled(!0);const e=Number(t);this.setEnabled(window.innerWidth>=e)}onScroll(){const t=it(),e=t>this.lastScrollTop?Se:we;this.updateScrollbar(e),this.lastScrollTop=t,this.lastDirection=e}updateScrollbar(t){if(!this.enabled)return;if(this.getSidebarHeight()<this.getViewportHeight()){this.makeSidebarStickyTop();return}const e=this.sidebar.getBoundingClientRect();switch(this.state){case Ee:this.getViewportHeight()-e.bottom>=this.getStickyBottomOffset()&&this.makeSidebarStickyBottom();break;case zi:t===we&&this.getViewportHeight()-e.bottom-1<=this.getStickyBottomOffset()&&this.makeSidebarStatic();break;case Ce:t===we&&e.top>=this.getStickyTopOffset()?this.makeSidebarStickyTop():t===Se&&this.getViewportHeight()-e.bottom>=this.getStickyBottomOffset()&&this.makeSidebarStickyBottom();break;case ji:t===Se?this.makeSidebarStatic():this.lastStickyTopOffset!==this.getStickyTopOffset()&&this.makeSidebarStickyTop();break}}getSidebarHeight(){return this.sidebar.clientHeight+this.getStickyTopOffset(!1)+this.getStickyBottomOffset()}getViewportHeight(){return window.innerHeight}makeSidebarStickyTop(){this.sidebar.style.position="sticky",this.sidebar.style.top=`${this.getStickyTopOffset()}px`,this.sidebar.style.bottom=null,this.sidebar.style.alignSelf=null,this.sidebar.style.transform=null,this.lastStickyTopOffset=this.getStickyTopOffset(),this.state=ji}makeSidebarStickyBottom(){this.sidebar.style.position="sticky",this.sidebar.style.top="auto",this.sidebar.style.bottom=`${this.getStickyBottomOffset()}px`,this.sidebar.style.alignSelf="flex-end",this.sidebar.style.transform=null,this.state=zi}makeSidebarStatic(){const t=this.sidebar.getBoundingClientRect();this.sidebar.style.position=null,this.sidebar.style.top=null,this.sidebar.style.bottom=null,this.sidebar.style.alignSelf=null;const e=this.sidebar.getBoundingClientRect();this.sidebar.style.transform=`translateY(${t.y-e.y}px)`,this.state=Ce}cleanUpSidebar(){this.sidebar.style.position=null,this.sidebar.style.top=null,this.sidebar.style.bottom=null,this.sidebar.style.alignSelf=null,this.sidebar.style.transform=null,this.sidebar.style.transition=null,this.state=Ee}getStickyTopOffset(t=!0){return t&&this.header.classList.contains("section-site-header--sticky")&&!this.header.classList.contains("section-site-header--hidden")?Number(this.getAttribute("top-offset")??24)+96:Number(this.getAttribute("top-offset")??24)}getStickyBottomOffset(){return Number(this.getAttribute("bottom-offset")??24)}saveComponentState(){return{state:this.state,sidebarCss:this.sidebar.style.cssText}}loadComponentState(t){this.state=t.state,this.sidebar.style.cssText=t.sidebarCss}}customElements.define("sticky-sidebar",Hd);const Pt=new Map;class Bd{constructor(t){this.variantPicker=t}clearCache(){Pt.clear()}buildUrl({productUrl:t,variantId:e,sectionId:n=null,optionValueIds:s=null}){const r=new URL(t,window.location.origin);return e?r.searchParams.set("variant",e):(s??(s=this.variantPicker.getSelectedOptionValueIds()),r.searchParams.set("option_values",s.join(","))),n&&r.searchParams.set("section_id",n),r}async fetchUrl(t){return Pt.has(t)||Pt.set(t,new Promise(e=>fetch(t).then(n=>n.text().then(e))).catch(e=>(console.error("Failed to fetch product",e),Pt.delete(t),!1))),Pt.get(t)}async switchVariant(t){const e=this.buildUrl({productUrl:this.variantPicker.productUrl,variantId:t,sectionId:this.variantPicker.dataset.section}),n=await this.fetchUrl(e.toString()),s=this.variantPicker.quickAddModal?Ie(n):new DOMParser().parseFromString(n,"text/html");this.updateDOM(s),this.variantPicker.section.querySelectorAll("input[name=id]").forEach(r=>r.removeAttribute("disabled")),this.variantPicker.updateCurrentVariant(),this.updateURL(),this.preloadOptions()}async switchProduct(t,e){const n=this.buildUrl({productUrl:t,variantId:e}),s=await this.fetchUrl(n.toString());if(this.variantPicker.quickAddModal){const l=Ie(s).querySelector(".product-info");pe(()=>this.variantPicker.quickAddModal.setProductInfo(l));return}const r=new DOMParser().parseFromString(s,"text/html"),a=r.querySelector("main");if(!a)return;pe(()=>{document.querySelector("main").innerHTML=a.innerHTML}),window.history.replaceState({},"",n);const o=r.querySelector("title");o&&(document.title=o.textContent)}updateDOM(t){const e=this.variantPicker.section.querySelectorAll("[data-variant-info]"),n=t.querySelectorAll("[data-variant-info]"),s=t.querySelectorAll("template"),r=new Map;n.forEach(a=>{r.set(a.dataset.variantInfo,a)}),s.forEach(a=>{a.content.querySelectorAll("[data-variant-info]").forEach(l=>{r.set(l.dataset.variantInfo,l)})}),pe(()=>{e.forEach(a=>{const o=r.get(a.dataset.variantInfo);if(o){const l=a instanceof HTMLInputElement||a instanceof HTMLSelectElement,c=l?a.value:null;a.innerHTML=o.innerHTML,Array.from(a.attributes).forEach(f=>{a.removeAttribute(f.name)}),Array.from(o.attributes).forEach(f=>{a.setAttribute(f.name,f.value)}),l&&c!==a.value&&a.dispatchEvent(new Event("change",{bubbles:!0}))}})})}updateURL(){!this.variantPicker.currentVariant||this.variantPicker.dataset.updateUrl==="false"||!this.variantPicker.closest(".section-main-product")||window.history.replaceState({},"",`${this.variantPicker.dataset.url}?variant=${this.variantPicker.currentVariant.id}`)}preloadOptions(){this.getOptionsToPreload().forEach(t=>{const e=this.buildUrl({...t,productUrl:t.productUrl||this.variantPicker.productUrl,sectionId:t.productUrl?null:this.variantPicker.dataset.section});this.fetchUrl(e.toString())})}getOptionsToPreload(){const t=[],e=this.variantPicker.getSelectedOptionValueIds();return this.variantPicker.getProductOptions().forEach((n,s)=>{n.forEach(r=>{if(r.id.toString()!==e[s]){const a=[...e];a[s]=r.id.toString(),t.push({productUrl:r.productUrl??"",variantId:r.variantId??"",optionValueIds:a})}})}),t}}class Un extends HTMLElement{constructor(){super();kt(this,"currentVariant");this.productRenderer=new Bd(this),this.addEventListener("change",this.handleChange),Qn(this,async()=>{await ts(),this.productRenderer.preloadOptions()})}get addButton(){return this.section.querySelector('[name="add"]')}get productForm(){var e;return(e=this.section)==null?void 0:e.querySelector("product-form")}get productUrl(){return this.dataset.url}connectedCallback(){this.quickAddModal=this.closest("#quick-add-modal"),this.productInfoWrapper=this.closest(".product-info"),this.section=this.closest(".shopify-section")??this.closest(".quick-add-modal")}disconnectedCallback(){Shopify!=null&&Shopify.designMode&&this.productRenderer.clearCache()}async handleChange(e){var n,s;this.setLoading(!0),(n=this.productForm)==null||n.setErrorMessage();try{e.target.dataset.productUrl?this.productRenderer.switchProduct(e.target.dataset.productUrl,e.target.dataset.variantId):(await this.productRenderer.switchVariant(e.target.dataset.variantId),this.dispatchEvent(new CustomEvent("variant:change",{detail:{variant:this.currentVariant},bubbles:!0})))}catch(r){console.error("Error updating product section",r),(s=this.addButton)==null||s.removeAttribute("disabled")}finally{this.setLoading(!1)}}updateCurrentVariant(){const e=this.section.querySelector("[data-variant-info=json]");e?this.currentVariant=JSON.parse(e.textContent):this.currentVariant=null}setLoading(e){var n;e?(this.classList.add("disabled"),(n=this.addButton)==null||n.setAttribute("disabled","")):this.classList.remove("disabled")}getProductOptions(){const e=this.querySelectorAll("[data-option-group]"),n=[];return e.forEach(s=>{const r=[],a=Array.from(s.querySelectorAll("[data-option-value-id]"));fd(a,o=>o.dataset.optionValueId).forEach(o=>{r.push({id:o.dataset.optionValueId,variantId:o.dataset.variantId,productUrl:o.dataset.productUrl,selected:o.dataset.optionValueId===this.getOptionGroupSelected(s).dataset.optionValueId})}),n.push(r)}),n}getOptionGroupSelected(e){return e.tagName==="FIELDSET"?e.querySelector("input:checked"):e.querySelector("input")}getSelectedOptionValueIds(){return this.getProductOptions().map(e=>{var n;return(n=e.find(s=>s.selected))==null?void 0:n.id})}}customElements.define("variant-picker",Un);class Fd extends Un{constructor(){super()}updateOptions(){const t=Array.from(this.querySelectorAll("fieldset"));this.options=t.map(e=>Array.from(e.querySelectorAll("input")).find(n=>n.checked).value)}getOptionGroups(){return this.querySelectorAll("fieldset")}getOptionGroupValue(t){return Array.from(t.querySelectorAll("input")).find(e=>e.checked).value}getOptionGroupElements(t){return t.querySelectorAll("input")}getOptionGroupElementValue(t){return t.value}}customElements.define("variant-radios",Fd);class $d extends HTMLElement{get host(){return this.getAttribute("host")}get videoId(){return this.getAttribute("video-id")}get controls(){return this.background?!1:this.getAttribute("controls")!==null}get muted(){return this.background?!0:this.getAttribute("muted")!==null}get loop(){return this.background?!0:this.getAttribute("loop")!==null}get background(){return this.getAttribute("background")!==null}get autopause(){return this.getAttribute("autopause")!=="false"}get autoplay(){return this.getAttribute("autoplay")!=="false"}connectedCallback(){this.poster=this.querySelector("[data-poster]"),!this.host||this.host==="native"?this.controller=new jd(this):this.host==="youtube"?this.controller=new Wd(this):this.host==="vimeo"&&(this.controller=new Vd(this)),this.poster?this.poster.addEventListener("click",this.onPosterClick.bind(this)):this.autoplay&&this.createInitObserver(),this.autopause&&this.createAutoPauseObserver()}disconnectedCallback(){var t,e;(t=this.observer)==null||t.disconnect(),(e=this.autoPauseObserver)==null||e.disconnect()}createInitObserver(){this.observer=new IntersectionObserver(t=>{t[0].isIntersecting&&(this.controller.createPlayer(),this.observer.disconnect())},{rootMargin:"50% 0px 50% 0px",threshold:0}),this.observer.observe(this)}createAutoPauseObserver(){this.autoPauseObserver=new IntersectionObserver(t=>{this.controller.init&&(t[0].isIntersecting&&this.background?this.controller.play():t[0].isIntersecting||this.controller.pause())},{threshold:0}),this.autoPauseObserver.observe(this)}async onPosterClick(){await this.play()}hidePoster(){this.poster&&(this.poster.style.display="none")}async play(){this.controller.init?this.controller.play():(this.classList.add("video-player--loading"),await this.controller.createPlayer(),this.hidePoster(),this.classList.remove("video-player--loading"))}async pause(){await this.controller.pause()}}class Ze{constructor(t){this.player=t,this.init=!1}createPlayer(){}play(){}pause(){}}class Wd extends Ze{async loadAPI(){var t;if(!((t=window.YT)!=null&&t.Player)){const e=document.createElement("script");e.src="//www.youtube.com/iframe_api",document.head.appendChild(e),await new Promise(n=>{window.onYouTubeIframeAPIReady=()=>n()})}}async createPlayer(){this.init=!0,await this.loadAPI();const t=document.createElement("div");t.style.display="none",this.player.background&&Object.assign(t.style,{pointerEvents:"none",userSelect:"none",width:"300%",height:"calc(100% + 2px)",marginTop:"-1px",marginLeft:"-100%"}),this.player.appendChild(t),await new Promise(e=>{this.playerInstance=new window.YT.Player(t,{width:"100%",height:"100%",videoId:this.player.videoId,playerVars:{playlist:this.player.videoId,controls:this.player.controls?1:0,autoplay:!0,mute:this.player.muted?1:0,rel:0,modestbranding:1,playsinline:1,loop:this.player.loop?1:0,showinfo:0},events:{onReady:e}})}),this.playerInstance.getIframe().style.display=null}play(){var t;(t=this.playerInstance)==null||t.playVideo()}pause(){var t;(t=this.playerInstance)==null||t.pauseVideo()}}class Vd extends Ze{async loadAPI(){var t;if(!((t=window.Vimeo)!=null&&t.Player)){const e=document.createElement("script");e.src="//player.vimeo.com/api/player.js",document.head.appendChild(e),await new Promise(n=>{e.onload=()=>n()})}}async createPlayer(){this.init=!0,await this.loadAPI();const t=document.createElement("div");Object.assign(t.style,{display:"none",position:"absolute",top:"0",right:"0",bottom:"0",left:"0"}),this.player.appendChild(t),this.playerInstance=new window.Vimeo.Player(t,{id:this.player.videoId,width:640,height:360,controls:this.player.controls,autopause:!1,autoplay:!0,muted:this.player.muted,playsinline:!0,loop:this.player.loop,background:this.player.background}),await this.playerInstance.ready();const e=t.querySelector("iframe");e&&Object.assign(e.style,{width:"100%",height:"100%"}),t.style.display=null}async play(){var t;await((t=this.playerInstance)==null?void 0:t.play())}async pause(){var t;await((t=this.playerInstance)==null?void 0:t.pause())}}class jd extends Ze{createPlayer(){this.init=!0;const t=this.player.querySelector("template");if(t){const e=document.importNode(t.content,!0).firstElementChild;this.video=e.tagName==="VIDEO"?e:e.querySelector("video"),this.video.autoplay=!0,this.video.playsinline=!0,this.video.controls=this.player.controls,this.video.muted=this.player.muted,this.video.loop=this.player.loop,this.player.background&&Object.assign(this.video.style,{pointerEvents:"none",userSelect:"none"}),this.player.appendChild(e)}}async play(){var t;await((t=this.video)==null?void 0:t.play())}async pause(){var t;await((t=this.video)==null?void 0:t.pause())}}customElements.define("video-player",$d);function Gn(){document.querySelectorAll(".prevent-transition-on-load").forEach(i=>i.classList.remove("prevent-transition-on-load"))}document.addEventListener("DOMContentLoaded",()=>{Re(),jr(),Gn()});window.addEventListener("pageshow",()=>{document.querySelectorAll("button[data-loading]").forEach(i=>{D(i,!1)})});window.addEventListener("resize",z(Re,100));window.pauseAllMedia=Ui;window.Shopify.designMode&&document.addEventListener("shopify:section:unload",()=>{window.activeModals=[],window.activeDropdowns=[],window.activeMegaMenuHorizontal=null,_e(),Gn()});document.body.addEventListener("click",function(i){var e;const t=i.target.closest("a:not([data-smooth-scroll-ignore])");if(t&&((e=t.getAttribute("href"))!=null&&e.startsWith("#"))&&t.getAttribute("href").length>1){i.preventDefault();const n=document.querySelector(t.getAttribute("href"));if(n){const s=n.getBoundingClientRect().top,r=window.innerHeight*.25,a=window.scrollY+s-r;window.scrollTo({top:a,behavior:"smooth"})}(n.getAttribute("tabindex")!==null||["A","BUTTON","INPUT","SELECT","TEXTAREA"].includes(n.tagName))&&n.focus()}});
