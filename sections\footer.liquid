{% render 'section-bg-number-vars', bg: settings.colors_footer_background %}

<div {% render 'section-attrs', class: 'footer overflow-hidden' %}>
  <div class="footer__blocks">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'links' %}
          <div
            data-animation="block"
            data-animation-group="{{ section.id }}"
            class="footer-block--links styled-links"
            {{ block.shopify_attributes }}
          >
            {% if block.settings.show_heading %}
              <p class="font-bold mb-4">{{ block.settings.heading | default: block.settings.menu.title }}</p>
            {% endif %}
            <ul class="flex flex-col gap-3">
              {% for link in block.settings.menu.links %}
                <li>
                  <a data-instant href="{{ link.url }}">
                    {{ link.title }}
                  </a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {% when 'text' %}
          <div
            data-animation="block"
            data-animation-group="{{ section.id }}"
            class="footer-block--text"
            {{ block.shopify_attributes }}
          >
            {% if block.settings.image != blank %}
              {%- capture sizes -%} min({{ block.settings.image_width }}px, 100vw) {%- endcapture -%}
              {%- capture style -%} max-width: {{ block.settings.image_width }}px; {%- endcapture -%}
              {{
                block.settings.image
                | image_url: width: 1920
                | image_tag:
                  class: 'w-full mb-6 md:mb-8',
                  style: style,
                  loading: 'lazy',
                  widths: '120, 270, 420, 570, 720, 870, 1020, 1280',
                  sizes: sizes
              }}
            {% endif %}
            <p class="font-bold mb-4">{{ block.settings.heading }}</p>
            <div class="prose max-md:text-sm">
              {{ block.settings.content }}
            </div>
          </div>
        {% when 'newsletter' %}
          <div
            data-animation="block"
            data-animation-group="{{ section.id }}"
            class="footer-block--newsletter"
            {{ block.shopify_attributes }}
          >
            <p class="font-bold mb-4">{{ block.settings.heading }}</p>
            <div class="prose max-md:text-sm">
              {{ block.settings.content }}
            </div>
            <div class="flex mt-6">
              {% render 'newsletter-signup-form',
                button_style: block.settings.button_style,
                button_text: block.settings.button_text,
                button_background_color: block.settings.button_background_color,
                button_text_color: block.settings.button_text_color,
                button_class: 'text-sm',
                wrapper_class: 'flex flex-col sm:items-start gap-4'
              %}
            </div>
          </div>
      {% endcase %}
    {% endfor %}
  </div>

  <div class="flex xl:items-center max-xl:flex-col gap-y-8 md:gap-y-12">
    <div class="footer__country-lang flex gap-8 max-xl:justify-center empty:hidden">
      {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
        {%- render 'country-selector-dropdown',
          placement: 'top-start',
          flag: section.settings.country_selector_show_country_flag,
          name: section.settings.country_selector_show_country_name,
          currency: section.settings.country_selector_show_currency
        -%}
      {%- endif -%}

      {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
        {%- render 'language-selector-dropdown', placement: 'top-start' -%}
      {%- endif -%}
    </div>

    <div class="spacer max-xl:hidden"></div>

    {% if section.settings.show_social %}
      {% render 'footer-social-icons' %}
    {% endif %}

    {%- if shop.features.follow_on_shop? and section.settings.enable_follow_on_shop -%}
      <div class="text-center xl:text-right xl:ml-6">
        {{ shop | login_button: action: 'follow' }}
      </div>
    {%- endif -%}
  </div>

  <hr class="border-separator -mx-co mt-8 md:mt-12">

  <div class="footer__bottom items-center">
    {%- if section.settings.show_payment %}
      <ul class="flex flex-wrap items-center justify-center gap-2" role="list">
        {% for type in shop.enabled_payment_types %}
          <li>
            {{ type | payment_type_svg_tag: class: 'payment-icon' }}
          </li>
        {% endfor %}
      </ul>
    {% endif %}

    <p class="text-foreground text-sm text-opacity-50 ml-0 xl:ml-auto">
      <span>
        &copy; {{ 'now' | date: '%Y' }}
        {{ shop.name | link_to: routes.root_url }},
      </span>
      <span>{{ powered_by_link }}</span>
    </p>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.footer.name",
  "class": "section-footer",
  "tag": "footer",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.footer.settings.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_social",
      "label": "t:sections.footer.settings.show_social.label",
      "info": "t:sections.footer.settings.show_social.info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_payment",
      "label": "t:sections.footer.settings.show_payment.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.footer.headers.country_region_selector",
      "info": "To add a country/region, go to your [market settings.](/admin/settings/markets)"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": true,
      "label": "t:sections.footer.settings.enable_country_selector.label"
    },
    {
      "type": "checkbox",
      "id": "country_selector_show_country_flag",
      "label": "t:sections.footer.settings.country_selector_show_country_flag.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "country_selector_show_country_name",
      "label": "t:sections.footer.settings.country_selector_show_country_name.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "country_selector_show_currency",
      "label": "t:sections.footer.settings.country_selector_show_currency.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.footer.headers.language_selector",
      "info": "To add a language, go to your [language settings.](/admin/settings/languages)"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": true,
      "label": "t:sections.footer.settings.enable_language_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.headers.follow_on_shop",
      "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
    },
    {
      "type": "checkbox",
      "id": "enable_follow_on_shop",
      "default": true,
      "label": "t:sections.footer.settings.enable_follow_on_shop.label"
    }
  ],
  "max_blocks": 5,
  "blocks": [
    {
      "type": "links",
      "name": "t:sections.footer.blocks.links.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "show_heading",
          "label": "t:sections.footer.blocks.links.settings.show_heading.label",
          "default": true
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.footer.blocks.links.settings.heading.label",
          "info": "t:sections.footer.blocks.links.settings.heading.info"
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "t:sections.footer.blocks.links.settings.menu.label",
          "info": "t:sections.footer.blocks.links.settings.menu.info",
          "default": "footer"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.footer.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.footer.blocks.text.settings.heading.label",
          "default": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.footer.blocks.text.settings.content.label",
          "default": "<p>Share contact information, store details, and brand content with your customers.</p>"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.footer.blocks.text.settings.image.label"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 60,
          "max": 640,
          "step": 10,
          "unit": "px",
          "label": "t:sections.footer.blocks.text.settings.image_width.label",
          "default": 240
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "t:sections.footer.blocks.newsletter.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.footer.blocks.newsletter.settings.heading.label",
          "default": "Stay in touch"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.footer.blocks.newsletter.settings.content.label",
          "default": "<p>Provide additional information about your newsletter and explain the benefits of subscribing to it.</p>"
        },
        {
          "type": "text",
          "id": "button_text",
          "default": "Subscribe",
          "label": "t:sections.footer.blocks.newsletter.settings.button_text.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:sections.all.button_style.label",
          "options": [
            {
              "value": "",
              "label": "t:sections.all.button_style.options.filled"
            },
            {
              "value": "button-outline",
              "label": "t:sections.all.button_style.options.outline"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "t:sections.all.headers.colors"
        },
        {
          "type": "color",
          "label": "t:sections.all.button_background_color.label",
          "id": "button_background_color"
        },
        {
          "type": "color",
          "label": "t:sections.all.button_text_color.label",
          "id": "button_text_color"
        }
      ]
    }
  ]
}
{% endschema %}
