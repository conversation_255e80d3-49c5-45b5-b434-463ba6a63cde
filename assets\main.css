/* ! tailwindcss v3.4.3 | MIT License | https://tailwindcss.com */

*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e5e5;
}

::before,
::after {
  --tw-content: "";
}

html,
:host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted currentColor;
  text-decoration: underline dotted currentColor;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button,
select {
  text-transform: none;
}

button,
input:where([type="button"]),
input:where([type="reset"]),
input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #a3a3a3;
}

button,
[role="button"] {
  cursor: pointer;
}
:disabled {
  cursor: default;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}
[hidden] {
  display: none;
}

.image-hover-zoom,
.image-hotspot,
.rotate-180,
.-scale-100,
.-scale-x-100,
.transform {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
}

.dropdown-menu,
.modal::part(panel),
.privacy-banner-modal::part(panel),
.newsletter-modal::part(panel),
.carousel-prev,
.carousel-next,
.image-comparison,
.image-comparison__handle,
.button-prev,
.button-next,
.button-zoom-in,
.shadow-block,
.shadow-floating-modal,
.sm\:shadow-block {
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
}

.button-outline:not(:hover):not(:active),
:where(.button-primary, .shopify-challenge__button, .shopify-payment-button__button--unbranded):not(
    :hover
  ):not(:active),
.button-secondary:not(:hover):not(:active),
:where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email),
[type="checkbox"],
[type="radio"],
input,
label,
.media::after,
.inner,
.ring-1 {
  --tw-ring-inset:;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: rgb(var(--color-background));
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
}
:root {
  --breakpoint-hd: 1400px;
  --breakpoint-xl: 1200px;
  --scrollbar-width: 0px;

  --gray-50: 250 250 250;
  --gray-100: 245 245 245;
  --gray-200: 229 229 229;
  --gray-300: 212 212 212;
  --gray-400: 163 163 163;
  --gray-500: 115 115 115;
  --gray-600: 82 82 82;
  --gray-700: 64 64 64;
  --gray-800: 38 38 38;
  --gray-900: 23 23 23;

  --white: 255 255 255;
  --black: 0 0 0;

  --color-separator: 229 231 235;
  --color-outline: 209 213 219;

  --color-border: 212 212 212;

  --color-background: var(--color-base-background);
  --color-foreground: var(--color-base-foreground);
  --color-headings: var(--color-base-headings);

  --color-modal-active: rgb(var(--color-base-foreground) / 3%);

  --color-icon: var(--color-foreground);

  --color-rating-star-empty: var(--color-foreground);
  --color-rating-star-empty-opacity: 10%;

  --input-color-foreground: var(--color-foreground);
  --input-background: rgb(var(--color-foreground) / 6%);

  --size-h0-rfs: min(4rem, calc(1.75rem + 3vw));
  --size-h1-rfs: min(3rem, calc(1.5rem + 2vw));
  --size-h2-rfs: min(2.25rem, calc(1.3125rem + 1.25vw));
  --size-h3-rfs: min(1.875rem, calc(1.21875rem + 0.875vw));
  --size-h4-rfs: min(1.5rem, calc(1.125rem + 0.5vw));
  --size-h5-rfs: min(1.25rem, calc(1.0625rem + 0.25vw));
  --size-h6-rfs: min(1.125rem, calc(1.03125rem + 0.125vw));

  --line-height-h0: calc(72 / 64);
  --line-height-h1: calc(56 / 48);
  --line-height-h2: calc(44 / 36);
  --line-height-h3: calc(36 / 30);
  --line-height-h4: calc(32 / 24);
  --line-height-h5: calc(28 / 20);
  --line-height-h6: calc(24 / 18);

  --size-text-lg: calc(var(--font-body-scale, 1) * 1.125rem);
  --size-text-base: calc(var(--font-body-scale, 1) * 1rem);
  --size-text-sm: calc(var(--font-body-scale, 1) * 0.875rem);
  --size-text-xs: calc(var(--font-body-scale, 1) * 0.75rem);
  --size-text-2xs: calc(var(--font-body-scale, 1) * 0.625rem);

  --weight-normal: var(--font-body-weight);
  --weight-bold: var(--font-body-weight-bold);

  --info-badge-text-transform: uppercase;
  --media-badge-offset: 1rem;
}
@media not all and (min-width: 768px) {
  :root {
    --media-badge-offset: 0.5rem;
  }
}
:root {
  --container-padding-const: 4rem;
}
@media not all and (min-width: 1200px) {
  :root {
    --container-padding-const: 2rem;
  }
}
@media not all and (min-width: 768px) {
  :root {
    --container-padding-const: 1.25rem;
  }
}
:root {
  --container-padding: var(--container-padding-const);

  --container-max-inner-width: var(--container-max-inner-width-const);
  --container-max-width: calc(var(--container-max-inner-width) + var(--container-padding) * 2);

  --container-inner-width: calc(
    min(
      100vw - var(--container-padding) * 2 - var(--scrollbar-width),
      var(--container-max-inner-width)
    )
  );

  --container-outer-width: calc(
    50vw - var(--container-inner-width) / 2 - var(--scrollbar-width) / 2
  );

  --announcement-bar-font-size: var(--size-text-sm);
  --announcement-bar-height: 0px;

  --header-min-height: 6rem;
  --header-height-actual: var(--header-height);
  --header-height-sticky: 0px;

  --header-group-height: calc(
    var(--announcement-bar-height, 0px) + var(--header-height-actual, 0px) +
      var(--promotion-bar-height, 0px)
  );

  --allow-transparent-header-padding: 0px;

  --overlay-bg: rgb(0 0 0 / 43%);

  --section-vertical-spacing: var(--section-vertical-spacing-const);
}
@media not all and (min-width: 992px) {
  :root {
    --header-min-height: 5rem;
  }
}
@media not all and (min-width: 768px) {
  :root {
    --header-min-height: 4rem;
  }
}
@media not all and (min-width: 576px) {
  :root {
    --size-h5: 1.125rem;
    --size-h6: 1rem;
  }
}
:root {
  --range-track-color: rgb(var(--gray-100));
  --range-track-height: 0.375rem;

  --range-thumb-color: rgb(var(--color-range-slider));
  --range-thumb-size: 1rem;
  --button-background: var(--button-primary-background);
  --button-background-hover: var(--button-primary-background-hover);
  --button-background-active: var(--button-primary-background-active);
  --button-foreground: var(--button-primary-foreground);

  --viewport-height: 100vh;
  --header-height: 6rem;
  --announcement-bar-height: 0px;
  --promotion-bar-height: 0px;
}
@media not all and (min-width: 768px) {
  :root {
    --header-height: 4rem;
  }
}
@supports (height: 100svh) {
  :root {
    --viewport-height: 100svh;
  }
}
.section-spacing-small {
  --section-vertical-spacing-const: clamp(3rem, 2.4615rem + 2.0513vw, 4rem);
}
.section-spacing-normal {
  --section-vertical-spacing-const: clamp(3rem, 1.9231rem + 4.1026vw, 5rem);
}
.section-spacing-large {
  --section-vertical-spacing-const: clamp(3.5rem, 1.8846rem + 6.1538vw, 6.5rem);
}
.section-spacing-extra-large {
  --section-vertical-spacing-const: clamp(4rem, 1.8462rem + 8.2051vw, 8rem);
}
.block-spacing-small {
  --block-spacing-const: 0.75rem;
  --block-spacing: var(--block-spacing-const);
}
.block-spacing-normal {
  --block-spacing-const: clamp(16px, calc(1rem + ((1vw - 3.6px) * 0.6452)), 24px);
  --block-spacing: var(--block-spacing-const);
}
.block-spacing-large {
  --block-spacing-const: clamp(16px, calc(1rem + ((1vw - 3.6px) * 1.6129)), 36px);
  --block-spacing: var(--block-spacing-const);
}
.block-spacing-extra-large {
  --block-spacing-const: clamp(16px, calc(1rem + ((1vw - 3.6px) * 2.5806)), 48px);
  --block-spacing: var(--block-spacing-const);
}
* {
  box-sizing: border-box;
}
*::before,
*::after {
  box-sizing: inherit;
}
*,
*::before,
*::after {
  border-color: rgb(var(--color-foreground) / 6%);
}
*:focus-visible {
  outline-offset: 2px;
}
[hidden] {
  display: none !important;
}
img {
  color: transparent;
}
svg {
  max-width: 100%;
  height: auto;
}
.spacer {
  flex-grow: 1;
}
html {
  height: 100%;
  font-size: 16px;
}
body {
  margin: 0;
  overflow-y: scroll;
  min-height: 100vh;
  min-height: -webkit-fill-available;
  min-height: -moz-available;
  min-height: stretch;
  min-height: 100svh;
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  display: flex;
  flex-direction: column;
}
body.no-scroll {
  overflow: hidden !important;
  padding-right: var(--scrollbar-width);
}
body.no-scroll .no-scroll-expand {
  margin-right: calc(var(--scrollbar-width) * -1);
  padding-right: var(--scrollbar-width);
}
#MainContent {
  flex-grow: 1;
  outline: none;
}
html.js.scroll-animations-enabled scroll-animate [data-animation] {
  opacity: 0;
}
hr {
  border-top-width: 1.25px;
  border-color: rgb(var(--color-foreground) / 6%);
}
html.no-js .no-js-hidden {
  display: none !important;
}
.errors ul {
  padding-left: 1.5rem;
  list-style: disc;
  list-style-position: inside;
}
.visually-hidden {
  position: absolute;
  clip-path: inset(50%);
  width: 1px;
  height: 1px;
  overflow: hidden;
  white-space: nowrap;
}
.skip-to-content-link:focus {
  z-index: 9999;
  position: static;
  clip-path: none;
  width: auto;
  height: auto;
  overflow: visible;
}
.container {
  margin: 0 auto;
  width: 100%;
  max-width: var(--container-max-width);
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}
.list-unstyled {
  list-style: none;
  margin: 0;
  padding: 0;
}
details > summary {
  list-style: none;
}
details > summary::-webkit-details-marker {
  display: none;
}
a {
  text-underline-offset: 0.25rem;
}
.styled-links a:hover,
.styled-link:hover {
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 0.25rem;
}
.underlined-link {
  text-decoration: underline;
  text-underline-offset: 0.25rem;
}
address {
  font-style: normal;
}
button {
  padding: 0;
  letter-spacing: inherit;
}
.button,
.shopify-payment-button__button--unbranded:not([disabled]),
.shopify-payment-button__button--unbranded[disabled],
.shopify-challenge__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.25em 2em;
  border: 0;
  border-radius: var(--button-corner-radius);
  font: inherit;
  text-decoration: none;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  letter-spacing: 0.05em;
  text-align: center;

  background: rgb(var(--button-background) / var(--button-background-opacity, 1));
  color: rgb(var(--button-foreground));
  transition:
    background-color 200ms,
    color 200ms,
    box-shadow 200ms;

  text-transform: var(--button-text-transform);
  font-family: var(--button-font-family);
  font-weight: var(--button-font-weight);
  line-height: 1;

  position: relative;

  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .button,
  .shopify-payment-button__button--unbranded:not([disabled]),
  .shopify-payment-button__button--unbranded[disabled],
  .shopify-challenge__button {
    font-size: var(--size-text-base);
  }
}
.button:hover,
.shopify-payment-button__button--unbranded:not([disabled]):hover,
.shopify-payment-button__button--unbranded[disabled]:hover,
.shopify-challenge__button:hover {
  background: rgb(var(--button-background-hover) / var(--button-background-hover-opacity, 1));
}
.button:active,
.shopify-payment-button__button--unbranded:not([disabled]):active,
.shopify-payment-button__button--unbranded[disabled]:active,
.shopify-challenge__button:active {
  background: rgb(var(--button-background-active) / var(--button-background-active-opacity, 1));
}
.button:disabled,
.button.disabled,
.shopify-payment-button__button--unbranded:not([disabled]):disabled,
.shopify-payment-button__button--unbranded:not([disabled]).disabled,
.shopify-payment-button__button--unbranded[disabled]:disabled,
.shopify-payment-button__button--unbranded[disabled].disabled,
.shopify-challenge__button:disabled,
.shopify-challenge__button.disabled {
  opacity: 0.5;
  pointer-events: none;
  filter: grayscale(1);
}
.button > .spinner,
.shopify-payment-button__button--unbranded:not([disabled]) > .spinner,
.shopify-payment-button__button--unbranded[disabled] > .spinner,
.shopify-challenge__button > .spinner {
  position: absolute;
  width: 1.5em;
  max-width: 50%;
}
.button > .spinner .path,
.shopify-payment-button__button--unbranded:not([disabled]) > .spinner .path,
.shopify-payment-button__button--unbranded[disabled] > .spinner .path,
.shopify-challenge__button > .spinner .path {
  stroke: rgb(var(--button-foreground));
}
.shopify-payment-button__button--unbranded:hover {
  background: rgb(
    var(--button-background-hover) / var(--button-background-hover-opacity, 1)
  ) !important;
}
.shopify-payment-button__button--unbranded:active {
  background: rgb(
    var(--button-background-active) / var(--button-background-active-opacity, 1)
  ) !important;
}
.button-primary,
.shopify-challenge__button,
.shopify-payment-button__button--unbranded {
  --button-background: var(--button-primary-background);
  --button-background-hover: var(--button-primary-background-hover);
  --button-background-active: var(--button-primary-background-active);

  --button-foreground: var(--button-primary-foreground);
}
.button-secondary {
  --button-background: var(--button-secondary-background);
  --button-background-hover: var(--button-secondary-background-hover);
  --button-background-active: var(--button-secondary-background-active);

  --button-foreground: var(--button-secondary-foreground);
}
.button-light {
  --button-background: var(--color-foreground);
  --button-background-opacity: 5%;

  --button-background-hover: var(--color-foreground);
  --button-background-hover-opacity: 8%;

  --button-background-active: var(--color-foreground);
  --button-background-active-opacity: 12%;

  --button-foreground: var(--color-foreground);
}
.shopify-payment-button__button--hidden {
  display: none !important;
}
.shopify-payment-button__button {
  height: auto;
}
.shopify-payment-button__button--unbranded {
  white-space: nowrap;
}
.button-outline:not(:hover):not(:active),
.button-primary-outline
  :where(
    .button-primary,
    .shopify-challenge__button,
    .shopify-payment-button__button--unbranded
  ):not(:hover):not(:active),
.button-secondary-outline .button-secondary:not(:hover):not(:active) {
  background: transparent !important;
  color: rgb(var(--button-background)) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: rgb(var(--button-background));
}
.button-outline > .spinner .path,
.button-primary-outline
  :where(.button-primary, .shopify-challenge__button, .shopify-payment-button__button--unbranded)
  > .spinner
  .path,
.button-secondary-outline .button-secondary > .spinner .path {
  stroke: rgb(var(--button-background));
}
button[aria-disabled="true"],
.button[aria-disabled="true"] {
  opacity: 0.6;
  cursor: not-allowed;
}
th {
  text-align: left;
}
.message {
  line-height: 1.75;
}
@media not all and (min-width: 768px) {
  .message {
    font-size: var(--size-text-sm);
  }
}
.message {
  outline: 0 !important;
}
.message:empty {
  display: none;
}
.message::before {
  content: "";
  display: block;
  border-radius: 100rem;
  width: 1em;
  height: 1em;
  flex-shrink: 0;
  margin-top: 0.3125em;
}
.message {
  display: flex;
  gap: 0.75em;
}
.message-danger::before {
  background: currentColor;
  background-image: var(--svg-message-danger);
}
.message-success::before {
  background: currentColor;
  background-image: var(--svg-message-success);
}
.message-unavailable::before {
  background: currentColor;
  background-image: var(--svg-message-unavailable);
}
.media {
  display: block;
  position: relative;
  overflow: hidden;
  aspect-ratio: var(--aspect-ratio, auto);
}
.media :where(img, video, svg) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.media iframe {
  width: 100%;
  height: 100%;
}
.media--overlay::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 5;
  background: var(--media-overlay);
  opacity: var(--media-overlay-opacity, 1);
}
.media--contain :where(img, video, svg) {
  object-fit: contain;
}
.media--ratio-4-3 {
  --aspect-ratio: 4 / 3;
}
.media--ratio-3-2 {
  --aspect-ratio: 3 / 2;
}
.media--ratio-16-9 {
  --aspect-ratio: 16 / 9;
}
.media--ratio-21-9 {
  --aspect-ratio: 21 / 9;
}
.media--ratio-3-4 {
  --aspect-ratio: 3 / 4;
}
.media--ratio-2-3 {
  --aspect-ratio: 2 / 3;
}
.media--ratio-1-1 {
  --aspect-ratio: 1 / 1;
}
.model-icon::after {
  position: absolute;
  top: 20px;
  left: 20px;
  filter: drop-shadow(0px 0px 2px black);
  pointer-events: none;
  content: attr(data-label);
  padding: 5px;
  background-color: rgb(var(--color-background));
}
.align-left {
  text-align: left;
}
.align-center {
  text-align: center;
}
.align-right {
  text-align: right;
}
.pagination {
  --pagination-scale: 1;
}
@media not all and (min-width: 576px) {
  .pagination {
    --pagination-scale: 0.875;
  }
}
.pagination {
  display: flex;
  justify-content: center;
}
.pagination li {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: calc(var(--size-text-sm) * var(--pagination-scale));
  width: calc(2.5rem * var(--pagination-scale));
  height: calc(3rem * var(--pagination-scale));
  font-weight: var(--weight-bold);
  border-radius: var(--block-sm-corner-radius);
  background-color: rgb(var(--color-foreground) / 0.05);
}
.pagination li.active {
  background: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}
.pagination li a {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagination ol {
  display: flex;
  gap: calc(0.75rem * var(--pagination-scale));
  flex-wrap: wrap;
  justify-content: center;
}
.pagination .pagination-prev svg,
.pagination .pagination-next svg {
  width: calc(1rem * var(--pagination-scale));
  height: calc(1rem * var(--pagination-scale));
}
.pagination .pagination-prev svg {
  transform: scaleX(-1);
}
.loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgb(var(--color-background) / 50%);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-overlay .spinner {
  width: min(15vw, 5rem);
  height: auto;
}
@keyframes dash {
  0% {
    stroke-dashoffset: 280;
  }

  50% {
    stroke-dashoffset: 75;
    transform: rotate(135deg);
  }

  100% {
    stroke-dashoffset: 280;
    transform: rotate(450deg);
  }
}
.disable-pointer-events {
  pointer-events: none;
}
.cart-badge {
  --badge-size: 1.125rem;
  background: rgb(var(--color-cart-badge));
  color: rgb(var(--color-cart-badge-foreground));
  font-size: var(--size-text-xs);
  font-weight: var(--weight-bold);
  line-height: var(--badge-size);
  position: absolute;
  left: calc(100% - var(--badge-size) / 2);
  top: calc(var(--badge-size) / -2);
  border-radius: 100rem;
  text-align: center;
  height: var(--badge-size);
  min-width: var(--badge-size);
  padding: 0 4px;
}
@media not all and (min-width: 768px) {
  .cart-badge {
    --badge-size: 1rem;
  }
}
.grid-columns {
  --grid-columns: 4;
  --grid-columns-actual: min(var(--grid-columns), var(--grid-columns-max, 99999));
  display: grid;
  grid-template-columns: repeat(var(--grid-columns-actual), minmax(0, 1fr));
}
.grid-carousel {
  --grid-gap: max(var(--block-spacing), var(--grid-gap-min, 0px));
  --grid-columns-actual: min(var(--grid-columns, 4), var(--grid-columns-max, 99999));

  --grid-item-width: calc(
    100% / var(--grid-columns-actual) - var(--grid-gap) / var(--grid-columns-actual) *
      (var(--grid-columns-actual) - 1)
  );

  display: grid;
  gap: var(--grid-gap);
  grid: auto / auto-flow var(--grid-item-width);
}
:not(.grid-carousel--stack).grid-carousel--pseudo-pr > :last-child {
  position: relative;
}
:not(.grid-carousel--stack).grid-carousel--pseudo-pr > :last-child::after {
  border: 1px solid transparent;
  left: 100%;
  top: -100%;
  position: relative;
  content: "";
  display: block;
  width: var(--scroll-x-pseudo-pr, var(--scroll-bleed));
}
@supports (inset-inline-start: 100%) {
  :not(.grid-carousel--stack).grid-carousel--pseudo-pr > :last-child::after {
    left: auto;
    inset-inline-start: 100%;
  }
}
.grid-carousel-item {
  min-width: 0;
}
.grid-carousel--stack {
  grid: auto / repeat(var(--grid-columns-actual), minmax(0, 1fr));
}
.grid-carousel--products {
  --grid-columns: 5;
}
@media not all and (min-width: 1400px) {
  .grid-carousel--products {
    --grid-columns: 4;
  }
}
@media not all and (min-width: 1200px) {
  .grid-carousel--products {
    --grid-columns: 3.2;
  }
}
@media not all and (min-width: 992px) {
  .grid-carousel--products {
    --grid-columns: 2.4;
  }
}
@media not all and (min-width: 768px) {
  .grid-carousel--products {
    --grid-columns: 2.2;
  }
}
@media not all and (min-width: 576px) {
  .grid-carousel--products {
    --grid-columns: var(--grid-products-columns-sm);
  }
}
.grid-carousel--products.grid-carousel--stack:not(.product-card-container--diff-bg) {
  row-gap: min(3rem, calc(1.5rem + 2vw));
}
@media not all and (min-width: 576px) {
  .grid-carousel--products.grid-carousel--stack.product-card-container--diff-bg {
    margin: 0 calc(var(--container-padding) * -1 + 0.5rem);
    gap: 0.5rem;
  }
}
split-lines {
  display: block;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  appearance: none;
}
@keyframes dash {
  0% {
    stroke-dashoffset: 280;
  }
  50% {
    stroke-dashoffset: 75;
    transform: rotate(135deg);
  }
  100% {
    stroke-dashoffset: 280;
    transform: rotate(450deg);
  }
}
.spinner .path {
  stroke-dasharray: 280;
  stroke-dashoffset: 0;
  transform-origin: center;
  stroke: currentColor;
  animation: dash 1.4s ease-in-out infinite;
}
deferred-media {
  display: block;
}
.deferred-media-poster {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.play-icon svg {
  width: 4rem;
  height: 4rem;
  transition: transform 200ms;
  filter: drop-shadow(0px 8px 8px rgb(0 0 0 / 37.5%));
}
.play-icon:hover svg {
  transform: scale(1.0875);
}
.play-icon:active svg {
  transform: scale(0.95);
}
.play-icon .spinner {
  display: none;
}
.video-player--loading .play-icon > :not(.spinner) {
  display: none;
}
.video-player--loading .play-icon .spinner {
  display: block;
}
.prose-align.text-left .prose {
  margin-left: 0;
  margin-right: 0;
}
.prose-align.text-center .prose {
  margin-left: auto;
  margin-right: auto;
}
.prose-align.text-right .prose {
  margin-left: auto;
  margin-right: 0;
}
.dropdown-menu {
  position: absolute;
  left: 0;
  top: 0;
  width: max-content;
  -webkit-user-select: none;
  user-select: none;
  transform-origin: top;
  z-index: 1100;
  background: rgb(var(--color-modal-background));
  color: rgb(var(--color-modal-foreground));
  overflow-y: auto;
  overscroll-behavior-y: contain;
  border-radius: var(--dropdown-corner-radius);
  --tw-shadow: 0 8px 16px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
  outline-style: solid;
  outline-width: 1px;
  outline-offset: -1px;
  outline-color: rgb(var(--color-foreground) / 0.1);
}
smooth-collapse.collapsing > details[open] > summary .collapse-chevron svg {
  transform: rotate(90deg);
}
.shopify-section.loading .collapse-chevron svg {
  transition: none !important;
}
details > summary {
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}
.disabled {
  opacity: 0.5;
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}
.hide-input-arrows::-webkit-outer-spin-button,
.hide-input-arrows::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.hide-input-arrows {
  -moz-appearance: textfield;
}
.custom-scrollbar {
  scrollbar-width: thin;
}
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(var(--gray-200));
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(var(--color-base-foreground));
}
.sale-price {
  color: rgb(var(--color-sale-price));
}
.alert {
  border-radius: var(--block-corner-radius);
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
}
@media not all and (min-width: 768px) {
  .alert {
    font-size: var(--size-text-sm);
  }
}
@media (min-width: 768px) {
  .alert {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
.alert-success {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-success-light) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-success) / var(--tw-text-opacity));
}
.alert-danger {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-danger-light) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-danger) / var(--tw-text-opacity));
}
.progress-bar {
  background-color: rgb(var(--black) / 6%);
  height: 0.375rem;
  overflow: hidden;
  border-radius: var(--block-xs-corner-radius);
}
.progress-bar-inner {
  background-color: rgb(var(--color-progress-bar));
  height: 100%;
}
.info-badge {
  text-transform: var(--info-badge-text-transform);
  display: inline-block;
  white-space: nowrap;
  border-radius: var(--block-xs-corner-radius);
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-foreground) / var(--tw-bg-opacity));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: var(--size-text-2xs);
  font-weight: var(--font-body-weight-bold);
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(var(--color-background) / var(--tw-text-opacity));
}
@media (min-width: 768px) {
  .info-badge {
    font-size: var(--size-text-xs);
  }
}
.info-badge.sale-badge {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-sale-badge) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-sale-badge-text) / var(--tw-text-opacity));
}
.info-badge.sold-out-badge {
  background: rgb(var(--color-sold-out-badge));
  color: rgb(var(--color-sold-out-badge-text));
}
.info-badge.article-badge {
  background: rgb(var(--color-article-category-badge));
  color: rgb(var(--color-article-category-badge-text));
}
.info-badge.custom-badge {
  background: rgb(var(--color-custom-badge));
  color: rgb(var(--color-custom-badge-text));
}
.product-carousel-wrapper {
  --carousel-button-y: 37.5%;
}
.localization-select {
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  border-width: 2px;
  border-color: rgb(var(--color-foreground) / var(--tw-border-opacity));
  --tw-border-opacity: 0.15;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 4rem;
}
.localization-select:hover {
  --tw-border-opacity: 0.3;
}
@media (min-width: 768px) {
  .localization-select {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1.5rem;
  }
}
:not(dropdown-element.fading-out) > details[open] > summary > .localization-select {
  --tw-border-opacity: 1;
}
.localization-select__chevron {
  position: absolute;
  right: 1.5rem;
  top: 0px;
  bottom: 0px;
  display: flex;
  width: 1rem;
  align-items: center;
}
.button-pill {
  display: flex;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  align-items: center;
  white-space: nowrap;
  border-radius: 9999px;
  font-size: var(--size-text-sm);
  font-weight: var(--font-body-weight-bold);
  line-height: 1.25;
  text-decoration-line: none;
  padding-top: 0.75em;
  padding-bottom: 0.75em;
  padding-left: 1.5em;
  padding-right: 1.5em;
}
@media (min-width: 768px) {
  .button-pill {
    font-size: var(--size-text-base);
  }
}
.button-pill {
  background: rgb(var(--button-pill-background, var(--color-foreground) / 6%));
  color: rgb(var(--button-pill-foreground, var(--color-foreground)));
}
.button-pill:hover {
  background: rgb(var(--button-pill-background-hover, var(--color-foreground) / 10%));
}
.button-pill:active {
  background: rgb(var(--button-pill-background-active, var(--color-foreground) / 20%));
}
.button-pill--sm {
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .button-pill--sm {
    font-size: var(--size-text-sm);
  }
}
.button-pill.active,
input[type="radio"]:checked + label.button-pill {
  --color-foreground: var(--color-base-accent-foreground);
  --color-background: var(--color-base-accent);
  pointer-events: none;
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-base-accent) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-base-accent-foreground) / var(--tw-text-opacity));
}
sticky-sidebar {
  display: flex;
  align-items: flex-start;
  height: 100%;
}
.sticky-sidebar-disabled {
  --sticky-sidebar-disabled: 1;
}
.shopify-challenge__container {
  padding: 2rem 0;
}
.shopify-challenge__container p {
  margin-bottom: 1rem;
}
.shopify-challenge__container .btn {
  margin-top: 1.5rem;
}
@keyframes animateSkeleton {
  0% {
    background-position-x: -400px;
  }
  66%,
  100% {
    background-position-x: calc(100% + 400px);
  }
}
.skeleton {
  background-color: rgb(var(--color-foreground) / 8%);
  background-image: linear-gradient(
    90deg,
    rgb(var(--white) / 0%) 0%,
    rgb(var(--white) / 75%) 40%,
    rgb(var(--white) / 75%) 60%,
    rgb(var(--white) / 0%) 100%
  );
  background-size: 400px 100%;
  background-repeat: no-repeat;
  animation: animateSkeleton 1.25s ease-in-out infinite;
  border-radius: var(--block-sm-corner-radius);
}
height-observer {
  display: contents;
}
.separator-dot {
  height: 0.25rem;
  width: 0.25rem;
  flex-shrink: 0;
  border-radius: 9999px;
  background-color: rgb(var(--color-foreground) / 0.25);
}
.metafield-rich_text_field > :first-child {
  margin-top: 0;
}
.metafield-rich_text_field > :last-child {
  margin-bottom: 0;
}
.image-hover-zoom-enabled .image-hover-zoom {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 1000ms;
  transition-timing-function: cubic-bezier(0.34, 0.01, 0.04, 0.98);
}
.image-hover-zoom-enabled .group:hover .image-hover-zoom {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.section[enable-transparent-header] .page-banner__content {
  padding-top: var(--allow-transparent-header-padding);
}
.content-over-media {
  position: relative;
  display: grid;
}
.content-over-media[enable-transparent-header] .content-over-media__content {
  padding-top: max(var(--section-vertical-spacing), var(--allow-transparent-header-padding));
}
.content-over-media__media {
  pointer-events: none;
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  -webkit-user-select: none;
  user-select: none;
}
.content-over-media__content {
  position: relative;
  z-index: 10;
  grid-column-start: 1;
  grid-row-start: 1;
  max-width: 48rem;
  padding-top: var(--section-vertical-spacing);
  padding-bottom: var(--section-vertical-spacing);
}
.content-over-media--full-width .content-over-media__media {
  margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
}
@media (min-width: 576px) {
  .content-over-media--block {
    overflow: hidden;
    border-radius: var(--block-corner-radius);
  }
}
.content-over-media--block .content-over-media__content {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
@media (min-width: 576px) {
  .content-over-media--block .content-over-media__content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
@media (min-width: 768px) {
  .content-over-media--block .content-over-media__content {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}
@media (min-width: 992px) {
  .content-over-media--block .content-over-media__content {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}
.content-over-media--natural-size .content-over-media__media {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  grid-column-start: 1;
  grid-row-start: 1;
}
.placeholder {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
  fill: #737373;
}
.placeholder--dark {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
  fill: #525252;
}
html.js .prevent-transition-on-load {
  transition: none !important;
}
html.js .prevent-transition-on-load * {
  transition: none !important;
}
:where(input[type="radio"], input[type="checkbox"]).visually-hidden:focus-visible + label {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 4px;
  outline-color: #000;
}
.prose img {
  border-radius: var(--block-sm-corner-radius);
}
.media-lq-placeholder {
  filter: blur(10px);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.no-js .media-lg-placeholder {
  display: none;
}
.image-loader {
  display: grid;
}
.image-loader.loading {
  background-color: rgb(var(--color-foreground) / 0.05);
}
.image-loader.loaded .loading-bar {
  display: none;
}
.image-loader > * {
  grid-area: 1/1;
  min-height: 0;
}
.image-loader img:first-child {
  z-index: 2;
}
.js .image-loader.loading img {
  opacity: 0;
}
.loading-bar {
  width: min(33.33%, 4rem);
  z-index: 6;
  background: rgb(var(--color-base-background));
  height: 0.125rem;
  align-self: center;
  justify-self: center;
  overflow: hidden;
}
.loading-bar::before {
  display: block;
  height: 100%;
  width: 100%;
  --tw-content: "";
  content: var(--tw-content);
  background: rgb(var(--color-base-foreground));
  animation: 1.2s mediaLoadingBar infinite;
}
@keyframes mediaLoadingBar {
  0% {
    transform-origin: left;
    transform: scaleX(0);
  }

  40% {
    transform-origin: left;
    transform: scaleX(1);
  }

  40.01% {
    transform-origin: right;
    transform: scaleX(1);
  }

  80%,
  100% {
    transform-origin: right;
    transform: scaleX(0);
  }
}
scroll-animate {
  display: contents;
}
.fit-cover {
  aspect-ratio: var(--aspect-ratio, 16 / 9);
  position: absolute;
  min-width: 100%;
  min-height: 100%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
video-player {
  display: block;
}
.modal {
  --color-background: var(--color-modal-background);
  --color-foreground: var(--color-modal-foreground);
  --color-headings: var(--color-modal-foreground);
}
.modal::part(panel) {
  --tw-shadow: 0 8px 16px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.modal [slot="content"]:focus {
  outline: none;
}
.modal-drawer::part(panel) {
  position: relative;
  width: 28rem;
  max-width: 90vw;
  box-sizing: border-box;
  border-radius: 0;
}
.modal-drawer--lg::part(panel) {
  width: 34rem;
}
.modal-drawer[child]::part(panel-wrapper) {
  position: absolute;
  padding: 0;
  left: calc(100% - 1px);
  width: 28rem;
  max-width: 85vw;
  overflow: clip;
}
.modal-drawer[child]::part(panel) {
  border-left: 1.25px solid rgb(var(--color-foreground) / 6%);
}
.modal-drawer::part(panel-wrapper) {
  justify-content: flex-end;
}
.modal-drawer--top::part(panel-wrapper) {
  justify-content: center;
  align-items: flex-start;
}
.modal-drawer--top::part(panel) {
  width: auto;
  max-width: none;
}
.modal-drawer--right::part(panel-wrapper) {
  justify-content: flex-end;
}
.modal-drawer--bottom::part(panel-wrapper) {
  justify-content: center;
  align-items: flex-end;
}
.modal-drawer--bottom::part(panel) {
  width: auto;
  max-width: none;
}
.modal-drawer--left::part(panel-wrapper) {
  justify-content: flex-start;
}
@media not all and (min-width: 768px) {
  .mobile-bottom-modal-content {
    max-height: min(650px, 85vh);
  }
}
.modal:not(:defined) [slot="content"] {
  display: none;
}
.modal-close {
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  border-radius: 100rem;
  padding: 0.5rem;
  box-sizing: content-box;
  flex-shrink: 0;
}
.modal-close svg {
  width: 1.25rem;
  --icon-stroke-width: var(--icon-sm-stroke-width);
}
@media (min-width: 768px) {
  .modal-close svg {
    width: 1.5rem;
    --icon-stroke-width: var(--icon-md-stroke-width);
  }
}
.modal-close:hover {
  background-color: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}
.bottom-modal::part(panel) {
  max-height: 100%;
  overflow-y: auto;
  border-radius: 0;
}
.bottom-modal::part(panel-wrapper) {
  align-items: flex-end;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}
@media not all and (min-width: 768px) {
  .bottom-modal::part(panel-wrapper) {
    padding-left: 0;
    padding-right: 0;
  }
}
.bottom-modal[position="center"]::part(panel-wrapper) {
  justify-content: center;
}
@media (min-width: 768px) {
  .bottom-modal[position="center-center"]::part(panel) {
    border-radius: var(--block-corner-radius);
  }
}
.bottom-modal[position="center-center"]::part(panel-wrapper) {
  align-items: center;
  justify-content: center;
}
@media not all and (min-width: 768px) {
  .bottom-modal[position="center-center"]::part(panel-wrapper) {
    align-items: flex-end;
  }
}
.bottom-modal[position="right"]::part(panel-wrapper) {
  justify-content: flex-end;
}
.bottom-modal.bottom-modal-md::part(panel) {
  width: 32rem;
}
.bottom-modal.bottom-modal-lg::part(panel) {
  width: 40rem;
}
.bottom-modal.modal-w-full::part(panel) {
  width: 100%;
}
.cart-modal {
  --cart-modal-px: 1.5rem;
}
@media (min-width: 576px) {
  .cart-modal {
    --cart-modal-px: 2rem;
  }
}
@media (min-width: 768px) {
  .cart-modal {
    --cart-modal-px: 2.5rem;
  }
}
.cart-modal::part(panel) {
  width: 32rem;
  overflow: hidden;
}
.mega-menu-drawer::part(panel) {
  width: var(--mega-menu-drawer-panel-width, 26rem);
  box-shadow: none;
}
.mega-menu-drawer .modal-close {
  margin-left: 1.5rem;
}
.modal-header {
  border-bottom-width: 1.25px;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-bottom: 1.5rem;
  padding-top: 1.5rem;
}
@media (min-width: 576px) {
  .modal-header {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    padding-bottom: 2rem;
    padding-top: 2rem;
  }
}
.modal-body {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
@media (min-width: 576px) {
  .modal-body {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}
@media not all and (min-width: 768px) {
  .modal-drawer--mobile-bottom::part(panel-wrapper) {
    justify-content: stretch;
    align-items: flex-end;
    padding: 0;
  }

  .modal-drawer--mobile-bottom::part(panel) {
    width: 100%;
    max-width: none;
  }
}
.modal-search::part(panel) {
  width: 52rem;
  overflow-y: auto;
}
.modal-search::part(panel-wrapper) {
}
.modal-search--full-width::part(panel) {
  width: 100%;
  border-radius: 0;
}
.modal-search--full-width::part(panel-wrapper) {
  padding-left: 0px;
  padding-right: 0px;
}
.modal-search--full-width [slot="content"] {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
.modal-search--full-width .predictive-search__carousel {
  --bleed: var(--container-outer-width);
}
.modal--absolute::part(panel) {
  position: absolute;
}
.modal--absolute::part(panel-wrapper) {
  position: absolute;
}
.modal--absolute::part(overlay) {
  position: absolute;
}
.cart-modal-child {
  --overlay-bg: rgb(0 0 0 / 25%);
}
.cart-modal-child::part(panel-wrapper) {
  padding: 0;
}
.cart-modal-child::part(panel) {
  width: 100%;
  border-radius: 0;
}
.privacy-banner-modal::part(panel-wrapper) {
  align-items: flex-end;
  justify-content: center;
  padding: 1rem;
}
@media (min-width: 768px) {
  .privacy-banner-modal::part(panel-wrapper) {
    padding: 1.5rem;
  }
}
.privacy-banner-modal::part(panel) {
  width: 100%;
  max-width: 42rem;
  border-width: 1.25px;
  border-style: solid;
  border-color: rgb(var(--color-foreground) / 6%);
  padding: 1.25rem;
  --tw-shadow: 0 20px 60px 15px rgba(0 0 0 / 30%);
  --tw-shadow-colored: 0 20px 60px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.privacy-banner-modal--left::part(panel-wrapper) {
  justify-content: flex-start;
}
@media (min-width: 576px) {
  .privacy-banner-modal--left::part(panel) {
    max-width: 24rem;
  }
}
.privacy-banner-modal--right::part(panel-wrapper) {
  justify-content: flex-end;
}
@media (min-width: 576px) {
  .privacy-banner-modal--right::part(panel) {
    max-width: 24rem;
  }
}
.newsletter-modal::part(panel-wrapper) {
  align-items: flex-end;
  justify-content: center;
  padding: 1rem;
}
@media (min-width: 768px) {
  .newsletter-modal::part(panel-wrapper) {
    padding: 1.5rem;
  }
}
.newsletter-modal::part(panel) {
  --tw-shadow: 0 20px 60px 15px rgba(0 0 0 / 30%);
  --tw-shadow-colored: 0 20px 60px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
@media (min-width: 768px) {
  .newsletter-modal--bottom-left::part(panel-wrapper) {
    align-items: flex-end;
    justify-content: flex-start;
  }

  .newsletter-modal--center::part(panel-wrapper) {
    align-items: center;
    justify-content: center;
  }

  .newsletter-modal--bottom-right::part(panel-wrapper) {
    align-items: flex-end;
    justify-content: flex-end;
  }
}
.modal--scrollable::part(panel) {
  max-height: 100%;
  overflow-y: auto;
}
.shipping-estimator-modal::part(panel) {
  width: 100%;
}
@media (min-width: 768px) {
  .shipping-estimator-modal::part(panel) {
    width: 512px;
    max-width: 100%;
  }
}
.collection-list-blocks {
  align-items: start;

  --grid-columns: 6;
}
@media not all and (min-width: 1400px) {
  .collection-list-blocks {
    --grid-columns: 5;
  }
}
@media not all and (min-width: 1200px) {
  .collection-list-blocks {
    --grid-columns: 4.1;
  }
}
@media not all and (min-width: 992px) {
  .collection-list-blocks {
    --grid-columns: 3.1;
  }
}
@media not all and (min-width: 768px) {
  .collection-list-blocks {
    --grid-columns: 2.1;
  }
}
@media not all and (min-width: 576px) {
  .collection-list-blocks {
    --grid-columns: var(--collection-list-columns-mobile);
  }
}
.collection-block {
  display: block;
  --color-headings: var(--color-base-headings);
}
.collection-block--overlay {
  --color-headings: 255 255 255;

  display: grid;
}
.collection-block--overlay > * {
  grid-area: 1 / 1;
}
.collection-block__overlay::before {
  content: "";
  background: var(--blocks-item-overlay-bg, rgb(0 0 0 / 15%));
  opacity: var(--blocks-item-overlay-bg-opacity, 1);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
}
.collection-block__overlay {
  display: grid;
  border-radius: var(--block-corner-radius);
  padding: 1rem;
}
@media (min-width: 768px) {
  .collection-block__overlay {
    padding: 1.5rem;
  }
}
@media (min-width: 1400px) {
  .collection-block__overlay {
    padding: 2rem;
  }
}
.section-main-collection-banner + .section-collection-list {
  --section-vertical-spacing: min(3rem, calc(1.5rem + 2vw));
}
.color {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  --color-rating-star-empty: var(--color-foreground);
}
.input,
.spr-form-input-text,
.spr-form-input-textarea,
.spr-form-input-email {
  display: block;
  padding: 0.875em 0.875em;
  border-radius: var(--input-corner-radius);
  background-color: transparent;
  color: rgb(var(--color-foreground));
  font: inherit;
  max-width: 100%;
  width: 100%;
  line-height: 1.25em;
  transition: 100ms border-color;
  appearance: none;
  text-align: left;
}
.input:hover,
.spr-form-input-text:hover,
.spr-form-input-textarea:hover,
.spr-form-input-email:hover {
  border-color: rgb(var(--color-foreground) / 10%);
}
.input:focus,
.spr-form-input-text:focus,
.spr-form-input-textarea:focus,
.spr-form-input-email:focus {
  border-color: rgb(var(--color-base-accent));
  outline: 0;
}
.input.has-icon-left,
.spr-form-input-text.has-icon-left,
.spr-form-input-textarea.has-icon-left,
.spr-form-input-email.has-icon-left {
  padding-left: 3rem;
}
.input.has-icon-right,
.spr-form-input-text.has-icon-right,
.spr-form-input-textarea.has-icon-right,
.spr-form-input-email.has-icon-right {
  padding-right: 3rem;
}
.inputs-filled
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email) {
  background-color: rgb(var(--color-foreground) / 6%);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: transparent;
}
.inputs-filled
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email):hover {
  --tw-ring-color: rgb(var(--color-foreground) / 0.1);
}
.inputs-filled
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email):focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
  outline: 0;
}
.inputs-outline
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email) {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: rgb(var(--color-foreground) / 0.2);
}
.inputs-outline
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email):hover {
  --tw-ring-color: rgb(var(--color-foreground) / 0.3);
}
.inputs-outline
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email):focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
  outline: 0;
}
.use-input-accent
  :where(.input, .spr-form-input-text, .spr-form-input-textarea, .spr-form-input-email):focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-input-accent) / var(--tw-ring-opacity));
}
.input-wrapper {
  position: relative;
}
.input-wrapper .icon-left,
.input-wrapper .icon-right {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}
.input-wrapper .icon-left {
  left: 0;
}
.input-wrapper .icon-right {
  right: 0;
}
select.input,
.input.select {
  padding-right: 2.5rem;
}
select.input.select-inline {
  background-color: transparent;
  padding: 0;
  padding-right: 2.25rem;
  background-position: right 0.5rem center;
  border: 0;
  font-weight: var(--weight-bold);
}
button.select,
.form-select {
  position: relative;
}
button.select::after,
.form-select::after {
  content: "";
  display: block;
  width: 1rem;
  height: 1rem;
  background: rgb(var(--color-foreground));
  position: absolute;
  right: 1rem;
  top: calc(50% - 0.5rem);
  -webkit-mask: var(--svg-chevron);
  mask: var(--svg-chevron);
  transform: rotate(90deg);
  transition: transform 200ms;
}
:not(.fading-out) > details[open] > summary button.select::after {
  transform: rotate(270deg);
}
.checkbox,
.radio {
  display: flex;
  text-align: left;
}
.checkbox > input[type="checkbox"],
.radio > input[type="radio"] {
  width: 1.5em;
  height: 1.5em;
  border: var(--input-border-width) solid transparent;
  vertical-align: top;
  background-color: rgb(var(--color-foreground) / 6%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0%;
  appearance: none;
  transition:
    background-size 100ms,
    background-color 100ms;
  margin-right: 0.75em;
  flex-shrink: 0;
  cursor: pointer;
}
.checkbox > input[type="checkbox"]:hover,
.radio > input[type="radio"]:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: rgb(var(--color-foreground) / 0.1);
}
.checkbox > label,
.radio > label {
  cursor: pointer;
  line-height: 1.5em;
  -webkit-user-select: none;
  user-select: none;
  overflow-wrap: anywhere;
}
.checkbox > input[type="checkbox"] {
  background-image: var(--svg-checkbox-check);
  border-radius: var(--input-corner-radius);
}
.checkbox > input[type="checkbox"]:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-input-accent) / var(--tw-bg-opacity));
  background-size: 75%;
}
.radio > input[type="radio"] {
  border-radius: 10000px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-2 -2 4 4'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.radio > input[type="radio"]:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-input-accent) / var(--tw-bg-opacity));
  background-size: 50%;
}
.label,
.spr-form-label {
  font-size: var(--label-font-size);
  font-weight: var(--label-font-weight);
  font-family: var(--label-font-family);
  text-transform: var(--label-text-transform);
  display: inline-block;
  letter-spacing: 0.025em;
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .label,
  .spr-form-label {
    font-size: var(--size-text-sm);
  }
}
.spr-form-label {
  display: block;
}
.dots-indicator {
  display: inline-flex;
  flex-wrap: wrap;
  justify-content: center;
}
.dots-indicator__dot {
  padding: 0.5rem;
}
.dots-indicator__dot::before {
  display: block;
  content: "";
  width: 0.75rem;
  height: 0.75rem;
  border: 2px solid rgb(var(--white));
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 200ms;
  filter: drop-shadow(0px 0px 2px rgba(0 0 0 / 12.5%));
}
.dots-indicator__dot.active::before {
  background-color: rgb(var(--white));
}
.dots-indicator-filled .dots-indicator {
  background: rgb(var(--color-foreground) / 5%);
  padding: 0 0.125rem;
  border-radius: 100rem;
}
.dots-indicator-filled .dots-indicator .dots-indicator__dot::before {
  border: 0;
  background: rgb(var(--color-foreground) / 10%);
}
@media not all and (min-width: 768px) {
  .dots-indicator-filled .dots-indicator .dots-indicator__dot::before {
    width: 0.5rem;
    height: 0.5rem;
  }
}
.dots-indicator-filled .dots-indicator .dots-indicator__dot.active::before {
  background-color: rgb(var(--color-base-accent));
}
.dots-indicator-overlay .dots-indicator {
  background: rgb(var(--white) / 75%);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  padding: 0.25rem 0.375rem;
  border-radius: 100rem;
}
.dots-indicator-overlay .dots-indicator .dots-indicator__dot {
  border: 0;
  background: rgb(var(--color-foreground) / 12.5%);
}
@media not all and (min-width: 768px) {
  .dots-indicator-overlay .dots-indicator .dots-indicator__dot {
    width: 0.5rem;
    height: 0.5rem;
  }
}
.dots-indicator-overlay .dots-indicator .dots-indicator__dot.active {
  background-color: rgb(var(--color-base-accent));
}
.section.footer {
  --section-vertical-spacing: min(4rem, calc(1.75rem + 3vw));
  --color-background: var(--color-footer-background);
  --color-foreground: var(--color-footer-foreground);
  --color-headings: var(--color-footer-foreground);
  padding-bottom: 0;
}
@media not all and (min-width: 768px) {
  .section.footer {
    font-size: var(--size-text-sm);
  }
}
.footer__blocks {
  display: flex;
  margin-bottom: min(5rem, calc(2rem + 4vw));
  gap: 5rem;
  flex-wrap: wrap;
}
@media not all and (min-width: 992px) {
  .footer__blocks {
    gap: 3rem 1.5rem;
  }
}
.footer__blocks > * {
  flex: 1;
}
.footer__blocks a {
  display: inline-block;
}
.footer-block--text {
  flex-grow: 2;
  min-width: 200px;
}
@media not all and (min-width: 992px) {
  .footer-block--text {
    flex-basis: 100%;
    max-width: none;
  }
}
.footer-block--links {
  min-width: 150px;
  overflow-wrap: anywhere;
}
.footer-block--newsletter {
  flex-grow: 2;
  min-width: 240px;
}
@media not all and (min-width: 992px) {
  .footer-block--newsletter {
    flex-basis: 100%;
    max-width: none;
  }
}
.footer__bottom {
  padding: 2rem 0;
  display: flex;
  flex-wrap: wrap;
}
@media not all and (min-width: 1200px) {
  .footer__bottom {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }
}
.footer__bottom .payment-icon {
  width: 3rem;
  height: auto;
}
.footer__list-social {
  display: flex;
  align-items: center;
  gap: 1.5rem 2rem;
  flex-wrap: wrap;
}
@media not all and (min-width: 1200px) {
  .footer__list-social {
    justify-content: center;
  }
}
.footer__list-social svg {
  width: 1.5rem;
  height: 1.5rem;
  transition: transform 150ms;
}
@media not all and (min-width: 768px) {
  .footer__list-social svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}
.list-social__link {
  margin: -0.75rem;
  display: block;
  padding: 0.75rem;
}
.list-social__link:hover svg {
  transform: scale(1.1);
}
.list-social__link:active svg {
  transform: none;
}
.section-site-header {
  z-index: 1000;
}
.header {
  --color-background: var(--color-header-background);
  --color-foreground: var(--color-header-foreground);
  --color-headings: var(--color-header-foreground);

  background: rgb(var(--color-background) / var(--header-background-opacity, 100%));
  color: rgb(var(--color-foreground));
  position: relative;
  z-index: 1000;
  min-height: var(--header-min-height);
  display: flex;
  align-items: center;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
@media (min-width: 1150px) {
  .header {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }
}
.header__container {
  display: grid;
  align-items: center;
}
.header__logo {
  grid-area: logo;
}
.header__logo img {
  width: var(--header-logo-width);
}
@media not all and (min-width: 768px) {
  .header__logo img {
    width: var(--header-logo-width-mobile);
  }
}
.header__nav {
  grid-area: nav;
  display: flex;
  align-items: center;
}
.header__actions {
  grid-area: actions;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
@media (min-width: 1150px) {
  .header--desktop-logo-left-nav-left .header__container {
    grid-template-areas: "logo nav actions";
    grid-template-columns: auto auto minmax(max-content, 1fr);
  }

  .header--desktop-logo-left-nav-left .header__nav {
    margin-left: 3rem;
    margin-right: 1rem;
  }
  .header--desktop-logo-left-nav-center .header__container {
    grid-template-areas: "logo nav actions";
    grid-template-columns: minmax(var(--header-logo-width), 1fr) auto minmax(max-content, 1fr);
    gap: 1.5rem;
  }

  .header--desktop-logo-left-nav-center .header__nav ul {
    justify-content: center;
  }

  .header--desktop-logo-left-nav-center .header__logo {
    justify-self: start;
  }
  .header--desktop-logo-center-nav-left .header__container {
    grid-template-areas: "nav logo actions";
    grid-template-columns: 1fr auto minmax(max-content, 1fr);
    gap: 1.5rem;
  }
}
@media not all and (min-width: 1150px) {
  .header--mobile-logo-left .header__container {
    grid-template-areas: "nav logo actions";
    grid-template-columns: auto auto minmax(max-content, 1fr);
  }

  .header--mobile-logo-left .header__nav {
    margin-right: 1.5rem;
  }
  .header--mobile-logo-center .header__container {
    grid-template-areas: "nav logo actions";
    grid-template-columns: 1fr auto minmax(max-content, 1fr);
    gap: 0.5rem;
  }
}
.section-site-header--sticky {
  position: sticky;
  top: 0;
  z-index: 1000;
}
.section-site-header--sticky .header {
  border-bottom: 1.25px solid rgb(var(--color-foreground) / 5%);
}
.section-site-header--hidden {
  transform: translateY(calc(-100% - 2px));
}
.section-site-header.animate {
  transition: transform 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
}
ul.menu {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem 1.5rem;
}
@media not all and (min-width: 1150px) {
  ul.menu {
    display: none;
  }
}
ul.menu .menu-item {
  font-family: var(--navigation-font-family);
  font-weight: var(--navigation-font-weight);
  text-transform: var(--navigation-text-transform);
  font-style: var(--navigation-font-style);

  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  line-height: 1;
  transition: opacity 150ms;
}
ul.menu .menu-item.active .collapse-chevron svg {
  transform: rotate(270deg);
}
ul.menu .menu-item:hover {
  opacity: 0.7;
}
.header .header-dropdown {
  min-width: 14rem;
  padding: 1rem 0;
}
.header .header-dropdown details {
  transition: margin 250ms;
}
.header .header-dropdown details[open]:not(.first) {
  margin-top: 0.5rem;
}
.header .header-dropdown details[open]:not(.last) {
  margin-bottom: 0.5rem;
}
.header .header-dropdown smooth-collapse.collapsing details[open] {
  margin: 0;
}
.dropdown-menu-item,
.side-menu-item,
.dropdown-list-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  text-decoration: none;
  line-height: 1.5;
  overflow-wrap: anywhere;
}
.dropdown-menu-item .collapse-chevron,
.side-menu-item .collapse-chevron,
.dropdown-list-item .collapse-chevron {
  margin-left: 1rem;
}
.dropdown-menu-item,
.side-menu-item {
  font-family: var(--navigation-font-family);
  font-weight: var(--navigation-font-weight);
  text-transform: var(--navigation-text-transform);
  font-style: var(--navigation-font-style);
}
.dropdown-menu-item:hover,
.side-menu-item:hover {
  background: rgb(var(--color-modal-foreground) / 2.5%);
}
.dropdown-menu-item.active,
.side-menu-item.active {
  background: var(--color-modal-active);
}
@media (min-width: 768px) {
  .dropdown-list--small .dropdown-list-item {
    padding: 0.75rem 1.25rem;
  }
}
.dropdown-list-item:hover {
  background: rgb(var(--color-modal-foreground) / 2.5%);
}
.dropdown-list-item.active {
  background: var(--color-selected-dropdown-item);
  color: rgb(var(--color-selected-dropdown-item-foreground));
}
.side-menu-item {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  font-size: var(--size-text-lg);
  font-family: var(--navigation-font-family);
  font-weight: var(--navigation-font-weight);
  text-transform: var(--navigation-text-transform);
  font-style: var(--navigation-font-style);
}
.side-menu-item .collapse-chevron {
  margin-left: auto;
}
.side-menu-item .collapse-chevron svg {
  transition: none;
  transform: none;
}
.dropdown-list-item.active {
  font-weight: var(--weight-bold);
}
.collapse-chevron {
  width: 1rem;
  height: auto;
  flex-shrink: 0;
  --stroke-width: 2;
}
.collapse-chevron svg {
  transform: rotate(90deg);
  transition: transform 200ms;
}
details[open] > summary > .dropdown-menu-item {
  background: var(--color-modal-active);
}
details[open] > summary .collapse-chevron svg {
  transform: rotate(270deg);
}
.fading-out > details[open] > summary .collapse-chevron svg {
  transform: rotate(90deg);
}
.dropdown-submenu {
  background: var(--color-modal-active);
}
.dropdown-submenu .dropdown-submenu-item {
  display: block;
  padding: 0.75rem 1.5rem;
  font-size: var(--size-text-sm);
}
.dropdown-submenu .dropdown-submenu-item:first-child {
  padding-top: 0.5rem;
}
.dropdown-submenu .dropdown-submenu-item:last-child {
  padding-bottom: 1.5rem;
}
.header-separator {
  height: 1.5rem;
  width: 1px;
  background-color: rgb(var(--color-foreground) / 6%);
  margin-left: 1.5rem;
  margin-right: 1rem;
}
.country-flag {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 100rem;
  box-shadow:
    inset 0 0.375rem 0.25rem -0.25rem rgb(255 255 255 / 40%),
    inset 0 0 0.25rem 0 rgb(0 0 0 / 20%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}
.country-flag[data-iso-code="US"] {
  background-size: 200%;
  background-position-x: 58%;
}
.header-icon-btn {
  display: flex;
  align-items: center;
  position: relative;
  text-decoration: none;
  flex-shrink: 0;
  margin: -0.5rem;
  padding: 0.5rem;
}
.header-icon-btn svg {
  width: 1.5rem;
  --icon-stroke-width: var(--icon-md-stroke-width);
}
@media (min-width: 768px) {
  .header-icon-btn svg {
    width: 1.75rem;
    --icon-stroke-width: var(--icon-lg-stroke-width);
  }
}
.hamburger {
  display: none;

  padding: 0.5rem 0;
  cursor: pointer;

  --bar-width: 20px;
  --bar-height: 1.5px;
  --anim-speed: 160ms;
}
@media not all and (min-width: 1150px) {
  .hamburger {
    display: block;
  }
}
@media not all and (min-width: 768px) {
  .hamburger {
    margin: 0;
  }
}
.hamburger span {
  width: var(--bar-width);
  height: var(--bar-height);
  background: rgb(var(--color-foreground));
  display: block;
  margin-bottom: 6px;
  transition:
    margin var(--anim-speed),
    transform var(--anim-speed);
  transition-delay: calc(var(--anim-speed) * 0.75), 0s;
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}
.hamburger span:last-child {
  margin-bottom: 0;
}
.hamburger.active span {
  transition-delay: 0s, calc(var(--anim-speed) * 0.5);
}
.hamburger.active span:nth-child(1) {
  margin-bottom: 0;
  transform: rotate(45deg);
}
.hamburger.active span:nth-child(2) {
  transform: translateY(calc(var(--bar-height) * -1)) rotate(-45deg);
}
.header-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: var(--overlay-bg);
  z-index: 500;
  display: none;
}
.header-overlay.active {
  display: block;
  pointer-events: all;
}
.side-menu-header {
  display: flex;
  align-items: center;
  height: 5rem;
}
.header__logo-img {
  transition: opacity 333ms;
}
.header__logo-img--header-transparent {
  display: none;
}
.product-card {
  display: flex;
  flex-direction: column;
  height: 100%;

  --color-background: var(--color-product-card-background);
  --color-foreground: var(--color-product-card-text);
}
.product-card .price {
  align-items: flex-end;
}
.product-card .regular-price .price-from {
  opacity: 0.75;
}
.product-card .color-swatch-selector label .inner {
  width: 1rem;
}
@media (min-width: 576px) {
  .product-card .color-swatch-selector label .inner {
    width: 1.25rem;
  }
}
.product-card__media-spinner {
  background: rgb(var(--color-product-card-background));
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  pointer-events: none;
  transition: opacity 150ms;
}
.product-card__media-spinner > svg {
  width: 3rem;
  height: 3rem;
}
.product-card--media-loading .product-card__media-spinner {
  opacity: 1;
}
.product-card-sizes {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  font-size: var(--size-text-xs);
}
.product-card-sizes:empty {
  display: none;
}
@media (min-width: 768px) {
  .product-card-sizes {
    gap: 0.375rem;
  }
}
.product-card-size-option {
  height: 1.75rem;
  min-width: 1.75rem;
  border-radius: var(--block-xs-corner-radius);
  border-width: 1.25px;
  border-color: rgb(var(--color-foreground) / 0.2);
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  text-align: center;
  line-height: 1.75rem;
}
.prose-align.product-card-size-option .prose {
  margin-left: auto;
  margin-right: auto;
}
@media not all and (min-width: 576px) {
  .section-header--with-link.product-card-size-option {
    text-align: left;
  }

  .section-header--with-link.product-card-size-option .filler-left {
    display: none;
  }
}
.product-card-size-option.unavailable {
  --tw-border-opacity: 1;
  border-color: rgb(var(--color-foreground) / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-foreground) / var(--tw-text-opacity));
  opacity: 0.2;
  background-image: var(--svg-product-card-size-unavailable);
  background-repeat: no-repeat;
  background-position: center center;
  background-size:
    100% 100%,
    auto;
}
@media not all and (min-width: 576px) {
  .product-card-size-option {
    height: 1.5rem;
    min-width: 0px;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    font-size: var(--size-text-2xs);
    line-height: 1.5rem;
  }
}
.carousel-prev,
.carousel-next {
  --width: 3.5rem;
  --height: 3.5rem;

  width: var(--width);
  height: var(--height);
  background: rgb(var(--white));
  color: rgb(var(--black));
  border: 1.25px solid rgb(var(--black) / 6%);
  border-radius: 100px;
  --tw-shadow: 0 0 8px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 0 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
  z-index: 200;
  position: absolute;
  cursor: pointer;
  transition: transform 125ms;
  display: flex;
  align-items: center;
  justify-content: center;
  top: calc(var(--carousel-button-y, 50%) - var(--height) / 2);
}
@media not all and (min-width: 768px) {
  .carousel-prev,
  .carousel-next {
    --width: 2.5rem;
    --height: 2.5rem;
  }
}
.carousel-prev svg,
.carousel-next svg {
  width: 1.25rem;
  height: 1.25rem;
}
.carousel-prev:hover,
.carousel-next:hover {
  transform: scale(1.0625);
}
.carousel-prev:active,
.carousel-next:active {
  transform: none;
}
.carousel-prev.is-hidden,
.carousel-next.is-hidden {
  transform: scale(0);
  pointer-events: none;
}
@media (hover: hover) {
  *:not(:hover) > .carousel-prev,
  *:not(:hover) > .carousel-next {
    transition-delay: 250ms;
    transform: scale(0);
  }
}
@media (hover: none), (pointer: coarse) {
  .carousel-prev,
  .carousel-next {
    display: none;
  }
}
.carousel-prev {
  inset-inline-start: var(--carousel-button-x-offset, calc(var(--width) / -2));
}
.carousel-prev svg {
  transform: scaleX(-1);
}
.carousel-next {
  inset-inline-end: var(--carousel-button-x-offset, calc(var(--width) / -2));
}
.scroll-shadow-start,
.scroll-shadow-end {
  position: absolute;
  pointer-events: none;
  --scroll-shadow-size: 3rem;
  background: linear-gradient(
    var(--scroll-shadow-angle),
    rgb(var(--color-background)),
    rgb(var(--color-background) / 0.75),
    transparent
  );
}
.scroll-shadow-start {
  --progress: calc(var(--scroll-progress) * 5);
  --progress-limited: min(1, var(--progress));
  opacity: var(--progress-limited);
}
.scroll-shadow-end {
  --progress: calc((var(--scroll-progress) - 0.8) * 5);
  --progress-limited: max(0, var(--progress));
  --progress-reversed: calc(1 - var(--progress-limited));
  opacity: var(--progress-reversed);
}
.scroll-shadow-horizontal .scroll-shadow-start {
  top: 0;
  bottom: 0;
  left: 0;
  width: calc(var(--scroll-shadow-size) * var(--progress-limited));
  --scroll-shadow-angle: 90deg;
}
.scroll-shadow-horizontal .scroll-shadow-end {
  top: 0;
  bottom: 0;
  right: 0;
  width: calc(var(--scroll-shadow-size) * var(--progress-reversed));
  --scroll-shadow-angle: 270deg;
}
.scroll-shadow-vertical .scroll-shadow-start {
  left: 0;
  right: 0;
  top: 0;
  height: calc(var(--scroll-shadow-size) * var(--progress-limited));
  --scroll-shadow-angle: 180deg;
}
.scroll-shadow-vertical .scroll-shadow-end {
  left: 0;
  right: 0;
  bottom: 0;
  height: calc(var(--scroll-shadow-size) * var(--progress-reversed));
  --scroll-shadow-angle: 0deg;
}
.slide-carousel {
  display: grid;
  align-items: start;
}
.slide-carousel > * {
  grid-area: 1 / -1;
}
.slide-carousel > *:not(.is-active) {
  opacity: 0;
  visibility: hidden;
}
.section-slideshow {
  --section-vertical-spacing: 3rem;
}
@media not all and (min-width: 768px) {
  .section-slideshow {
    --section-vertical-spacing: 2rem;
  }
}
.section-slideshow {
  --slideshow-header-offset: calc(
    var(--announcement-bar-height, 0px) + var(--header-height-actual, 0px)
  );
}
.slideshow-element {
  display: grid;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  background: rgb(var(--color-foreground));
  z-index: 50;
}
.slideshow-controls {
  position: absolute;
  bottom: 1.5rem;
  left: 0;
  right: 0;
  justify-content: flex-end;
  align-items: center;
  z-index: 200;
  opacity: 0;
  pointer-events: none;
  display: flex;

  transition: opacity 500ms;
}
.slideshow-controls.show {
  opacity: 1;
  pointer-events: all;
}
@media not all and (min-width: 768px) {
  .slideshow-controls {
    bottom: 1rem;
  }
}
.slideshow-controls .button-circle {
  --button-background: var(--color-base-background);
  --button-background-opacity: 0.9;
  --button-background-hover: var(--color-base-background);
  --button-background-active: var(--color-base-background);
  --button-foreground: var(--color-base-foreground);

  width: 3rem;
  height: 3rem;
  padding: 0;
  border-radius: 100rem;
}
@media not all and (min-width: 768px) {
  .slideshow-controls .button-circle {
    width: 2.5rem;
    height: 2.5rem;
  }
}
.slideshow-controls .button-circle svg {
  width: 1rem;
  height: 1rem;
}
.slideshow-controls .button-prev svg {
  transform: rotate(180deg);
}
.slideshow-controls .slide-counter {
  font-weight: var(--weight-bold);
  color: rgb(var(--color-background));
  font-size: var(--size-text-xs);
  letter-spacing: 0.05em;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  -webkit-user-select: none;
  user-select: none;
  text-shadow: 0 0 3px rgb(0 0 0 / 40%);
}
@media not all and (min-width: 768px) {
  .slideshow-controls .slide-counter {
    width: 2.5rem;
    height: 2.5rem;
    font-size: var(--size-text-2xs);
  }
}
.slideshow-controls .slide-counter svg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.slideshow-controls .slide-counter span {
  position: relative;
  z-index: 10;
}
.slideshow-controls .button-prev {
  margin-left: 1rem;
}
@media not all and (min-width: 768px) {
  .slideshow-controls .button-prev {
    margin-left: 0.75rem;
  }
}
.slideshow-controls .button-next {
  margin-left: 0.5rem;
}
@media not all and (min-width: 768px) {
  .slideshow-controls .button-next {
    margin-left: 0.5rem;
  }
}
.slideshow-element.autoplay-paused .slide-counter svg > circle:nth-child(1) {
  stroke: rgb(255 255 255 / 90%);
}
.slideshow-element.autoplay-paused .slide-counter svg > circle:nth-child(2) {
  display: none;
}
.slideshow-slide {
  grid-area: 1 / 1;
  display: grid;
  visibility: hidden;

  --slideshow-available-height: calc(var(--viewport-height) - var(--slideshow-header-offset));

  --slideshow-min-height: 500px;
}
@media (min-width: 768px) {
  .slideshow-slide {
    --slideshow-min-height: 560px;
  }
}
.slideshow-slide {
  min-height: max(
    var(--slideshow-min-height),
    min(var(--slideshow-available-height), var(--slideshow-target-height))
  );

  --slide-content-top-padding: min(4rem, calc(1.75rem + 3vw));
  --slide-content-bottom-padding: 4rem;
}
.slideshow-slide.active {
  visibility: visible;
}
.slideshow-slide.anim-in {
  z-index: 100;
}
.slideshow-slide:not(.active):not(.preload) :where(img, video) {
  display: none;
}
.js .slide-loading .slide-content {
  opacity: 0;
}
.slide-background {
  grid-area: 1 / 1;
  background-color: rgb(var(--gray-100));
  position: relative;
}
.slide-background::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: var(--slide-background-overlay, none);
  opacity: var(--slide-background-overlay-opacity, 0);
  z-index: 5;
}
@media not all and (min-width: 768px) {
  .slide-background::before {
    background: var(--slide-background-mobile-overlay, none);
    opacity: var(--slide-background-mobile-overlay-opacity, 0);
  }
}
.slide-background :where(img, svg, video, lqip-element) {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.slideshow-element[adaptive-height] .slide-background :where(img, svg, video, lqip-element) {
  position: static;
}
.slideshow-element[adaptive-height] .slideshow-slide {
  min-height: 0;
  align-self: start;
}
.slideshow-element[adaptive-height] .slideshow-slide:not(.active),
.slideshow-element[adaptive-height] .slideshow-slide.anim-out {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.slide-content {
  grid-area: 1 / 1;
  z-index: 75;
}
.slide-content > .container {
  height: 100%;
  display: flex;
  padding-top: var(--slide-content-top-padding);
  padding-bottom: var(--slide-content-bottom-padding);
}
.slideshow-element-wrapper:not(.slideshow-wrapper--block) .slide-content > .container {
  padding-top: max(var(--slide-content-top-padding), var(--allow-transparent-header-padding));
}
.slide-buttons {
  margin-top: 1.5rem;
  display: inline-flex;
  flex-wrap: wrap;
  column-gap: 0.75rem;
  row-gap: 0.5rem;
}
.slide-buttons:empty {
  display: none;
}
@media (min-width: 768px) {
  .slide-buttons {
    margin-top: 2rem;
  }
}
.slide-content-inner {
  min-width: 13rem;
  max-width: min(95%, var(--slide-content-max-width));
  width: 100%;
  color: rgb(var(--color-foreground));
}
@media not all and (min-width: 768px) {
  .slide-content-inner {
  }
  .slide-content-inner.mobile\:content-align-top-center {
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.mobile\:content-align-top-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.mobile\:content-align-top-right {
    margin-left: auto;
    text-align: right;
  }

  .slide-content-inner.mobile\:content-align-top-right .slide-buttons {
    justify-content: flex-end;
  }

  .slide-content-inner.mobile\:content-align-middle-left {
    align-self: center;
  }

  .slide-content-inner.mobile\:content-align-middle-left .slide-buttons {
    justify-content: flex-start;
  }

  .slide-content-inner.mobile\:content-align-middle-center {
    align-self: center;
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.mobile\:content-align-middle-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.mobile\:content-align-middle-right {
    align-self: center;
    margin-left: auto;
    text-align: right;
  }

  .slide-content-inner.mobile\:content-align-middle-right .slide-buttons {
    justify-content: flex-end;
  }

  .slide-content-inner.mobile\:content-align-bottom-left {
    align-self: flex-end;
  }

  .slide-content-inner.mobile\:content-align-bottom-left .slide-buttons {
    justify-content: flex-start;
  }

  .slide-content-inner.mobile\:content-align-bottom-center {
    align-self: flex-end;
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.mobile\:content-align-bottom-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.mobile\:content-align-bottom-right {
    align-self: flex-end;
    margin-left: auto;
    text-align: right;
    margin-bottom: 2rem;
  }

  .slide-content-inner.mobile\:content-align-bottom-right .slide-buttons {
    justify-content: flex-end;
  }
}
@media (min-width: 768px) {
  .slide-content-inner.desktop\:content-align-top-center {
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.desktop\:content-align-top-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.desktop\:content-align-top-right {
    margin-left: auto;
    text-align: right;
  }

  .slide-content-inner.desktop\:content-align-top-right .slide-buttons {
    justify-content: flex-end;
  }

  .slide-content-inner.desktop\:content-align-top-right.desktop\:content-text-left {
    width: 50%;
  }

  .slide-content-inner.desktop\:content-align-middle-left {
    align-self: center;
  }

  .slide-content-inner.desktop\:content-align-middle-left .slide-buttons {
    justify-content: flex-start;
  }

  .slide-content-inner.desktop\:content-align-middle-center {
    align-self: center;
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.desktop\:content-align-middle-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.desktop\:content-align-middle-right {
    align-self: center;
    margin-left: auto;
    text-align: right;
  }

  .slide-content-inner.desktop\:content-align-middle-right .slide-buttons {
    justify-content: flex-end;
  }

  .slide-content-inner.desktop\:content-align-middle-right.desktop\:content-text-left {
    width: 50%;
  }

  .slide-content-inner.desktop\:content-align-bottom-left {
    align-self: flex-end;
  }

  .slide-content-inner.desktop\:content-align-bottom-left .slide-buttons {
    justify-content: flex-start;
  }

  .slide-content-inner.desktop\:content-align-bottom-center {
    align-self: flex-end;
    margin: 0 auto;
    text-align: center;
  }

  .slide-content-inner.desktop\:content-align-bottom-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.desktop\:content-align-bottom-right {
    align-self: flex-end;
    margin-left: auto;
    text-align: right;
    margin-bottom: 2rem;
  }

  .slide-content-inner.desktop\:content-align-bottom-right .slide-buttons {
    justify-content: flex-end;
  }

  .slide-content-inner.desktop\:content-align-bottom-right.desktop\:content-text-left {
    width: 50%;
  }

  .slide-content-inner.desktop\:content-text-left {
    text-align: left;
  }

  .slide-content-inner.desktop\:content-text-left .slide-buttons {
    justify-content: flex-start;
  }

  .slide-content-inner.desktop\:content-text-center {
    text-align: center;
  }

  .slide-content-inner.desktop\:content-text-center .slide-buttons {
    justify-content: center;
  }

  .slide-content-inner.desktop\:content-text-right {
    text-align: right;
  }

  .slide-content-inner.desktop\:content-text-right .slide-buttons {
    justify-content: flex-end;
  }
}
.slide-content-inner.has-background {
  background-color: rgb(var(--color-background));
  padding: min(3rem, calc(1.5rem + 2vw));
}
.slideshow-wrapper--block {
  padding-top: max(var(--section-vertical-spacing), var(--allow-transparent-header-padding));
  padding-bottom: var(--section-vertical-spacing);
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}
.slideshow-wrapper--block .slideshow-element {
  max-width: var(--container-max-inner-width);
  border-radius: var(--block-corner-radius);
}
.slideshow-wrapper--block .slideshow-slide {
  --slideshow-available-height: calc(
    var(--viewport-height) - var(--slideshow-header-offset) -
      max(var(--section-vertical-spacing), var(--allow-transparent-header-padding)) -
      var(--section-vertical-spacing)
  );
}
body {
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
  letter-spacing: var(--font-body-letter-spacing);
  font-size: var(--size-text-base);
}
.body-font {
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
}
.h0,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  color: rgb(var(--color-headings));
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  text-transform: var(--heading-text-transform);
  letter-spacing: var(--heading-letter-spacing);
  overflow-wrap: anywhere;
  text-wrap: balance;
  display: block;
}
.prose :where(h1, h2, h3, h4, h5, h6),
.headings :where(h1, h2, h3, h4, h5, h6) {
  color: rgb(var(--color-headings));
  font-family: var(--font-heading-family) !important;
  font-style: var(--font-heading-style) !important;
  font-weight: var(--font-heading-weight) !important;
  letter-spacing: var(--heading-letter-spacing) !important;
  overflow-wrap: anywhere;
  text-wrap: balance;
}
.h0 {
  font-size: var(--size-text-h0);
  line-height: var(--line-height-h0);
}
.h1 {
  font-size: var(--size-text-h1);
  line-height: var(--line-height-h1);
}
.h2 {
  font-size: var(--size-text-h2);
  line-height: var(--line-height-h2);
}
.h3 {
  font-size: var(--size-text-h3);
  line-height: var(--line-height-h3);
}
.h4 {
  font-size: var(--size-text-h4);
  line-height: var(--line-height-h4);
}
.h5 {
  font-size: var(--size-text-h5);
  line-height: var(--line-height-h5);
}
.h6 {
  font-size: var(--size-text-h6);
  line-height: var(--line-height-h6);
}
.subheading {
  font-family: var(--label-font-family);
  font-weight: var(--label-font-weight);
  text-transform: var(--label-text-transform);
  color: rgb(var(--color-foreground) / var(--subheading-opacity, 0.75));
  overflow-wrap: anywhere;
  letter-spacing: 0.05em;
  font-size: var(--size-text-sm);
}
.product-vendor {
  overflow-wrap: anywhere;
}
.product-name {
  font-family: var(--product-card-font-family);
  font-weight: var(--product-card-font-weight);
  overflow-wrap: anywhere;
}
.heading-text-transform {
  text-transform: var(--heading-text-transform);
}
.label-text-transform {
  text-transform: var(--label-text-transform);
}
.media-grid {
  --grid-gap: var(--block-spacing);

  display: grid;
  grid-gap: var(--grid-gap);
  grid-auto-rows: minmax(var(--media-grid-row-height), auto);
  grid-auto-columns: 1fr;
}
.media-grid__element:nth-child(1) {
  grid-area: a;
}
.media-grid__element:nth-child(2) {
  grid-area: b;
}
.media-grid__element:nth-child(3) {
  grid-area: c;
}
.media-grid__element:nth-child(4) {
  grid-area: d;
}
.media-grid__element:nth-child(5) {
  grid-area: e;
}
.media-grid--stack .media-grid__element {
  grid-area: auto;
}
.media-grid--layout-1-1,
.media-grid--layout-1-2,
.media-grid--layout-1-3,
.media-grid--layout-1-4 {
  grid-template-areas: "a";
}
.media-grid--layout-2-1 {
  grid-template-areas: "a b";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-2-1 {
    grid-template-areas: "a" "b";
  }
}
.media-grid--layout-2-2 {
  grid-template-areas: "a a b";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-2-2 {
    grid-template-areas: "a" "b";
  }
}
.media-grid--layout-2-3 {
  grid-template-areas: "a a a b";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-2-3 {
    grid-template-areas: "a" "b";
  }
}
.media-grid--layout-2-4 {
  grid-template-areas: "a b b";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-2-4 {
    grid-template-areas: "a" "b";
  }
}
.media-grid--layout-3-1 {
  grid-template-areas: "a b c";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-3-1 {
    grid-template-areas: "a" "b" "c";
  }
}
.media-grid--layout-3-2 {
  grid-template-areas: "a a b c";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-3-2 {
    grid-template-areas: "a" "b" "c";
  }
}
.media-grid--layout-3-3 {
  grid-template-areas: "a b" "a c";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-3-3 {
    grid-template-areas: "a" "b" "c";
  }
}
.media-grid--layout-3-4 {
  grid-template-areas: "a a" "b c";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-3-4 {
    grid-template-areas: "a" "b" "c";
  }
}
.media-grid--layout-4-1 {
  grid-template-areas: "a b c d";
}
@media not all and (min-width: 1200px) {
  .media-grid--layout-4-1 {
    grid-template-areas: "a b" "c d";
  }
}
@media not all and (min-width: 992px) {
  .media-grid--layout-4-1 {
    grid-template-areas: "a" "b" "c" "d";
  }
}
.media-grid--layout-4-2 {
  grid-template-areas: "a b" "c d";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-4-2 {
    grid-template-areas: "a" "b" "c" "d";
  }
}
.media-grid--layout-4-3 {
  grid-template-areas: "a a a" "b c d";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-4-3 {
    grid-template-areas: "a" "b" "c" "d";
  }
}
.media-grid--layout-4-4 {
  grid-template-areas: "a a b" "c d d";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-4-4 {
    grid-template-areas: "a" "b" "c" "d";
  }
}
.media-grid--layout-5-1 {
  grid-template-areas: "a a a b b b" "c c d d e e";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-5-1 {
    grid-template-areas: "a" "b" "c" "d" "e";
  }
}
.media-grid--layout-5-2 {
  grid-auto-rows: minmax(calc(var(--media-grid-row-height) / 3), auto);
  grid-template-areas: "a c" "a c" "a d" "b d" "b e" "b e";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-5-2 {
    grid-auto-rows: minmax(var(--media-grid-row-height), auto);
    grid-template-areas: "a" "b" "c" "d" "e";
  }
}
.media-grid--layout-5-3 {
  grid-auto-rows: minmax(calc(var(--media-grid-row-height) / 3), auto);
  grid-template-areas: "a a c" "a a c" "a a d" "b b d" "b b e" "b b e";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-5-3 {
    grid-auto-rows: minmax(var(--media-grid-row-height), auto);
    grid-template-areas: "a" "b" "c" "d" "e";
  }
}
.media-grid--layout-5-4 {
  grid-template-areas: "a a a a b b" "c c d d e e";
}
@media not all and (min-width: 992px) {
  .media-grid--layout-5-4 {
    grid-template-areas: "a" "b" "c" "d" "e";
  }
}
.media-grid__element-content .prose {
  margin-top: 1rem;
}
.media-grid__element-content .button-wrapper {
  margin-top: 2rem;
}
.form-floating {
  position: relative;
  text-align: left;
}
@media not all and (min-width: 768px) {
  .form-floating {
    font-size: var(--size-text-sm);
  }
}
.form-floating > .input {
  padding: 1.75em 2.5em 0.75em 0.875em;
}
.form-floating > .input::placeholder {
  color: transparent;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1.25em 0.875em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  color: rgb(var(--color-foreground));
  transform-origin: 0 0;
  transition: 100ms all;
  line-height: 1.25em;
  opacity: 0.66;
}
.form-floating > .input:focus ~ label,
.form-floating > .input:not(:placeholder-shown) ~ label,
.form-floating > .input:-webkit-autofill ~ label {
  transform: scale(0.85) translateY(-0.5em) translateX(0.15em);
}
.section {
  --section-bg-number-actual: var(--section-bg-number);
  --section-bg-number-diff: calc(
    var(--section-bg-number-actual) - var(--previous-section-bg-number, -1)
  );
  --section-bg-number-diff-abs: max(
    var(--section-bg-number-diff),
    -1 * var(--section-bg-number-diff)
  );
  --section-has-diff-bg: clamp(0, var(--section-bg-number-diff-abs), 1);

  --section-first-pt-max: 3rem;
}
@media not all and (min-width: 768px) {
  .section {
    --section-first-pt-max: 2rem;
  }
}
.section {
  background: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
}
.section--first-pt-0 {
  --section-first-pt-max: 0px;
}
.section--full-width {
  padding-top: min(
    calc(var(--section-vertical-spacing) * var(--section-has-diff-bg)),
    var(--section-pt-max, 9999px)
  );
  padding-bottom: calc(var(--section-vertical-spacing));
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);

  --container-inner-width: calc(
    min(
      100vw - var(--container-padding) * 2 - var(--scrollbar-width),
      var(--container-max-inner-width)
    )
  );

  --container-outer-width: calc(
    50vw - var(--container-inner-width) / 2 - var(--scrollbar-width) / 2
  );
}
main > .shopify-section:first-child .section--full-width:not(.section--no-padding) {
  padding-top: min(
    max(
      calc(var(--section-vertical-spacing) * var(--section-has-diff-bg)),
      var(--section-first-pt-max)
    ),
    var(--section-pt-max, 9999px)
  );
}
.section--block {
  --section-block-padding: min(4rem, calc(1.75rem + 3vw));
  padding: var(--section-block-padding);
  margin-left: var(--container-outer-width);
  margin-right: var(--container-outer-width);
  margin-top: calc(var(--section-vertical-spacing) * var(--section-has-diff-bg));
  margin-bottom: calc(var(--section-vertical-spacing));
}
@media (min-width: 576px) {
  .section--block {
    border-radius: var(--block-corner-radius);
    --section-bg-number-actual: var(--main-bg-number);
    --section-block-outer-margin: var(--container-padding-const);
    --container-padding: var(--section-block-padding);
    --container-outer-width: calc(
      max(
        50vw - var(--container-inner-width) / 2 - var(--scrollbar-width) / 2 -
          var(--container-padding),
        var(--section-block-outer-margin)
      )
    );

    --container-inner-width: calc(
      min(
        100vw - var(--container-padding) * 2 - var(--scrollbar-width),
        var(--container-max-inner-width) - var(--container-padding) * 2
      )
    );

    --bleed-distance: var(--container-padding);
  }
}
@media not all and (min-width: 576px) {
  .section--block {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: calc(var(--section-vertical-spacing) * var(--section-has-diff-bg));
    padding-bottom: calc(var(--section-vertical-spacing));
    padding-left: var(--container-outer-width);
    padding-right: var(--container-outer-width);
  }
}
.section--space-after {
  margin-bottom: var(--section-vertical-spacing);
}
@media (min-width: 576px) {
  .shopify-section:first-child .section--block {
    margin-top: 1.5rem;
  }
  .shopify-section:last-child .section--block {
    margin-bottom: min(3rem, calc(1.5rem + 2vw));
  }
}
.section--no-padding.section--block {
  padding: 0;
}
.section--no-padding.section--full-width {
  padding-top: 0;
  padding-bottom: 0;
}
.loading-target {
  transition: opacity 150ms;
}
.shopify-section.loading .loading-target {
  opacity: 0.5;
}
.section-content-spacing .subheading {
  margin-bottom: 0.5rem;
}
.section-content-spacing .media {
  margin-bottom: 1.5rem;
}
.section-content-spacing :where(.h0, .h1) {
  margin-bottom: 1rem;
}
@media (min-width: 768px) {
  .section-content-spacing :where(.h0, .h1) {
    margin-bottom: 1.5rem;
  }
}
.section-content-spacing :where(.h2, .h3, .h4) {
  margin-bottom: 1rem;
}
.section-content-spacing :where(.h5, .h6) {
  margin-bottom: 0.5rem;
}
.section-content-spacing .button-wrapper {
  margin-top: 2.5rem;
}
.section-content-spacing > :first-child {
  margin-top: 0;
}
.section-content-spacing > :last-child {
  margin-bottom: 0;
}
.section-header--with-link {
  display: flex;
}
.section-header--with-link .filler-left,
.section-header--with-link .filler-right {
  flex-basis: 0;
  flex-grow: 1;
}
.section-header--with-link .filler-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.section-header--with-link.text-left .filler-left {
  display: none;
}
@media not all and (min-width: 576px) {
  .section-header--with-link.text-center {
    text-align: left;
  }

  .section-header--with-link.text-center .filler-left {
    display: none;
  }
}
.button-group {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.button-group-inner {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  column-gap: 0.75rem;
  row-gap: 0.5rem;
}
.mobile-menu {
  --mobile-menu-header-height: 4rem;
}
.modal-mobile-menu--bottom::part(panel) {
  min-width: 28rem;
}
@media not all and (min-width: 768px) {
  .modal-mobile-menu--bottom::part(panel) {
    min-width: 0;
    width: 100%;
    max-width: none;
    flex-grow: 1;
  }
}
.modal-mobile-menu--bottom::part(panel) {
  overflow: hidden;
}
.modal-mobile-menu--bottom::part(panel-wrapper) {
  align-items: stretch;
  justify-content: flex-start;
}
@media not all and (min-width: 768px) {
  .modal-mobile-menu--bottom::part(panel-wrapper) {
    align-items: flex-end;
    justify-content: stretch;
  }
}
.modal-mobile-menu--bottom .mobile-menu {
  height: 100%;
}
@media not all and (min-width: 768px) {
  .modal-mobile-menu--bottom .mobile-menu {
    height: max(580px, 60vh);
    max-height: 90vh;
    max-height: 90svh;
  }
}
.modal-mobile-menu--left::part(panel) {
  overflow: hidden;
  border-radius: 0;
  width: 85vw;
  max-width: 400px;
}
.modal-mobile-menu--left .mobile-menu {
  height: 100%;
}
.mobile-menu-inner.anim-in {
  scrollbar-width: none;
}
.mobile-menu-inner.anim-in::-webkit-scrollbar {
  display: none;
}
.mobile-menu-promo-images {
  --grid-columns: 1.33;
  --grid-gap-min: 1.5rem;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
@media (min-width: 768px) {
  .mobile-menu-promo-images {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}
.predictive-search mark {
  background: none;
  color: inherit;
  font-weight: var(--font-body-weight-bold);
}
.predictive-search .product-card .product-name {
  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .predictive-search .product-card .product-name {
    font-size: var(--size-text-base);
  }
}
.predictive-search .product-card .price {
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .predictive-search .product-card .price {
    font-size: var(--size-text-base);
  }
}
.predictive-search .collection-block__label {
  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .predictive-search .collection-block__label {
    font-size: var(--size-text-base);
  }
}
.predictive-search .article-card__title {
  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .predictive-search .article-card__title {
    font-size: var(--size-text-base);
  }
}
.predictive-search :where(.carousel-prev, .carousel-next) {
  --width: 3rem;
  --height: 3rem;
}
.predictive-search__carousel {
  --bleed: 3rem;
  padding-bottom: 0.5rem;
  margin-bottom: -0.5rem;
}
@media not all and (min-width: 992px) {
  .predictive-search__carousel {
    --bleed: 2rem;

    margin-left: calc(var(--bleed) * -1);
    padding-left: var(--bleed);
    scroll-padding-left: var(--bleed);
    scroll-padding-right: var(--bleed);
  }
}
@media not all and (min-width: 768px) {
  .predictive-search__carousel {
    --bleed: 1.5rem;
  }
}
.predictive-search__carousel {
  margin-right: calc(var(--bleed) * -1);
  --scroll-bleed: var(--bleed);
}
.predictive-search__carousel .grid-carousel {
  padding-right: var(--bleed);
}
.predictive-search__carousel .carousel__products {
  --grid-gap: 1.5rem;
  --grid-columns: 3;
}
@media not all and (min-width: 1400px) {
  .predictive-search__carousel .carousel__products {
    --grid-gap: 1rem;
  }
}
@media not all and (min-width: 576px) {
  .predictive-search__carousel .carousel__products {
    --grid-gap: 0.75rem;
    --grid-columns: 2.125;
  }
}
.predictive-search__carousel .carousel__collections {
  --grid-gap: 1.5rem;
  --grid-columns: 3;
}
.predictive-search__carousel .carousel__articles {
  --grid-gap: 1.5rem;
  --grid-columns: 2;
}
.rating-star {
  --letter-spacing: 0.1;
}
.product-info .rating-star {
  --letter-spacing: 0.1;
}
.product-card .rating-star {
  --letter-spacing: 0.1;
  --rating-font-size: 1;
}
@media not all and (min-width: 576px) {
  .product-card .rating-star {
    --rating-font-size: 0.875;
  }
}
.rating-star {
  --percent: calc(
    var(--rating) / var(--rating-max) * 100% +
      (17px * var(--rating-font-size) * var(--rating-decimal)) +
      (3px * var(--rating-font-size) * var(--rating) / var(--rating-max))
  );
  width: calc(101px * var(--rating-font-size));
  height: calc(16px * var(--rating-font-size));
  line-height: 1;
  margin: 0;
  background: linear-gradient(
    90deg,
    rgb(var(--color-rating-star)) var(--percent),
    rgb(var(--color-rating-star-empty) / var(--color-rating-star-empty-opacity)) var(--percent)
  );
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='101' height='16' viewBox='0 0 101 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_2503_4152)'%3E%3Cpath d='M8.4117 0L10.3974 6.11146H16.8234L11.6247 9.88854L13.6104 16L8.4117 12.2229L3.21298 16L5.19871 9.88854L0 6.11146H6.42596L8.4117 0Z' fill='white'/%3E%3Cpath d='M29.2351 0L31.2208 6.11146H37.6468L32.448 9.88854L34.4338 16L29.2351 12.2229L24.0363 16L26.0221 9.88854L20.8234 6.11146H27.2493L29.2351 0Z' fill='white'/%3E%3Cpath d='M50.0585 0L52.0442 6.11146H58.4702L53.2715 9.88854L55.2572 16L50.0585 12.2229L44.8598 16L46.8455 9.88854L41.6468 6.11146H48.0728L50.0585 0Z' fill='white'/%3E%3Cpath d='M70.8819 0L72.8676 6.11146H79.2936L74.0949 9.88854L76.0806 16L70.8819 12.2229L65.6832 16L67.6689 9.88854L62.4702 6.11146H68.8962L70.8819 0Z' fill='white'/%3E%3Cpath d='M91.7053 0L93.691 6.11146H100.117L94.9183 9.88854L96.904 16L91.7053 12.2229L86.5066 16L88.4923 9.88854L83.2936 6.11146H89.7195L91.7053 0Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_2503_4152'%3E%3Crect width='101' height='16' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
  mask-image: url("data:image/svg+xml,%3Csvg width='101' height='16' viewBox='0 0 101 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_2503_4152)'%3E%3Cpath d='M8.4117 0L10.3974 6.11146H16.8234L11.6247 9.88854L13.6104 16L8.4117 12.2229L3.21298 16L5.19871 9.88854L0 6.11146H6.42596L8.4117 0Z' fill='white'/%3E%3Cpath d='M29.2351 0L31.2208 6.11146H37.6468L32.448 9.88854L34.4338 16L29.2351 12.2229L24.0363 16L26.0221 9.88854L20.8234 6.11146H27.2493L29.2351 0Z' fill='white'/%3E%3Cpath d='M50.0585 0L52.0442 6.11146H58.4702L53.2715 9.88854L55.2572 16L50.0585 12.2229L44.8598 16L46.8455 9.88854L41.6468 6.11146H48.0728L50.0585 0Z' fill='white'/%3E%3Cpath d='M70.8819 0L72.8676 6.11146H79.2936L74.0949 9.88854L76.0806 16L70.8819 12.2229L65.6832 16L67.6689 9.88854L62.4702 6.11146H68.8962L70.8819 0Z' fill='white'/%3E%3Cpath d='M91.7053 0L93.691 6.11146H100.117L94.9183 9.88854L96.904 16L91.7053 12.2229L86.5066 16L88.4923 9.88854L83.2936 6.11146H89.7195L91.7053 0Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_2503_4152'%3E%3Crect width='101' height='16' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
  -webkit-mask-size: cover;
  mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}
.rating-text {
  margin-left: 0.5rem;
  margin-top: 1px;
  font-size: var(--size-text-xs);
  color: rgb(var(--color-foreground) / 0.75);
}
@media (min-width: 768px) {
  .rating-text {
    font-size: var(--size-text-sm);
  }
}
.qty-selector {
  display: inline-flex;
  align-items: stretch;
}
.qty-selector > input {
  text-align: center;
  margin: -2px 0.5rem;
  flex-grow: 1;
  background: transparent;
  width: 2rem;
  border-radius: var(--input-corner-radius);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: transparent;
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
@media not all and (min-width: 768px) {
  .qty-selector > input {
    font-size: var(--size-text-sm);
  }
}
@media (min-width: 768px) {
  .qty-selector > input {
    width: 2.5rem;
  }
}
.qty-selector > input:hover {
  --tw-ring-color: rgb(var(--color-foreground) / 0.3);
}
.qty-selector > input:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-input-accent) / var(--tw-ring-opacity));
  outline: 0;
}
.qty-selector__button {
  height: 1.5rem;
  width: 1.5rem;
  flex-shrink: 0;
  touch-action: manipulation;
  padding: 0px;
}
@media (min-width: 768px) {
  .qty-selector__button {
    height: 1.75rem;
    width: 1.75rem;
  }
}
.qty-selector__button > svg {
  width: 0.75rem;
  height: 0.75rem;
}
@media not all and (min-width: 768px) {
  .qty-selector__button > svg {
    width: 0.5rem;
    height: 0.5rem;
  }
}
.blog-posts-grid {
  --grid-columns: 3;

  display: grid;
  grid-template-columns: repeat(
    min(var(--grid-columns), var(--grid-columns-max, 9999)),
    minmax(0, 1fr)
  );
  gap: 4rem 2rem;
}
@media not all and (min-width: 1200px) {
  .blog-posts-grid {
    --grid-columns: 2;
  }
}
@media not all and (min-width: 768px) {
  .blog-posts-grid {
    --grid-columns: 1;
    row-gap: 2rem;
  }
}
.blog__header--full-width {
  --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

  margin-left: calc(var(--scroll-bleed) * -1);
  margin-right: calc(var(--scroll-bleed) * -1);

  padding-left: var(--scroll-bleed);
  padding-right: var(--scroll-bleed);

  scroll-padding-left: var(--scroll-bleed);
  scroll-padding-right: var(--scroll-bleed);
  padding-top: min(4rem, calc(1.75rem + 3vw));
  padding-bottom: min(2rem, calc(1.25rem + 1vw));
}
.blog__header--full-width.has-diff-bg {
  padding-bottom: min(4rem, calc(1.75rem + 3vw));
}
.blog__header--block {
  padding: min(4rem, calc(1.75rem + 3vw));
}
@media not all and (min-width: 576px) {
  .blog__header--block {
    margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
    margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  }
}
@media (min-width: 576px) {
  .blog__header--block {
    overflow: hidden;
    border-radius: var(--block-corner-radius);
  }
}
.featured-article {
}
.range {
  appearance: none;
  background: transparent;
  cursor: pointer;
}
.range:focus {
  outline: none;
}
.range::-webkit-slider-runnable-track {
  background-color: var(--range-track-color);
  border-radius: var(--range-track-height);
  height: var(--range-track-height);
}
.range::-webkit-slider-thumb {
  appearance: none;
  margin-top: -4px;
  background-color: var(--range-thumb-color);
  border-radius: var(--range-thumb-size);
  height: var(--range-thumb-size);
  width: var(--range-thumb-size);

  box-shadow: 0 0 0 2px rgb(var(--color-background));
}
.range:focus-visible::-webkit-slider-thumb {
  outline: 2px solid var(--range-thumb-color);
  outline-offset: 2px;
}
.range::-moz-range-track {
  background-color: var(--range-track-color);
  border-radius: var(--range-track-height);
  height: var(--range-track-height);
}
.range::-moz-range-thumb {
  background-color: var(--range-thumb-color);
  border: none;
  border-radius: var(--range-thumb-size);
  height: var(--range-thumb-size);
  width: var(--range-thumb-size);

  box-shadow: 0 0 0 2px rgb(var(--color-background));
}
.range:focus-visible::-moz-range-thumb {
  outline: 2px solid var(--range-thumb-color);
  outline-offset: 2px;
}
.range-group {
  position: relative;
  height: var(--range-track-height);
  border-radius: var(--range-track-height);
  background: linear-gradient(
    90deg,
    var(--range-track-color) 0%,
    var(--range-track-color) var(--range-min, 0%),
    var(--range-thumb-color) var(--range-min, 0%),
    var(--range-thumb-color) var(--range-max, 100%),
    var(--range-track-color) var(--range-max, 100%),
    var(--range-track-color) 100%
  );
}
.range-group > .range {
  --range-track-color: transparent;

  position: absolute;
  width: 100%;
  height: var(--range-track-height);
  pointer-events: none;
}
.range-group > .range::-webkit-slider-thumb {
  pointer-events: all;
}
.range-group > .range::-moz-range-thumb {
  pointer-events: all;
}
.table-styled {
  --table-x-padding: 1.5rem;
}
@media not all and (min-width: 992px) {
  .table-styled {
    --table-x-padding: 1rem;
  }
}
.table-styled th {
  background-color: rgb(var(--color-foreground) / 0.025);
  padding: 1.25rem var(--table-x-padding);
  text-transform: var(--label-text-transform);
  letter-spacing: 0.05em;
  font-size: var(--size-text-sm);
}
.table-styled th:first-child {
  border-top-left-radius: var(--block-corner-radius);
  border-bottom-left-radius: var(--block-corner-radius);
}
.table-styled th:last-child {
  border-top-right-radius: var(--block-corner-radius);
  border-bottom-right-radius: var(--block-corner-radius);
}
.table-styled td {
  padding: 1.5rem var(--table-x-padding);
  border-bottom: 1.25px solid rgb(var(--color-foreground) / 6%);
  transition: background-color 150ms;
}
.table-styled tr:last-child td {
  border-bottom: 0;
}
.status-pill {
  line-height: 1;
  padding: 0.375em 0.75em;
  font-weight: var(--weight-bold);
  font-size: var(--size-text-sm);
  border-radius: 100px;
}
.financial-status-authorized {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.financial-status-expired {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity));
}
.financial-status-paid {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}
.financial-status-partially_paid {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity));
}
.financial-status-partially_refunded {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.financial-status-pending {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity));
}
.financial-status-refunded {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.financial-status-unpaid {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}
.financial-status-voided {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity));
}
.fulfillment-status-complete {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}
.fulfillment-status-fulfilled {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}
.fulfillment-status-partial {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity));
}
.fulfillment-status-restocked {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity));
}
.fulfillment-status-unfulfilled {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity));
}
body.no-scroll .mega-menu-horizontal_inner {
  padding-right: var(--scrollbar-width);
}
.mega-menu-horizontal {
  --color-background: var(--color-modal-background);
  --color-foreground: var(--color-modal-foreground);
  --color-headings: var(--color-modal-foreground);
  color: rgb(var(--color-foreground));
}
.mega-menu-horizontal__inner {
  max-height: calc(var(--viewport-height) - var(--header-height, 0px) - 3rem);
}
.grid-cols-mega-menu-horizontal-items {
  --mega-menu-horizontal-column-width: clamp(180px, 77.7778px + 8.8889vw, 220px);

  grid-template-columns: repeat(auto-fit, minmax(var(--mega-menu-horizontal-column-width), 1fr));
}
.announcement-bar--text-xs {
  font-size: var(--size-text-xs);
}
.announcement-bar--text-sm {
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .announcement-bar--text-sm {
    font-size: var(--size-text-sm);
  }
}
.announcement-bar--text-md {
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .announcement-bar--text-md {
    font-size: var(--size-text-base);
  }
}
.announcement-bar--text-lg {
  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .announcement-bar--text-lg {
    font-size: var(--size-text-lg);
  }
}
.announcement.is-active {
  opacity: 1;
  pointer-events: all;
}
.announcement {
  letter-spacing: max(0.1em, var(--font-body-letter-spacing));
}
.announcement-bar {
  z-index: 1100;
}
.section-site-header ~ .section-announcement-bar .announcement-bar {
  z-index: 900;
}
.pswp.pswp--custom-bg {
  --pswp-bg: rgb(var(--color-background));
}
.pswp.pswp--custom-bg .pswp__button {
  width: 2.75rem;
  height: 2.75rem;
  box-shadow: 0 2px 10px rgba(0 0 0 / 15%);
  transition: transform 125ms;
  opacity: 1;
  border-radius: 9999px;
  border-width: 1.25px;
  border-style: solid;
  border-color: rgb(0 0 0 / 0.05);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
@media not all and (min-width: 768px) {
  .pswp.pswp--custom-bg .pswp__button {
    width: 2.5rem;
    height: 2.5rem;
  }
}
.pswp.pswp--custom-bg .pswp__button svg {
  width: 1.25rem;
  --icon-stroke-width: var(--icon-sm-stroke-width);
  margin: 0 auto;
}
.pswp.pswp--custom-bg .pswp__button:hover {
  transform: scale(1.0625);
}
.pswp.pswp--custom-bg .pswp__button:active {
  transform: none;
}
.pswp.pswp--custom-bg .pswp__top-bar {
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  margin-top: 1rem;
}
.pswp.pswp--custom-bg .pswp__top-bar .pswp__button {
  margin: 0;
  margin-left: 0.75rem;
}
.pswp.pswp--custom-bg.pswp--zoomed-in .pswp__button--arrow {
  opacity: 0;
  pointer-events: none;
}
.pswp.pswp--custom-bg .pswp__button--arrow svg {
  width: 1rem;
  --icon-stroke-width: var(--icon-xs-stroke-width);
}
@media (hover: none), (pointer: coarse) {
  .pswp.pswp--custom-bg .pswp__button--arrow {
    visibility: hidden;
  }
}
.pswp.pswp--custom-bg .pswp__button--arrow--prev {
  left: 1.5rem;
}
.pswp.pswp--custom-bg .pswp__button--arrow--prev svg {
  transform: scaleX(-1);
}
.pswp.pswp--custom-bg .pswp__button--arrow--next {
  right: 1.5rem;
}
.pswp.pswp--custom-bg .pswp__counter {
  display: none;
}
.pswp--zoomed-in .icon-zoom-in-v-bar {
  display: none;
}
.multicolumn {
  display: grid;
  grid-template-columns: repeat(
    min(var(--multicolumn-columns), var(--multicolumn-columns-max, 999)),
    minmax(0, 1fr)
  );
  gap: 3rem var(--multicolumn-gap);
}
@media not all and (min-width: 992px) {
  .multicolumn {
    --multicolumn-columns: var(--multicolumn-columns-md, 2);
  }
}
@media not all and (min-width: 768px) {
  .multicolumn:not(.multicolumn--swipe-on-mobile) {
    --multicolumn-columns-max: 1;
  }
}
.multicolumn--swipe-on-mobile {
  --multicolumn-mobile-swipe-cols: 1.5;
}
@media not all and (min-width: 768px) {
  .multicolumn--swipe-on-mobile {
    --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

    margin-left: calc(var(--scroll-bleed) * -1);
    margin-right: calc(var(--scroll-bleed) * -1);

    padding-left: var(--scroll-bleed);
    padding-right: var(--scroll-bleed);

    scroll-padding-left: var(--scroll-bleed);
    scroll-padding-right: var(--scroll-bleed);
    scrollbar-width: none;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    scroll-behavior: smooth;
  }

  .multicolumn--swipe-on-mobile::-webkit-scrollbar {
    display: none;
  }
  .multicolumn--swipe-on-mobile {
    grid: auto / auto-flow calc(
        100% / var(--multicolumn-mobile-swipe-cols) - var(--multicolumn-gap) /
          var(--multicolumn-mobile-swipe-cols) * (var(--multicolumn-mobile-swipe-cols) - 1)
      );
  }
}
.multicolumn__item .media + * {
  margin-top: 0;
}
.shopify-policy__container {
  max-width: 48rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
@media (min-width: 768px) {
  .shopify-policy__container {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}
.shopify-policy__title h1 {
  margin-bottom: 1.5rem;
  font-size: var(--size-text-h3);
  color: rgb(var(--color-headings));
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  text-transform: var(--heading-text-transform);
  letter-spacing: var(--heading-letter-spacing);
  overflow-wrap: anywhere;
}
.cart-modal .shopify-policy__title h1 + .cart-modal-buttons {
  margin-top: 2rem;
}
@media (min-width: 768px) {
  .cart-modal .shopify-policy__title h1 + .cart-modal-buttons {
    margin-top: 2.5rem;
  }

  .shopify-policy__title h1 {
    margin-bottom: 2rem;
  }
}
.image-hotspot {
  position: absolute;
  z-index: 10;
  display: flex;
  --tw-translate-x: -50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;

  --image-hotspot-size: clamp(1.75rem, 1.3462rem + 1.5385vw, 2.5rem);

  width: var(--image-hotspot-size);
  height: var(--image-hotspot-size);
  background: rgb(var(--hotspot-background-color, 255 255 255));
  color: rgb(var(--hotspot-icon-color, 0 0 0));

  left: var(--hotspot-x);
  top: var(--hotspot-y);
  transition: opacity 200ms;

  --shadow-size: 0.5rem;
}
@media not all and (min-width: 768px) {
  .image-hotspot {
    --shadow-size: 0.375rem;

    left: var(--hotspot-x-mobile, var(--hotspot-x));
    top: var(--hotspot-y-mobile, var(--hotspot-y));
  }
}
.image-hotspot svg {
  height: 0.625rem;
  width: 0.625rem;
}
@media (min-width: 768px) {
  .image-hotspot svg {
    height: 0.75rem;
    width: 0.75rem;
  }
}
.image-hotspot svg {
  transform: rotate(45deg);
  transition: transform 150ms;
  --icon-stroke-width: 1.875;
}
@media not all and (min-width: 768px) {
  .image-hotspot svg {
    --icon-stroke-width: 1.5;
  }
}
.image-hotspot::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgb(var(--hotspot-background-color, 255 255 255) / 25%);
  border-radius: 1000px;

  animation: hotspot-pulsate 2s infinite ease-in-out;
}
.image-hotspot-container:has(.image-hotspot.is-active) > .image-hotspot:not(.is-active) {
  opacity: 0.5;
}
:not(.fading-out) > details[open] > .image-hotspot svg {
  transform: rotate(90deg);
}
@keyframes hotspot-pulsate {
  0% {
    transform: scale(1.15);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1.15);
  }
}
:root {
  --image-comparison-progress: 50%;
}
.image-comparison {
  display: grid;
  justify-content: stretch;
  align-items: stretch;
  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
@media not all and (min-width: 576px) {
  .image-comparison {
    margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
    margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  }
}
@media (min-width: 576px) {
  .image-comparison {
    border-radius: var(--block-corner-radius);
    --tw-shadow: var(--block-shadow);
    --tw-shadow-colored: var(--block-shadow);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }
}
.image-comparison__before {
  grid-area: 1 / 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.image-comparison__after {
  grid-area: 1 / 1;
  clip-path: inset(-1px -1px -1px var(--image-comparison-progress));
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: translateZ(0);
}
.image-comparison__handle {
  --width: 2rem;
  --height: 3rem;

  position: absolute;
  left: calc(var(--image-comparison-progress) - var(--width) / 2);
  top: calc(50% - var(--height) / 2);
  width: var(--width);
  height: var(--height);
  border-radius: 100rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgb(var(--image-comparison-handle-background));
  cursor: grab;
  --tw-shadow: 0 8px 16px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.image-comparison__handle:active {
  cursor: grabbing;
}
@media not all and (min-width: 768px) {
  .image-comparison__handle {
    --width: 1.667rem;
    --height: 2.5rem;
  }
}
.image-comparison__handle::before {
  content: "";
  display: block;
  width: 12px;
  height: 16px;
  background-size: 100% 100%;
  background-image: linear-gradient(
    to right,
    rgb(var(--image-comparison-handle-foreground)) 0%,
    rgb(var(--image-comparison-handle-foreground)) 20%,
    transparent 20%,
    transparent 40%,
    rgb(var(--image-comparison-handle-foreground)) 40%,
    rgb(var(--image-comparison-handle-foreground)) 60%,
    transparent 60%,
    transparent 80%,
    rgb(var(--image-comparison-handle-foreground)) 80%,
    rgb(var(--image-comparison-handle-foreground)) 100%
  );
}
.image-comparison__bar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  left: calc(var(--image-comparison-progress) - 2px);
  background: rgb(var(--image-comparison-handle-background));
}
.promo-tile .button {
  margin-top: 1rem;
  font-size: var(--size-text-xs);
}
@media (min-width: 768px) {
  .promo-tile .button {
    margin-top: 1.5rem;
    font-size: var(--size-text-sm);
  }
}
.promo-tile .prose {
  font-size: var(--size-text-sm);
}
@media (min-width: 768px) {
  .promo-tile .prose {
    font-size: var(--size-text-base);
  }
}
.promo-tile .prose {
  margin-top: 0.5rem;
}
@media not all and (min-width: 576px) {
  .products-collection-grid--mobile-two-col li:nth-child(even) .promo-tile {
    margin-inline-end: calc(var(--container-outer-width) * -1);
  }

  .products-collection-grid--mobile-two-col li:nth-child(even) .promo-tile .media {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }

  .products-collection-grid--mobile-two-col li:nth-child(odd) .promo-tile {
    margin-inline-start: calc(var(--container-outer-width) * -1);
  }

  .products-collection-grid--mobile-two-col li:nth-child(odd) .promo-tile .media {
    border-start-start-radius: 0;
    border-end-start-radius: 0;
  }
}
.sticky-add-to-cart-wrapper {
  pointer-events: none;
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 300;
  display: flex;
}
@media (min-width: 992px) {
  .sticky-add-to-cart-wrapper {
    padding: 1.5rem;
  }
}
.sticky-add-to-cart-wrapper.--left {
  justify-content: flex-start;
}
.sticky-add-to-cart-wrapper.--center {
  justify-content: center;
}
.sticky-add-to-cart-wrapper.--right {
  justify-content: flex-end;
}
@media (min-width: 992px) {
  body.no-scroll .sticky-add-to-cart-wrapper {
    right: var(--scrollbar-width);
  }
}
sticky-add-to-cart {
  display: flex;
  width: 100%;
}
@media (min-width: 992px) {
  sticky-add-to-cart {
    max-width: 42rem;
  }
}
sticky-add-to-cart {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
@media (min-width: 992px) {
  sticky-add-to-cart {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}
sticky-add-to-cart {
  align-items: center;
  overflow: hidden;
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-background) / var(--tw-bg-opacity));
}
@media (min-width: 992px) {
  sticky-add-to-cart {
    border-radius: var(--block-corner-radius);
  }
}
sticky-add-to-cart {
  pointer-events: none;
  opacity: 0;
  box-shadow:
    0 0 8px rgba(0 0 0 / 5%),
    0 0 24px rgba(0 0 0 / 7.5%),
    0 0 48px rgba(0 0 0 / 10%);
}
sticky-add-to-cart.hide {
  animation: hideStickyAddToCart 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) forwards;
}
sticky-add-to-cart.show {
  animation: showStickyAddToCart 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  pointer-events: all;
}
@media not all and (min-width: 768px) {
  sticky-add-to-cart.show {
    animation: showStickyAddToCartMobile 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  }
}
sticky-add-to-cart .product-thumbnail {
  margin-inline-end: 1rem;
  height: 4rem;
  width: auto;
  border-radius: var(--block-sm-corner-radius);
}
@media (min-width: 768px) {
  sticky-add-to-cart .product-thumbnail {
    margin-inline-end: 1.25rem;
    height: 5rem;
  }
}
@media (min-width: 992px) {
  sticky-add-to-cart .product-thumbnail {
    margin-inline-end: 1.5rem;
    height: 6rem;
  }
}
@media not all and (min-width: 768px) {
  sticky-add-to-cart .product-title {
    display: none;
  }
}
@media (min-width: 992px) {
  sticky-add-to-cart .product-title {
    margin-inline-end: 2rem;
  }
}
sticky-add-to-cart .button {
  gap: 0.5rem;
}
@media not all and (min-width: 768px) {
  sticky-add-to-cart .button {
    flex-grow: 1;
    font-size: var(--size-text-xs);
  }
}
@media (min-width: 768px) {
  sticky-add-to-cart .button {
    margin-inline-start: auto;
  }
}
sticky-add-to-cart .button svg {
  width: 1rem;
  flex-shrink: 0;
}
@media (min-width: 768px) {
  sticky-add-to-cart .button svg {
    width: 1.25rem;
  }
}
sticky-add-to-cart quick-add-button {
  display: contents;
}
@keyframes hideStickyAddToCart {
  from {
    transform: none;
    opacity: 1;
  }
  to {
    transform: translateY(1rem);
    opacity: 0;
  }
}
@keyframes showStickyAddToCart {
  from {
    transform: translateY(3rem);
    opacity: 0;
  }
  to {
    transform: none;
    opacity: 1;
  }
}
@keyframes showStickyAddToCartMobile {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
.dropdown-filters {
  padding: 1.5rem;
}
.button-filter {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-family: var(--font-body-family);
  text-transform: none;
}
@media (min-width: 768px) {
  .button-filter {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}
.button-filter {
  letter-spacing: var(--font-body-letter-spacing);
}
.filter-label-count {
  font-size: var(--size-text-sm);
  opacity: 0.5;
  margin-left: 0.5rem;
}
.active-facets__button {
  background: rgb(var(--color-active-filter-pill));
  color: rgb(var(--color-active-filter-pill-foreground));
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 10000px;
  padding: 0.571428571em 1.14285714em;
  overflow-wrap: anywhere;
  font-weight: var(--font-body-weight-bold);
}
.active-facets__button svg {
  width: 0.75em;
  height: 0.75em;

  --stroke-width: 1.5px;
}
.active-facets__button-remove {
  text-transform: var(--label-text-transform);
}
.collection-grid {
  display: grid;
  --collection-grid-sidebar-width-actual: 0px;
}
.collection-grid.horizontal-filters {
  grid-template-columns: 1fr auto auto;
  grid-template-areas: "facets         results-count sort" "facets-active  facets-active facets-active" "products       products      products";
}
.collection-grid.horizontal-filters .collection-grid__facets {
  margin-inline-end: 1rem;
}
.collection-grid.horizontal-filters .collection-grid__sort {
  align-self: end;
  margin-bottom: 0.75rem;
  line-height: 1.25rem;
}
@media not all and (min-width: 992px) {
  .collection-grid.horizontal-filters .collection-grid__sort {
    align-self: center;
    margin-bottom: 0;
  }
}
.collection-grid.horizontal-filters .collection-grid__results-count {
  margin-inline-end: 2rem;
  align-self: end;
  margin-bottom: 0.75rem;
  line-height: 1.25rem;
}
@media not all and (min-width: 992px) {
  .collection-grid.horizontal-filters .collection-grid__results-count {
    display: none;
  }
}
.collection-grid.horizontal-filters .active-facets.active-facets--not-empty {
  margin-top: 1.5rem;
}
@media not all and (min-width: 992px) {
  .collection-grid.horizontal-filters .active-facets.active-facets--not-empty {
    margin-top: 1rem;
  }
}
.collection-grid.desktop-drawer {
  grid-template-columns: auto 1fr auto auto;
  grid-template-areas: "facets   facets-active results-count sort" "products products      products      products";
}
@media not all and (min-width: 992px) {
  .collection-grid.desktop-drawer {
    grid-template-columns: 1fr auto auto;
    grid-template-areas: "facets         results-count sort" "facets-active  facets-active facets-active" "products       products      products";
  }
}
.collection-grid.desktop-drawer .collection-grid__facets {
  margin-inline-end: 1rem;
}
.collection-grid.desktop-drawer .collection-grid__sort {
  align-self: start;
  margin-top: 0.5rem;
  line-height: 1.25rem;
}
.collection-grid.desktop-drawer .collection-grid__results-count {
  margin-top: 0.5rem;
  margin-inline-end: 2rem;
  align-self: start;
  line-height: 1.25rem;
}
@media not all and (min-width: 992px) {
  .collection-grid.desktop-drawer .collection-grid__results-count {
    display: none;
  }
}
.collection-grid.desktop-drawer .collection-grid__facets-active {
  margin-inline-start: 0.5rem;
  align-self: center;
}
@media not all and (min-width: 992px) {
  .collection-grid.desktop-drawer .collection-grid__facets-active {
    margin-inline-start: 0;
  }
  .collection-grid.desktop-drawer .active-facets.active-facets--not-empty {
    margin-top: 1rem;
  }
}
.collection-grid.vertical-filters {
  --collection-grid-sidebar-width: 260px;
  --collection-grid-sidebar-spacing: 4rem;
  --collection-grid-sidebar-width-actual: calc(
    var(--collection-grid-sidebar-width) + var(--collection-grid-sidebar-spacing)
  );

  grid-template-columns: calc(
      var(--collection-grid-sidebar-width) + var(--collection-grid-sidebar-spacing)
    ) 1fr auto;

  grid-template-rows: auto auto 1fr;

  grid-template-areas: "results-count  facets-active sort" "facets         facets-active sort" "facets         products      products";
}
@media not all and (min-width: 992px) {
  .collection-grid.vertical-filters {
    --collection-grid-sidebar-width-actual: 0px;
    grid-template-columns: 1fr auto auto;

    grid-template-areas: "facets         results-count sort" "facets-active  facets-active facets-active" "products       products      products";
  }
}
.collection-grid.vertical-filters .collection-grid__facets {
  margin-top: 1.5rem;
  margin-inline-end: var(--collection-grid-sidebar-spacing);
}
@media not all and (min-width: 992px) {
  .collection-grid.vertical-filters .collection-grid__facets {
    margin: 0;
  }
}
.collection-grid.vertical-filters .collection-grid__results-count {
  margin-inline-end: 2rem;
  align-self: end;
}
@media not all and (min-width: 992px) {
  .collection-grid.vertical-filters .collection-grid__results-count {
    display: none;
  }
}
.collection-grid.vertical-filters .active-facets.active-facets--not-empty {
  margin-top: -0.375rem;
}
@media not all and (min-width: 992px) {
  .collection-grid.vertical-filters .active-facets.active-facets--not-empty {
    margin-top: 1rem;
  }
}
.collection-grid__facets {
  grid-area: facets;
}
.collection-grid__facets-active {
  grid-area: facets-active;
  font-size: var(--size-text-sm);
}
@media not all and (min-width: 768px) {
  .collection-grid__facets-active {
    font-size: var(--size-text-xs);
  }
}
.collection-grid__sort {
  grid-area: sort;
  align-self: center;
}
.collection-grid__results-count {
  grid-area: results-count;
}
.collection-grid__products {
  grid-area: products;
  margin-top: 1.5rem;
}
@media not all and (min-width: 768px) {
  .collection-grid__products {
    margin-top: 1rem;
  }
}
.products-collection-grid {
  transition: opacity 150ms;

  --collection-grid-width: calc(
    var(--container-inner-width) - var(--collection-grid-sidebar-width-actual)
  );
  --collection-grid-column-gap: var(--block-spacing);
  --collection-grid-min-target-size: 240px;
  --collection-grid-max-target-size: calc(
    (
        var(--collection-grid-width) - (var(--collection-grid-max-columns) - 1) *
          var(--collection-grid-column-gap)
      ) / var(--collection-grid-max-columns) - 20px
  );

  --collection-grid-target-size: max(
    var(--collection-grid-min-target-size),
    var(--collection-grid-max-target-size)
  );

  display: grid;
  gap: 3rem var(--collection-grid-column-gap);

  grid-template-columns: repeat(auto-fill, minmax(var(--collection-grid-target-size), 1fr));
}
@media not all and (min-width: 768px) {
  .products-collection-grid {
    row-gap: 2.5rem;
  }
}
@media not all and (min-width: 576px) {
  .products-collection-grid {
    row-gap: 2rem;
  }
}
.products-collection-grid.product-card-container--diff-bg {
  row-gap: var(--collection-grid-column-gap);
}
@media not all and (min-width: 576px) {
  .products-collection-grid--mobile-one-col {
    --block-spacing: 1.5rem;
    grid-template-columns: 1fr;
  }
  .products-collection-grid--mobile-two-col {
    --collection-grid-column-gap: 0.75rem;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .products-collection-grid--mobile-two-col.product-card-container--diff-bg {
    --collection-grid-column-gap: 0.5rem;
    margin: 0 calc(var(--container-padding) * -1 + 0.5rem);
  }
}
.shopify-section.loading.loading .products-collection-grid {
  opacity: 0.5;
}
.shopify-section.loading.loading .pagination {
  pointer-events: none;
}
.product-card-container--diff-bg .product-card .media {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.product-card-container--diff-bg .product-card__info {
  padding: 1rem;
  padding-top: 1.5rem;
}
@media (min-width: 768px) {
  .product-card-container--diff-bg .product-card__info {
    padding: 1.5rem;
  }
}
.product-card-container--diff-bg .complementary-product {
  border: 0;
}
.image-filter-list {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 0.5rem;
}
@media (min-width: 992px) {
  .image-filter-list {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
.image-filter-list label {
  display: grid;
  height: 100%;
  cursor: pointer;
  grid-template-rows: 1fr auto;
  gap: 0.5rem;
  border-radius: var(--block-sm-corner-radius);
  padding: 0.25rem;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(var(--color-foreground) / 0.15);
}
.image-filter-list label:hover {
  --tw-ring-color: rgb(var(--color-foreground) / 0.25);
}
.image-filter-list input:checked + label {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
}
@supports (grid-template-rows: subgrid) {
  .image-filter-list > li {
    grid-row: span 2 / span 2;
    display: grid;
    grid-template-rows: subgrid;
  }

  .image-filter-list label {
    grid-row: span 2 / span 2;
    grid-template-rows: subgrid;
  }
}
.product-grid {
  --product-grid-gap-x: 5rem;
  --product-grid-py: 3rem;
}
@media (min-width: 1200px) {
  .product-grid.has-gallery-diff-bg {
    --product-grid-gap-x: 4rem;
  }
}
@media (min-width: 992px) {
  .product-grid.has-product-info-diff-bg {
    --product-grid-gap-x: 2rem;
  }
}
.product-grid {
  display: grid;
  align-items: start;
  grid-template-columns: minmax(
      auto,
      calc(var(--product-media-column-width) - var(--product-grid-gap-x) / 2)
    ) minmax(360px, 1fr);

  grid-template-rows: auto 1fr;
  column-gap: var(--product-grid-gap-x);
}
@media not all and (min-width: 1200px) {
  .product-grid {
    --product-grid-gap-x: 3rem;
  }
}
@media not all and (min-width: 992px) {
  .product-grid {
    display: block;
  }
}
@media (min-width: 992px) {
  .product-grid .product-info {
    grid-row: span 2;
  }
}
.product-grid .shopify-section:not(.product-grid-section) {
  grid-column: 1 / -1;
  display: block !important;
}
.product-grid-section {
  display: contents;
}
.product-media {
  --thumbnails-button-size: 2.5rem;
  --product-media-max-height: calc(var(--viewport-height) - var(--header-height) - 4rem);
}
@media not all and (min-width: 992px) {
  .product-media {
    padding-bottom: 1.5rem;
  }
}
.product-info .input {
  line-height: 1.5em;
}
.product-info .product-info-heading {
  font-size: var(--size-text-base);
}
@media (min-width: 768px) {
  .product-info .product-info-heading {
    font-size: var(--size-text-lg);
  }
}
.shopify-section:first-child .product-media {
  padding-top: 3rem;
}
@media not all and (min-width: 992px) {
  .shopify-section:first-child .product-media {
    padding-top: 2rem;
  }
}
@media not all and (min-width: 768px) {
  .shopify-section:first-child .product-media {
    padding-top: 1.5rem;
  }
}
@media (min-width: 992px) {
  .shopify-section:first-child .product-info {
    padding-top: 3rem;
  }
}
.product-extra .product-info-heading {
  font-size: var(--size-text-lg);
}
@media (min-width: 768px) {
  .product-extra .product-info-heading {
    font-size: var(--size-text-h5);
  }
}
@media (min-width: 992px) {
  .shopify-section:first-child .product-extra {
    margin-top: 3rem;
  }
}
main.product-grid .product-media {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
@media (min-width: 992px) {
  main.product-grid .product-media {
    margin-bottom: 3rem;
    padding-inline-end: 0px;
  }
}
main.product-grid .product-media {
  padding-top: calc(3rem * var(--section-has-diff-bg));
}
main.product-grid .product-info {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
@media (min-width: 992px) {
  main.product-grid .product-info {
    padding-inline-start: 0px;
  }
}
@media (min-width: 992px) {
  main.product-grid .product-info {
    padding-top: calc(3rem * var(--section-has-diff-bg));
  }
}
main.product-grid .product-info {
  padding-bottom: var(--section-vertical-spacing);
}
main.product-grid .product-extra {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
  padding-top: 3rem;

  padding-bottom: var(--section-vertical-spacing);
  grid-column: 1 / -1;
}
.section-main-product + .section-main-product-details .product-extra {
  grid-column: auto;
  padding-top: 0px;
}
@media (min-width: 992px) {
  .section-main-product + .section-main-product-details .product-extra {
    padding-inline-end: 0px;
  }
}
@media not all and (min-width: 992px) {
  .section-main-product + .section-main-product-details .product-extra {
    margin-top: min(0px, calc(3rem - var(--section-vertical-spacing)));
  }
}
@media not all and (min-width: 768px) {
  .section-main-product + .section-main-product-details .product-extra {
    margin-top: min(0px, calc(2rem - var(--section-vertical-spacing)));
  }
}
.product-gallery-wrapper {
  grid-area: gallery;
}
@media (min-width: 992px) {
  .product-gallery-wrapper {
    overflow: hidden;
  }
}
.product-gallery {
  display: grid;
  grid: auto / auto-flow 100%;
  align-items: start;
  transition: max-height 200ms;
  column-gap: 2rem;
  border-radius: var(--block-corner-radius);
}
@media (min-width: 768px) {
  .product-gallery {
    column-gap: 4rem;
  }
}
.product-gallery ~ .carousel-prev {
  inset-inline-start: calc(var(--width) / -2);
}
.product-gallery ~ .carousel-next {
  inset-inline-end: calc(var(--width) / -2);
}
.product-gallery__slide {
  position: relative;
  display: flex;
  justify-content: center;

  scroll-snap-align: start;
  scroll-snap-stop: always;
}
.product-media-item {
  --ratio-percent: calc(1 / var(--ratio, 1) * 100%);

  width: 100%;
  position: relative;
  overflow: hidden;
  padding-top: var(--ratio-percent);
  border-radius: var(--block-corner-radius);
}
.product-media-item > .product-gallery-shade > :where(img, lqip-element) {
  border-radius: var(--block-corner-radius);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: min(100%, calc(100% * (var(--real-ratio) / var(--ratio))));
  height: min(100%, calc(100% / (var(--real-ratio) / var(--ratio))));
}
.product-media-item > :where(video, iframe) {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.product-media-item:hover .button-zoom-in {
  transform: scale(1);
  transition-delay: 0ms;
}
.product-media--fit-height .product-media-item {
  --constrained-height: var(--product-media-max-height);
  --contained-width: calc(var(--constrained-height) * var(--ratio, 1));

  width: min(100%, calc(var(--contained-width)));
  padding-top: min(var(--constrained-height), var(--ratio-percent));
}
.product-gallery__loader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgb(var(--color-background));
  pointer-events: none;
  opacity: 0;
  transition: opacity 133ms;
}
.product-gallery__loader .spinner {
  width: 4rem;
}
@media not all and (min-width: 768px) {
  .product-gallery__loader .spinner {
    width: 3rem;
  }
}
.js .product-media-item:not([data-loaded="true"]) .product-gallery__loader {
  opacity: 1;
}
.product-gallery__slide:first-child .product-gallery__loader {
  display: none;
}
.product-thumbnail {
  position: relative;
}
.product-thumbnail .media {
  border-radius: var(--block-xs-corner-radius);
  background-color: rgb(var(--color-background));
  max-width: var(--thumbnail-size);
}
.product-thumbnail img {
  pointer-events: none;
}
.product-media-thumbnails--active-ring .product-thumbnail .media {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
@media (hover: hover) and (pointer: fine) {
  .product-media-thumbnails--active-ring .product-thumbnail:hover:not(.active) .media {
    opacity: 0.75;
  }
}
.product-media-thumbnails--active-ring .product-thumbnail .media::after {
  content: "";
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  display: block;
  border-radius: var(--block-xs-corner-radius);
  --tw-ring-inset: inset;
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.product-media-thumbnails--active-ring .product-thumbnail.active .media::after {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
}
.product-media-thumbnails--active-underline .product-thumbnail.active::before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  bottom: -6px;
  background-color: rgb(var(--color-base-accent));
}
.product-media-carousel {
  --thumbnail-size: 5rem;

  display: grid;
  grid-template-areas: "gallery";
  -webkit-user-select: none;
  user-select: none;
  position: relative;
}
.product-media-carousel .product-gallery-mobile-indicator {
  grid-area: mobile-indicator;
  display: none;
}
@media not all and (min-width: 992px) {
  .product-media-carousel {
    grid-template-areas: "gallery" "mobile-indicator";
  }
}
.product-media-carousel.thumbs-vertical {
  grid-template-areas: "thumbnails gallery";
  grid-template-columns: var(--thumbnail-size) 1fr;
  gap: 1.5rem;
  align-items: start;
}
@media not all and (min-width: 1200px) {
  .product-media-carousel.thumbs-vertical {
    --thumbnail-size: 4rem;
  }
}
.product-media-carousel.thumbs-vertical .product-media-thumbnails-wrapper .button-prev {
  top: calc(var(--thumbnails-button-size) / -2);
  left: calc(50% - var(--thumbnails-button-size) / 2);
}
.product-media-carousel.thumbs-vertical .product-media-thumbnails-wrapper .button-next {
  bottom: calc(var(--thumbnails-button-size) / -2);
  left: calc(50% - var(--thumbnails-button-size) / 2);
}
.product-media-carousel.thumbs-vertical .product-media-thumbnails-wrapper .button-prev svg {
  transform: rotate(-90deg);
}
.product-media-carousel.thumbs-vertical .product-media-thumbnails-wrapper .button-next svg {
  transform: rotate(90deg);
}
.product-media-carousel.thumbs-vertical .product-media-thumbnails-wrapper scroll-carousel {
  padding-bottom: 6px;
  max-height: min(45rem, var(--product-media-max-height));
}
.product-media-carousel.thumbs-horizontal {
  grid-template-areas: "gallery" "thumbnails";
  grid-template-rows: auto var(--thumbnail-size);
  gap: 3rem;
}
@media (min-width: 992px) {
  .product-media-carousel.thumbs-horizontal {
    --product-media-max-height: min(
      768px,
      calc(var(--viewport-height) - var(--header-height) - 4rem - var(--thumbnail-size))
    );
  }
}
.product-media-carousel.thumbs-horizontal .product-media-thumbnails-wrapper .button-prev {
  left: 0;
  top: calc(50% - var(--thumbnails-button-size) / 2);
}
@media not all and (min-width: 1200px) {
  .product-media-carousel.thumbs-horizontal .product-media-thumbnails-wrapper .button-prev {
    left: 0;
  }
}
.product-media-carousel.thumbs-horizontal .product-media-thumbnails-wrapper .button-next {
  right: 0;
  top: calc(50% - var(--thumbnails-button-size) / 2);
}
@media not all and (min-width: 1200px) {
  .product-media-carousel.thumbs-horizontal .product-media-thumbnails-wrapper .button-next {
    right: 0;
  }
}
.product-media-carousel.thumbs-horizontal .product-media-thumbnails-wrapper .button-prev svg {
  transform: scaleX(-1);
}
@media not all and (min-width: 992px) {
  .product-media-carousel.thumbs-horizontal,
  .product-media-carousel.thumbs-vertical {
    gap: 0;
    grid-template-columns: auto;
    grid-template-rows: auto;
    grid-template-areas: "gallery" "mobile-indicator";
  }

  .product-media-carousel.thumbs-horizontal .product-gallery-mobile-indicator,
  .product-media-carousel.thumbs-vertical .product-gallery-mobile-indicator {
    display: grid;
  }
}
.product-media-thumbnails {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media not all and (min-width: 992px) {
  .product-media-thumbnails {
    --thumbnail-size: 4rem;
    gap: 0.5rem;
  }
}
.product-media-thumbnails > * {
  flex-shrink: 0;
}
.product-media-thumbnails .play-icon {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
}
.product-media-thumbnails .play-icon svg {
  width: 1.25rem;
  height: 1.25rem;
  filter: none;
}
.product-media-thumbnails.product-media-thumbnails--horizontal {
  display: grid;
  grid: auto / auto-flow max-content;
}
.product-media-thumbnails.product-media-thumbnails--horizontal .media {
  height: var(--thumbnail-size);
}
.product-media-thumbnails.product-media-thumbnails--horizontal
  .media
  :where(img, svg, lqip-element) {
  width: auto;
  height: 100%;
}
.product-media-thumbnails.product-media-thumbanils--vertical {
  grid-auto-flow: row;
  grid-auto-rows: min-content;
}
.product-media-thumbnails.product-media-thumbanils--vertical .media {
  width: var(--thumbnail-size);
}
.product-media-thumbnails-wrapper {
  grid-area: thumbnails;
  display: grid;
  position: relative;
}
@media not all and (min-width: 992px) {
  .product-media-thumbnails-wrapper.thumbnails-wrapper--desktop {
    display: none;
  }
}
.product-media-thumbnails-wrapper .button-prev,
.product-media-thumbnails-wrapper .button-next {
  position: absolute;
  display: none;
  align-items: center;
  justify-content: center;
  width: var(--thumbnails-button-size);
  height: var(--thumbnails-button-size);
  border-radius: 100px;
  border-width: 1.25px;
  border-color: rgb(var(--color-foreground) / var(--tw-border-opacity));
  --tw-border-opacity: 0.05;
  --tw-shadow: 0 0 8px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 0 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
  background: rgb(var(--white));
  color: rgb(var(--black));
  z-index: 100;
  transition: transform 200ms;
}
.product-media-thumbnails-wrapper .button-prev svg,
.product-media-thumbnails-wrapper .button-next svg {
  width: 1rem;
  height: 1rem;
}
.product-media-thumbnails-wrapper .button-prev:hover,
.product-media-thumbnails-wrapper .button-next:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}
.product-media-thumbnails-wrapper .button-prev.is-hidden,
.product-media-thumbnails-wrapper .button-next.is-hidden {
  transform: scale(0);
  pointer-events: none;
}
@media (hover: hover) and (pointer: fine) {
  .product-media-thumbnails-wrapper .button-prev,
  .product-media-thumbnails-wrapper .button-next {
    display: flex;
  }
}
.product-media--lightbox [data-media-type="image"] {
  cursor: pointer;
}
.product-price {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
@media (min-width: 768px) {
  .product-price {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}
.product-price .price {
  font-size: var(--size-text-h4);
}
.product-price.bold .price {
  font-weight: var(--font-body-weight-bold);
}
.product-price .sale-price {
  color: rgb(var(--color-foreground));
}
.product-price .sale-price ~ .regular-price {
  font-weight: var(--weight-normal);
}
details.product-collapse {
  border-top-width: 1.25px;
  border-bottom-width: 1.25px;
  border-color: rgb(var(--color-foreground) / 6%);
}
details.product-collapse > summary {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  padding-right: 1rem;

  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}
@media not all and (min-width: 768px) {
  details.product-collapse > summary {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }
}
details.product-collapse > summary > .icon-chevron {
  width: 1rem;
  height: 1rem;
  transform: rotate(90deg);
  transition: transform 200ms;
}
details.product-collapse[open] > summary > .icon-chevron {
  transform: rotate(270deg);
}
details.product-collapse > summary + *::after {
  content: "";
  display: block;
  height: 1.5rem;
}
smooth-collapse.collapsing > details.product-collapse > summary > .icon-chevron {
  transform: rotate(90deg);
}
.variant-radios {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
@media (min-width: 768px) {
  .variant-radios {
    gap: 0.75rem;
  }
}
.variant-radios label {
  cursor: pointer;
}
.variant-radios input[type="radio"] {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  width: 1px;
  height: 1px;
}
.variant-radios--rounded .variant-radio label {
  border-radius: 9999px;
}
.variant-radios--outlined .variant-radio label {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-color: rgb(var(--color-foreground) / 0.1);
}
.variant-radios--outlined .variant-radio label:hover {
  --tw-ring-color: rgb(var(--color-foreground) / 0.2);
}
.variant-radios--outlined .variant-radio input[type="radio"]:checked + label {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
}
.variant-radios--outlined .variant-radio input[type="radio"]:focus-visible + label {
  outline: 2px solid rgb(var(--color-foreground));
}
.variant-radios--filled .variant-radio label {
  background-color: rgb(var(--color-foreground) / 0.05);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.variant-radios--filled .variant-radio label:hover {
  background-color: rgb(var(--color-foreground) / 0.1);
}
.variant-radios--filled .variant-radio label:active {
  background-color: rgb(var(--color-foreground) / 0.15);
}
.variant-radios--filled .variant-radio input[type="radio"]:checked + label {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-base-accent) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(var(--color-base-accent-foreground) / var(--tw-text-opacity));
}
.variant-radios--filled .variant-radio input[type="radio"]:focus-visible + label {
  outline: 2px solid rgb(var(--color-foreground));
}
.variant-radio label {
  border-radius: var(--button-corner-radius);
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
@media (min-width: 768px) {
  .variant-radio label {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}
.variant-radio input[type="radio"].option-unavailable + label {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' version='1.1' width='30' height='30'%3E%3Cpath d='M 0,30 30,0' style='stroke:%23000000;stroke-width:1'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center center;
  background-size:
    100% 100%,
    auto;
}
.variant-radio input[type="radio"].option-unavailable:not(:checked) + label {
  opacity: 0.6;
}
.variant-radio-image {
  position: relative;
}
.variant-radio-image label {
  display: block;
  cursor: pointer;
  overflow: hidden;
  border-radius: var(--block-xs-corner-radius);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(var(--color-foreground) / 0.1);
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.variant-radio-image input[type="radio"]:checked + label {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
}
.variant-radio-image input[type="radio"]:focus-visible + label {
  outline: 2px solid rgb(var(--color-foreground));
}
.variant-radio-image input[type="radio"].option-unavailable:not(:checked) + label {
  opacity: 0.4;
}
.color-swatch-selector {
  position: relative;
}
.color-swatch-selector label {
  cursor: pointer;
  outline: none !important;
}
.color-swatch-selector label .inner {
  width: 1.5rem;
  cursor: pointer;
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
@media (min-width: 768px) {
  .color-swatch-selector label .inner {
    width: 1.75rem;
  }
}
.color-swatch-selector label .inner {
  background: var(--swatch-color, var(--gray-200));
  background-size: cover;
  padding-bottom: calc(100% * var(--color-swatch-aspect-ratio));
  border-radius: var(--color-swatch-border-radius);
}
.color-swatch-selector label.has-swatch-border .inner {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(var(--color-foreground) / 0.2);
}
.color-swatch-selector label:hover .inner {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(var(--color-foreground) / 0.25);
  --tw-ring-offset-width: 3px;
}
.color-swatch-selector :where(input[type="radio"], input[type="checkbox"]):checked + label .inner {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--color-foreground) / var(--tw-ring-opacity));
  --tw-ring-offset-width: 3px;
}
.color-swatch-selector
  :where(input[type="radio"], input[type="checkbox"]):focus-visible
  + label
  .inner {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 8px;
  outline-color: currentColor;
}
.color-swatch-selector
  :where(input[type="radio"], input[type="checkbox"]).option-unavailable
  + label
  .inner {
  position: relative;
}
.color-swatch-selector
  :where(input[type="radio"], input[type="checkbox"]).option-unavailable
  + label
  .inner::before {
  content: "";
  border-radius: var(--color-swatch-border-radius);
  display: bottom;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(
    to bottom right,
    transparent calc(50% - 1.33px),
    rgb(var(--color-background)) calc(50% - 1px) calc(50% + 1px),
    transparent calc(50% + 1.33px)
  );
  background-position: center;
  background-size: cover;
}
.hide-sold-out-variants .option-unavailable,
.hide-sold-out-variants .option-unavailable + label {
  display: none;
}
.hide-sold-out-variants
  :is(.variant-radio, .variant-radio-image, .color-swatch-selector):has(.option-unavailable) {
  display: none;
}
.button-zoom-in {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 10000px;
  padding: 0;
  border: 1.25px solid rgb(var(--black) / 6%);
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 100;
  background: rgb(var(--white));
  color: rgb(var(--black));
  --tw-shadow: 0 0 8px rgb(0 0 0 / 8%);
  --tw-shadow-colored: 0 0 8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
  transition: transform 125ms ease 250ms;
  transform: scale(0);
  pointer-events: none;
}
.button-zoom-in svg {
  width: 1rem;
  --icon-stroke-width: var(--icon-xs-stroke-width);
}
@media (min-width: 768px) {
  .button-zoom-in svg {
    width: 1.25rem;
    --icon-stroke-width: var(--icon-sm-stroke-width);
  }
}
@media not all and (min-width: 768px) {
  .button-zoom-in {
    width: 2.25rem;
    height: 2.25rem;
    bottom: 0.5rem;
    right: 0.5rem;
  }
}
.product-media:not(.product-media--lightbox) .button-zoom-in {
  display: none;
}
.quick-add-modal {
}
.quick-add-modal .product-info .product-name-wrapper {
  padding-right: 1.5rem;
}
.quick-add-modal::part(panel) {
  width: 60rem;
}
@media (min-width: 768px) {
  .quick-add-modal::part(panel) {
    overflow-y: initial;
    margin: auto 0;
    max-height: none;
  }

  .quick-add-modal::part(panel-wrapper) {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
    align-items: flex-start;
  }
}
.quick-add-modal .carousel-prev {
  --width: 2.75rem;
  --height: 2.75rem;
  inset-inline-start: 1rem;
}
.quick-add-modal .carousel-next {
  --width: 2.75rem;
  --height: 2.75rem;
  inset-inline-end: 1rem;
}
.quick-add-modal .button-zoom-in {
  display: none;
}
.quick-add-modal .product-title {
  font-size: var(--size-text-h5);
  line-height: var(--line-height-h5);
}
@media (min-width: 768px) {
  .quick-add-modal .product-title {
    font-size: var(--size-text-h4);
    line-height: var(--line-height-h4);
  }
}
.quick-add-modal .product-price {
  margin-bottom: 0px;
}
@media not all and (min-width: 768px) {
  .quick-add-modal .product-price {
    margin-top: 0.5rem;
  }
}
.quick-add-modal .price {
  font-size: var(--size-text-h6);
  line-height: var(--line-height-h6);
}
@media (min-width: 768px) {
  .quick-add-modal .price {
    font-size: var(--size-text-h5);
    line-height: var(--line-height-h5);
  }
}
media-carousel[adaptive-height]:not(:defined) .product-gallery__slide:not(:first-child) {
  display: none;
}
.product-highlights-list {
  color: rgb(var(--color-foreground));
}
.product-highlights-list ul {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.product-highlights-list ul li {
  position: relative;
  padding-left: 2em;
}
.product-highlights-list ul li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.1875em;
  display: block;
  width: 1.25em;
  height: 1.25em;
  background-color: rgb(
    var(--product-highlights-icon-color, var(--color-foreground)) /
      var(--product-highlights-icon-color-opacity, 1)
  );
}
.product-highlights-list--icon-checkmark li::before {
  -webkit-mask: var(--svg-check);
  mask: var(--svg-check);
}
.product-highlights-list--icon-circle-checkmark li::before {
  border-radius: 1000px;
  background-image: var(--svg-circle-check);
}
.buy-buttons {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 0.75rem 1rem;
}
@media not all and (min-width: 768px) {
  .buy-buttons {
    gap: 0.5rem;
  }
}
.buy-buttons :where(.button, .shopify-payment-button__button--unbranded) {
  min-height: 52px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: var(--size-text-base) !important;
}
@media (min-width: 768px) {
  .buy-buttons :where(.button, .shopify-payment-button__button--unbranded) {
    min-height: 60px !important;
  }
}
.buy-buttons > * {
  flex-basis: 100%;
  flex-grow: 1;
}
.buy-buttons > *:where(.button, .shopify-payment-button) {
  flex-basis: 210px;
}
@media not all and (min-width: 576px) {
  .buy-buttons > *:where(.button, .shopify-payment-button) {
    flex-basis: 100%;
  }
}
.product-collapse-wrapper {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
@media (min-width: 768px) {
  .product-collapse-wrapper {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}
.product-collapse-wrapper + .product-collapse-wrapper {
  margin-top: -1rem;
}
@media (min-width: 768px) {
  .product-collapse-wrapper + .product-collapse-wrapper {
    margin-top: -1.5rem;
  }
}
.product-collapse-wrapper + .product-collapse-wrapper .product-collapse {
  border-top-width: 0px;
}
.product-collapse-wrapper:first-child .product-collapse {
  border-top-width: 0px;
}
.product-collapse-wrapper:last-child .product-collapse {
  border-bottom-width: 0px;
}
.product-rich-text + .product-rich-text {
  margin-top: -0.5rem;
}
@media (min-width: 768px) {
  .product-rich-text + .product-rich-text {
    margin-top: -1rem;
  }
}
.shopify-payment-button__button.shopify-payment-button__button--branded [role="button"] {
  min-height: 56px !important;
  border-radius: var(--button-corner-radius) !important;
  font-size: var(--size-text-base) !important;
}
@media (min-width: 768px) {
  .shopify-payment-button__button.shopify-payment-button__button--branded [role="button"] {
    min-height: 60px !important;
  }
}
shopify-accelerated-checkout {
  --shopify-accelerated-checkout-button-block-size: 60px;
  --shopify-accelerated-checkout-button-border-radius: var(--button-corner-radius);
}
@media not all and (min-width: 768px) {
  shopify-accelerated-checkout {
    --shopify-accelerated-checkout-button-block-size: 52px;
  }
}
.quick-add-modal .product-gallery,
.quick-add-modal .product-media-item,
.quick-add-modal .product-media-item img {
  border-radius: 0;
}
@media not all and (min-width: 768px) {
  .quick-add-modal {
  }
}
.product-sku {
  font-size: var(--size-text-xs);
  letter-spacing: max(0.05em, var(--font-body-letter-spacing));
  color: rgb(var(--color-foreground) / 0.75);
}
@media (min-width: 768px) {
  .product-sku {
    font-size: var(--size-text-sm);
  }
}
.product-custom-badges {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}
.product-custom-badges:empty {
  display: none;
}
@media (min-width: 768px) {
  .product-custom-badges {
    margin-top: 1rem;
    margin-bottom: 1rem;
    gap: 0.5rem;
  }
}
.main-cart-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}
@media not all and (min-width: 1200px) {
  .main-cart-grid {
    grid-template-columns: auto;
  }
}
@media not all and (min-width: 768px) {
  .main-cart-grid {
    gap: 3rem;
  }
}
@media not all and (min-width: 576px) {
  .main-cart-grid {
    gap: 2rem;
  }
}
.cart-items th {
  padding: 0 1rem;
  padding-bottom: 1rem;
  color: rgb(var(--color-foreground) / 0.75);
}
.cart-items th:first-child {
  padding-left: 0;
}
.cart-items th:last-child {
  padding-right: 0;
}
.cart-items td {
  vertical-align: top;
  padding: 2rem 1rem;
  border-bottom-width: 1.25px;
  border-color: rgb(var(--color-foreground) / 6%);
}
.cart-items td:first-child {
  padding-left: 0;
}
.cart-items td:last-child {
  padding-right: 0;
}
@media not all and (min-width: 576px) {
  .cart-items td {
    padding-left: 0;
    padding-right: 0;
  }
}
.cart-items tr:last-child td {
  padding-bottom: 0;
  border-bottom: 0;
}
@media not all and (min-width: 768px) {
  .cart-items tr:first-child td {
    padding-top: 0;
  }
}
.cart-side.cart-side--sticky {
  position: sticky;
  top: calc(1.5rem + var(--header-height-sticky));
  transition: top 250ms;
}
.free-shipping-indicator .progress-bar-inner {
  background-color: rgb(var(--color-free-shipping-bar));
}
.cart-modal .heading + .cart-modal-buttons {
  margin-top: 2rem;
}
@media (min-width: 768px) {
  .cart-modal .heading + .cart-modal-buttons {
    margin-top: 2.5rem;
  }
}
.shopify-payment-button__more-options {
  text-decoration: underline;
  text-underline-offset: 0.25rem;
  color: rgb(var(--color-foreground) / 75%);
}
#additional-checkout-buttons [role="button"] {
  border-radius: var(--button-corner-radius) !important;
}
shopify-accelerated-checkout-cart {
  --shopify-accelerated-checkout-button-block-size: 56px;
  --shopify-accelerated-checkout-button-border-radius: var(--button-corner-radius);
}
@media not all and (min-width: 768px) {
  shopify-accelerated-checkout-cart {
    --shopify-accelerated-checkout-button-block-size: 54px;
  }
}
.cart-side .additional-checkout-buttons {
  margin-top: 0.5rem;
}
.section-main-account.loading .orders-overview {
  opacity: 0.5;
}
#recover,
#recover + div {
  display: none;
}
#recover:target {
  display: block;
}
#recover:target + div {
  display: block;
}
#recover:target ~ #login,
#recover:target ~ #login + div {
  display: none;
}
#recover,
#login {
  scroll-margin-top: 20rem;
}
#recover {
  margin-bottom: 0;
}
.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}
.prose :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.prose :where(a):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}
.prose :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}
.prose :where(a strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}
.prose :where(ol[type="A"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="A" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}
.prose :where(ol[type="a" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}
.prose :where(ol[type="I"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="I" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: upper-roman;
}
.prose :where(ol[type="i" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: lower-roman;
}
.prose :where(ol[type="1"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: decimal;
}
.prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}
.prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}
.prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}
.prose :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}
.prose :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}
.prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-left-width: 0.25rem;
  border-left-color: var(--tw-prose-quote-borders);
  quotes: "\201C" "\201D" "\2018" "\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-left: 1em;
}
.prose
  :where(blockquote p:first-of-type):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  )::before {
  content: open-quote;
}
.prose
  :where(blockquote p:last-of-type):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  )::after {
  content: close-quote;
}
.prose :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.4;
}
.prose :where(h1 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}
.prose :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.4;
}
.prose :where(h2 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}
.prose :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.125em;
  margin-top: 1.7777778em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}
.prose :where(h3 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.prose :where(h4 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}
.prose :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow:
    0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%),
    0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-right: 0.375em;
  padding-bottom: 0.1875em;
  padding-left: 0.375em;
}
.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}
.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::before {
  content: "`";
}
.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
  content: "`";
}
.prose :where(a code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h1 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.prose :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.prose :where(h4 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(blockquote code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(thead th code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: inherit;
}
.prose :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-right: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-left: 1.1428571em;
}
.prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *))::before {
  content: none;
}
.prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
  content: none;
}
.prose :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  text-align: left;
  margin-top: 2em !important;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.prose :where(thead):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}
.prose :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}
.prose :where(tbody tr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}
.prose :where(tbody tr:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-bottom-width: 0;
}
.prose :where(tbody td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  vertical-align: baseline;
}
.prose :where(tfoot):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}
.prose :where(tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  vertical-align: top;
}
.prose :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.prose {
  --tw-prose-body: rgb(var(--color-foreground));
  --tw-prose-headings: rgb(var(--color-headings));
  --tw-prose-lead: rgb(var(--color-foreground));
  --tw-prose-links: rgb(var(--color-foreground));
  --tw-prose-bold: rgb(var(--color-foreground));
  --tw-prose-counters: rgb(var(--color-foreground));
  --tw-prose-bullets: rgb(var(--color-foreground) / 25%);
  --tw-prose-hr: rgb(var(--color-foreground) / 10%);
  --tw-prose-quotes: rgb(var(--color-foreground));
  --tw-prose-quote-borders: rgb(var(--color-foreground));
  --tw-prose-captions: rgb(var(--color-foreground));
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: rgb(var(--color-foreground));
  --tw-prose-pre-code: rgb(var(--color-foreground));
  --tw-prose-pre-bg: rgb(var(--color-foreground));
  --tw-prose-th-borders: rgb(var(--color-foreground) / 10%);
  --tw-prose-td-borders: rgb(var(--color-foreground) / 10%);
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: var(--size-text-base);
  line-height: 1.75;
}
.prose :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.375em;
}
.prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.375em;
}
.prose :where(.prose > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose
  :where(.prose > ul > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.25em;
}
.prose
  :where(.prose > ul > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.25em;
}
.prose
  :where(.prose > ol > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.25em;
}
.prose
  :where(.prose > ol > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.25em;
}
.prose
  :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.prose :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-left: 1.625em;
}
.prose :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0;
}
.prose :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-right: 0;
}
.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}
.prose
  :where(tbody td:first-child, tfoot td:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-left: 0;
}
.prose
  :where(tbody td:last-child, tfoot td:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-right: 0;
}
.prose :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.prose :where(.prose > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose :where(.prose > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose-sm {
  font-size: var(--size-text-sm);
  line-height: 1.7142857;
}
.prose-sm :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}
.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-sm :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.1111111em;
}
.prose-sm :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}
.prose-sm :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}
.prose-sm :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}
.prose-sm :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}
.prose-sm :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-sm :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-right: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-left: 0.3571429em;
}
.prose-sm :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8571429em;
}
.prose-sm :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.9em;
}
.prose-sm :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
}
.prose-sm :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-right: 1em;
  padding-bottom: 0.6666667em;
  padding-left: 1em;
}
.prose-sm :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-left: 1.5714286em;
}
.prose-sm :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-left: 1.5714286em;
}
.prose-sm :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}
.prose-sm :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.4285714em;
}
.prose-sm :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.4285714em;
}
.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}
.prose-sm
  :where(.prose-sm > ul > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.1428571em;
}
.prose-sm
  :where(.prose-sm > ul > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.1428571em;
}
.prose-sm
  :where(.prose-sm > ol > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.1428571em;
}
.prose-sm
  :where(.prose-sm > ol > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.1428571em;
}
.prose-sm
  :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}
.prose-sm :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}
.prose-sm :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}
.prose-sm :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-left: 1.5714286em;
}
.prose-sm :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}
.prose-sm :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}
.prose-sm :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-right: 1em;
  padding-bottom: 0.6666667em;
  padding-left: 1em;
}
.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0;
}
.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-right: 0;
}
.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-right: 1em;
  padding-bottom: 0.6666667em;
  padding-left: 1em;
}
.prose-sm
  :where(tbody td:first-child, tfoot td:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-left: 0;
}
.prose-sm
  :where(tbody td:last-child, tfoot td:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-right: 0;
}
.prose-sm :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}
.prose-sm :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-sm :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}
.prose-sm
  :where(.prose-sm > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-sm
  :where(.prose-sm > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-bottom: 0;
}
.prose-lg {
  font-size: var(--size-text-lg);
  line-height: 1.7777778;
}
.prose-lg :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.2222222em;
  line-height: 1.4545455;
  margin-top: 1.0909091em;
  margin-bottom: 1.0909091em;
}
.prose-lg :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  padding-left: 1em;
}
.prose-lg :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}
.prose-lg :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.3333333em;
  margin-top: 1.6666667em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}
.prose-lg :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.4;
}
.prose-lg :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}
.prose-lg :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
  border-radius: 0.3125rem;
  padding-top: 0.2222222em;
  padding-right: 0.4444444em;
  padding-bottom: 0.2222222em;
  padding-left: 0.4444444em;
}
.prose-lg :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
}
.prose-lg :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8666667em;
}
.prose-lg :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.875em;
}
.prose-lg :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.75;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.375rem;
  padding-top: 1em;
  padding-right: 1.5em;
  padding-bottom: 1em;
  padding-left: 1.5em;
}
.prose-lg :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.5555556em;
}
.prose-lg :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-left: 1.5555556em;
}
.prose-lg :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  margin-bottom: 0.6666667em;
}
.prose-lg :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.4444444em;
}
.prose-lg :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0.4444444em;
}
.prose-lg :where(.prose-lg > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg
  :where(.prose-lg > ul > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.3333333em;
}
.prose-lg
  :where(.prose-lg > ul > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.3333333em;
}
.prose-lg
  :where(.prose-lg > ol > li > *:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-top: 1.3333333em;
}
.prose-lg
  :where(.prose-lg > ol > li > *:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  margin-bottom: 1.3333333em;
}
.prose-lg
  :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.prose-lg :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
}
.prose-lg :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}
.prose-lg :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0.6666667em;
  padding-left: 1.5555556em;
}
.prose-lg :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 3.1111111em;
  margin-bottom: 3.1111111em;
}
.prose-lg :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
}
.prose-lg :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-right: 0.75em;
  padding-bottom: 0.75em;
  padding-left: 0.75em;
}
.prose-lg :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-left: 0;
}
.prose-lg :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-right: 0;
}
.prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  padding-top: 0.75em;
  padding-right: 0.75em;
  padding-bottom: 0.75em;
  padding-left: 0.75em;
}
.prose-lg
  :where(tbody td:first-child, tfoot td:first-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-left: 0;
}
.prose-lg
  :where(tbody td:last-child, tfoot td:last-child):not(
    :where([class~="not-prose"], [class~="not-prose"] *)
  ) {
  padding-right: 0;
}
.prose-lg :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 1.7777778em;
  margin-bottom: 1.7777778em;
}
.prose-lg :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.prose-lg :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  font-size: 0.8888889em;
  line-height: 1.5;
  margin-top: 1em;
}
.prose-lg
  :where(.prose-lg > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: 0;
}
.prose-lg
  :where(.prose-lg > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-bottom: 0;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.-right-2 {
  right: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-2 {
  bottom: 0.5rem;
}
.left-0 {
  left: 0px;
}
.left-\[--media-badge-offset\] {
  left: var(--media-badge-offset);
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-6 {
  right: 1.5rem;
}
.right-\[--media-badge-offset\] {
  right: var(--media-badge-offset);
}
.top-0 {
  top: 0px;
}
.top-2 {
  top: 0.5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-6 {
  top: 1.5rem;
}
.top-\[--media-badge-offset\] {
  top: var(--media-badge-offset);
}
.top-full {
  top: 100%;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-\[1000\] {
  z-index: 1000;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.col-\[1\/-1\] {
  grid-column: 1/-1;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-start-1 {
  grid-column-start: 1;
}
.col-start-2 {
  grid-column-start: 2;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.row-start-1 {
  grid-row-start: 1;
}
.row-start-2 {
  grid-row-start: 2;
}
.-m-1 {
  margin: -0.25rem;
}
.-m-1\.5 {
  margin: -0.375rem;
}
.-m-2 {
  margin: -0.5rem;
}
.-m-2\.5 {
  margin: -0.625rem;
}
.-m-6 {
  margin: -1.5rem;
}
.m-0 {
  margin: 0px;
}
.m-1 {
  margin: 0.25rem;
}
.m-4 {
  margin: 1rem;
}
.-mx-co {
  margin-left: calc(var(--container-outer-width) * -1);
  margin-right: calc(var(--container-outer-width) * -1);
}
.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.-my-3 {
  margin-top: -0.75rem;
  margin-bottom: -0.75rem;
}
.-my-4 {
  margin-top: -1rem;
  margin-bottom: -1rem;
}
.-my-6 {
  margin-top: -1.5rem;
  margin-bottom: -1.5rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-mb-2 {
  margin-bottom: -0.5rem;
}
.-mb-4 {
  margin-bottom: -1rem;
}
.-ml-0 {
  margin-left: -0px;
}
.-ml-0\.5 {
  margin-left: -0.125rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mr-2 {
  margin-right: -0.5rem;
}
.-mr-3 {
  margin-right: -0.75rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-0 {
  margin-left: 0px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-px {
  margin-right: 1px;
}
.ms-auto {
  margin-inline-start: auto;
}
.mt-0 {
  margin-top: 0px;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.box-content {
  box-sizing: content-box;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.contents {
  display: contents;
}
.hidden {
  display: none;
}
.aspect-\[--product-card-image-aspect\2c 1\] {
  aspect-ratio: var(--product-card-image-aspect, 1);
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-12 {
  height: 3rem;
}
.h-4 {
  height: 1rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[--image-size\] {
  height: var(--image-size);
}
.h-\[1em\] {
  height: 1em;
}
.h-full {
  height: 100%;
}
.max-h-\[40rem\] {
  max-height: 40rem;
}
.max-h-\[60vh\] {
  max-height: 60vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-full {
  max-height: 100%;
}
.min-h-\[--section-min-height\] {
  min-height: var(--section-min-height);
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[4rem\] {
  min-height: 4rem;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[--image-size\] {
  width: var(--image-size);
}
.w-\[--logo-width\] {
  width: var(--logo-width);
}
.w-\[--multicolumn-image-width\] {
  width: var(--multicolumn-image-width);
}
.w-\[1em\] {
  width: 1em;
}
.w-\[38rem\] {
  width: 38rem;
}
.w-\[512px\] {
  width: 512px;
}
.w-full {
  width: 100%;
}
.w-max {
  width: max-content;
}
.min-w-\[6rem\] {
  min-width: 6rem;
}
.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.\!max-w-none {
  max-width: none !important;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-\[--content-width\] {
  max-width: var(--content-width);
}
.max-w-\[--image-max-width\] {
  max-width: var(--image-max-width);
}
.max-w-\[50\%\] {
  max-width: 50%;
}
.max-w-full {
  max-width: 100%;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.shrink-\[2\] {
  flex-shrink: 2;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.basis-0 {
  flex-basis: 0px;
}
.basis-20 {
  flex-basis: 5rem;
}
.basis-64 {
  flex-basis: 16rem;
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.-scale-100 {
  --tw-scale-x: -1;
  --tw-scale-y: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.-scale-x-100 {
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
    skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
    scaleY(var(--tw-scale-y));
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
  user-select: none;
}
.resize {
  resize: both;
}
.snap-start {
  scroll-snap-align: start;
}
.snap-center {
  scroll-snap-align: center;
}
.snap-always {
  scroll-snap-stop: always;
}
.list-inside {
  list-style-position: inside;
}
.list-disc {
  list-style-type: disc;
}
.auto-rows-min {
  grid-auto-rows: min-content;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-\[1fr_auto\] {
  grid-template-columns: 1fr auto;
}
.grid-cols-\[1fr_auto_1fr\] {
  grid-template-columns: 1fr auto 1fr;
}
.grid-cols-\[4rem_auto\] {
  grid-template-columns: 4rem auto;
}
.grid-cols-\[auto_1fr\] {
  grid-template-columns: auto 1fr;
}
.grid-cols-\[auto_1fr_auto\] {
  grid-template-columns: auto 1fr auto;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-items-end {
  justify-items: end;
}
.gap-0 {
  gap: 0px;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-block {
  gap: var(--block-spacing);
}
.gap-x-12 {
  column-gap: 3rem;
}
.gap-x-16 {
  column-gap: 4rem;
}
.gap-x-2 {
  column-gap: 0.5rem;
}
.gap-x-3 {
  column-gap: 0.75rem;
}
.gap-x-4 {
  column-gap: 1rem;
}
.gap-x-6 {
  column-gap: 1.5rem;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-12 {
  row-gap: 3rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-6 {
  row-gap: 1.5rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.self-start {
  align-self: flex-start;
}
.self-center {
  align-self: center;
}
.justify-self-end {
  justify-self: end;
}
.justify-self-center {
  justify-self: center;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overscroll-y-contain {
  overscroll-behavior-y: contain;
}
.\!scroll-auto {
  scroll-behavior: auto !important;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.rounded-block {
  border-radius: var(--block-corner-radius);
}
.rounded-block-sm {
  border-radius: var(--block-sm-corner-radius);
}
.rounded-block-xs {
  border-radius: var(--block-xs-corner-radius);
}
.rounded-full {
  border-radius: 9999px;
}
.border {
  border-width: 1.25px;
}
.border-b {
  border-bottom-width: 1.25px;
}
.border-t {
  border-top-width: 1.25px;
}
.border-separator {
  border-color: rgb(var(--color-foreground) / 6%);
}
.\!bg-foreground\/2\.5 {
  background-color: rgb(var(--color-foreground) / 0.025) !important;
}
.bg-\[--icon-background\] {
  background-color: var(--icon-background);
}
.bg-\[rgb\(var\(--slideshow-background\)\)\] {
  background-color: rgb(var(--slideshow-background));
}
.bg-background {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-background) / var(--tw-bg-opacity));
}
.bg-base-accent {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-base-accent) / var(--tw-bg-opacity));
}
.bg-foreground {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-foreground) / var(--tw-bg-opacity));
}
.bg-foreground\/3 {
  background-color: rgb(var(--color-foreground) / 0.03);
}
.bg-light {
  background-color: rgb(var(--color-foreground) / var(--tw-bg-opacity, 2.5%));
}
.bg-modal-background {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--color-modal-background) / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.object-contain {
  object-fit: contain;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[\.25em\] {
  padding: 0.25em;
}
.p-\[\.375em\] {
  padding: 0.375em;
}
.p-\[min\(10\%\2c 2rem\)\] {
  padding: min(10%, 2rem);
}
.\!px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.\!py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[--cart-modal-px\] {
  padding-left: var(--cart-modal-px);
  padding-right: var(--cart-modal-px);
}
.px-co {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.\!pt-0 {
  padding-top: 0px !important;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-base {
  font-size: var(--size-text-base);
}
.text-h0\/h0 {
  font-size: var(--size-text-h0);
  line-height: var(--line-height-h0);
}
.text-h1\/h1 {
  font-size: var(--size-text-h1);
  line-height: var(--line-height-h1);
}
.text-h2\/h2 {
  font-size: var(--size-text-h2);
  line-height: var(--line-height-h2);
}
.text-h3\/h3 {
  font-size: var(--size-text-h3);
  line-height: var(--line-height-h3);
}
.text-h4 {
  font-size: var(--size-text-h4);
}
.text-h4\/h4 {
  font-size: var(--size-text-h4);
  line-height: var(--line-height-h4);
}
.text-h5 {
  font-size: var(--size-text-h5);
}
.text-h5\/h5 {
  font-size: var(--size-text-h5);
  line-height: var(--line-height-h5);
}
.text-h6 {
  font-size: var(--size-text-h6);
}
.text-h6\/h6 {
  font-size: var(--size-text-h6);
  line-height: var(--line-height-h6);
}
.text-lg {
  font-size: var(--size-text-lg);
}
.text-sm {
  font-size: var(--size-text-sm);
}
.text-xs {
  font-size: var(--size-text-xs);
}
.font-bold {
  font-weight: var(--font-body-weight-bold);
}
.font-normal {
  font-weight: var(--font-body-weight);
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.\!normal-case {
  text-transform: none !important;
}
.normal-case {
  text-transform: none;
}
.italic {
  font-style: italic;
}
.leading-none {
  line-height: 1;
}
.leading-normal {
  line-height: 1.5;
}
.leading-relaxed {
  line-height: 1.75;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-wider {
  letter-spacing: max(0.05em, var(--font-body-letter-spacing));
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-\[--icon-foreground\] {
  color: var(--icon-foreground);
}
.text-\[rgb\(var\(--collapsible-content-title-color\)\)\] {
  color: rgb(var(--collapsible-content-title-color));
}
.text-base-accent-foreground {
  --tw-text-opacity: 1;
  color: rgb(var(--color-base-accent-foreground) / var(--tw-text-opacity));
}
.text-danger {
  --tw-text-opacity: 1;
  color: rgb(var(--color-danger) / var(--tw-text-opacity));
}
.text-foreground {
  --tw-text-opacity: 1;
  color: rgb(var(--color-foreground) / var(--tw-text-opacity));
}
.text-foreground\/60 {
  color: rgb(var(--color-foreground) / 0.6);
}
.text-foreground\/75 {
  color: rgb(var(--color-foreground) / 0.75);
}
.text-headings {
  --tw-text-opacity: 1;
  color: rgb(var(--color-headings) / var(--tw-text-opacity));
}
.text-in-stock-text {
  --tw-text-opacity: 1;
  color: rgb(var(--color-in-stock-text) / var(--tw-text-opacity));
}
.text-low-stock-text {
  --tw-text-opacity: 1;
  color: rgb(var(--color-low-stock-text) / var(--tw-text-opacity));
}
.text-success {
  --tw-text-opacity: 1;
  color: rgb(var(--color-success) / var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-opacity-50 {
  --tw-text-opacity: 0.5;
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
  opacity: 0;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-\[--logo-opacity\] {
  opacity: var(--logo-opacity);
}
.shadow-block {
  --tw-shadow: var(--block-shadow);
  --tw-shadow-colored: var(--block-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.shadow-floating-modal {
  --tw-shadow: 0 20px 60px 15px rgba(0 0 0 / 30%);
  --tw-shadow-colored: 0 20px 60px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.outline-0 {
  outline-width: 0px;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
    var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
    var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-foreground\/5 {
  --tw-ring-color: rgb(var(--color-foreground) / 0.05);
}
.transition {
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    backdrop-filter,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@media not all and (min-width: 768px) {
  .prose {
    line-height: 1.6;
  }

  .section-main-article .prose {
    line-height: 1.75;
  }
}
.bleed {
  --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

  margin-left: calc(var(--scroll-bleed) * -1);
  margin-right: calc(var(--scroll-bleed) * -1);

  padding-left: var(--scroll-bleed);
  padding-right: var(--scroll-bleed);

  scroll-padding-left: var(--scroll-bleed);
  scroll-padding-right: var(--scroll-bleed);
}
.bleed-margin {
  margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
}
.scroll-area-x {
  scrollbar-width: none;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
  overscroll-behavior-x: contain;
  scroll-behavior: smooth;
}
.scroll-area-x::-webkit-scrollbar {
  display: none;
}
.scroll-area-y {
  scrollbar-width: none;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-snap-type: y mandatory;
  overscroll-behavior-y: contain;
  scroll-behavior: smooth;
}
.scroll-area-y::-webkit-scrollbar {
  display: none;
}
.trim-margins > :first-child {
  margin-top: 0;
}
.trim-margins > :last-child {
  margin-bottom: 0;
}
.icon-xs {
  width: 1rem;
  --icon-stroke-width: var(--icon-xs-stroke-width);
}
.icon-sm {
  width: 1.25rem;
  --icon-stroke-width: var(--icon-sm-stroke-width);
}
.icon-md {
  width: 1.5rem;
  --icon-stroke-width: var(--icon-md-stroke-width);
}
.icon-xl {
  width: 3rem;
  --icon-stroke-width: var(--icon-xl-stroke-width);
}
.icon-xs-stroke {
  --icon-stroke-width: var(--icon-xs-stroke-width);
}
.icon-sm-stroke {
  --icon-stroke-width: var(--icon-sm-stroke-width);
}
.font-navigation {
  font-family: var(--navigation-font-family);
  font-weight: var(--navigation-font-weight);
  text-transform: var(--navigation-text-transform);
  font-style: var(--navigation-font-style);
}
.font-accordion {
  font-family: var(--accordion-font-family);
  font-weight: var(--accordion-font-weight);
  text-transform: var(--accordion-text-transform, none);
  letter-spacing: var(--accordion-letter-spacing, inherit);
}
.heading {
  color: rgb(var(--color-headings));
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  text-transform: var(--heading-text-transform);
  letter-spacing: var(--heading-letter-spacing);
  overflow-wrap: anywhere;
}
.grid-stack > * {
  grid-area: 1/1;
}
.content-top-left {
  align-self: flex-start;
  justify-self: start;
  text-align: left;
}
.prose-align.content-top-left .prose {
  margin-left: 0;
  margin-right: 0;
}
.section-header--with-link.content-top-left .filler-left {
  display: none;
}
.content-top-center {
  align-self: flex-start;
  justify-self: center;
  text-align: center;
}
.prose-align.content-top-center .prose {
  margin-left: auto;
  margin-right: auto;
}
@media not all and (min-width: 576px) {
  .section-header--with-link.content-top-center {
    text-align: left;
  }

  .section-header--with-link.content-top-center .filler-left {
    display: none;
  }
}
.content-top-right {
  align-self: flex-start;
  justify-self: end;
  text-align: right;
}
.prose-align.content-top-right .prose {
  margin-left: auto;
  margin-right: 0;
}
.content-middle-left {
  align-self: center;
  justify-self: start;
  text-align: left;
}
.prose-align.content-middle-left .prose {
  margin-left: 0;
  margin-right: 0;
}
.section-header--with-link.content-middle-left .filler-left {
  display: none;
}
.content-middle-center {
  align-self: center;
  justify-self: center;
  text-align: center;
}
.prose-align.content-middle-center .prose {
  margin-left: auto;
  margin-right: auto;
}
@media not all and (min-width: 576px) {
  .section-header--with-link.content-middle-center {
    text-align: left;
  }

  .section-header--with-link.content-middle-center .filler-left {
    display: none;
  }
}
.content-middle-right {
  align-self: center;
  justify-self: end;
  text-align: right;
}
.prose-align.content-middle-right .prose {
  margin-left: auto;
  margin-right: 0;
}
.content-bottom-left {
  align-self: flex-end;
  justify-self: start;
  text-align: left;
}
.prose-align.content-bottom-left .prose {
  margin-left: 0;
  margin-right: 0;
}
.section-header--with-link.content-bottom-left .filler-left {
  display: none;
}
.content-bottom-center {
  align-self: flex-end;
  justify-self: center;
  text-align: center;
}
.prose-align.content-bottom-center .prose {
  margin-left: auto;
  margin-right: auto;
}
@media not all and (min-width: 576px) {
  .section-header--with-link.content-bottom-center {
    text-align: left;
  }

  .section-header--with-link.content-bottom-center .filler-left {
    display: none;
  }
}
.content-bottom-right {
  align-self: flex-end;
  justify-self: end;
  text-align: right;
}
.prose-align.content-bottom-right .prose {
  margin-left: auto;
  margin-right: 0;
}
.break-anywhere {
  overflow-wrap: anywhere;
}
.\[--aspect-ratio\:--product-card-image-aspect\] {
  --aspect-ratio: var(--product-card-image-aspect);
}
.\[--bleed-distance\:--cart-modal-px\] {
  --bleed-distance: var(--cart-modal-px);
}
.\[--bleed-distance\:1\.5rem\] {
  --bleed-distance: 1.5rem;
}
.\[--grid-columns\:1\.15\] {
  --grid-columns: 1.15;
}
.\[--grid-columns\:1\.2\] {
  --grid-columns: 1.2;
}
.\[--grid-columns\:1\.5\] {
  --grid-columns: 1.5;
}
.\[--grid-columns\:1\] {
  --grid-columns: 1;
}
.\[--grid-columns\:2\] {
  --grid-columns: 2;
}
.\[--grid-gap-min\:\.75rem\] {
  --grid-gap-min: 0.75rem;
}
.\[--grid-gap\:1\.5rem\] {
  --grid-gap: 1.5rem;
}
.\[--grid-gap\:3rem\] {
  --grid-gap: 3rem;
}
.\[--rating-font-size\:1\.125\] {
  --rating-font-size: 1.125;
}
.\[--section-pt-max\:2\.5rem\] {
  --section-pt-max: 2.5rem;
}
.\[--section-pt-max\:2rem\] {
  --section-pt-max: 2rem;
}
.\[--subheading-opacity\:1\] {
  --subheading-opacity: 1;
}
@media (min-width: 768px) {
  .md\:prose-lg {
    font-size: var(--size-text-lg);
    line-height: 1.7777778;
  }

  .md\:prose-lg :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .md\:prose-lg :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .md\:prose-lg :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-left: 1em;
  }

  .md\:prose-lg :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.3333333;
  }

  .md\:prose-lg :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.3333333;
  }

  .md\:prose-lg :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 1.25em;
    margin-top: 1.6em;
    margin-bottom: 0.6em;
    line-height: 1.4;
  }

  .md\:prose-lg :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .md\:prose-lg :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .md\:prose-lg :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .md\:prose-lg :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .md\:prose-lg :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .md\:prose-lg :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-right: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-left: 0.4444444em;
  }

  .md\:prose-lg :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .md\:prose-lg :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .md\:prose-lg :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .md\:prose-lg :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-right: 1.5em;
    padding-bottom: 1em;
    padding-left: 1.5em;
  }

  .md\:prose-lg :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-left: 1.5555556em;
  }

  .md\:prose-lg :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-left: 1.5555556em;
  }

  .md\:prose-lg :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .md\:prose-lg :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0.4444444em;
  }

  .md\:prose-lg :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0.4444444em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > ul > li > *:first-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-top: 1.3333333em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > ul > li > *:last-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-bottom: 1.3333333em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > ol > li > *:first-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-top: 1.3333333em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > ol > li > *:last-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    margin-bottom: 1.3333333em;
  }

  .md\:prose-lg
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .md\:prose-lg :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .md\:prose-lg :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .md\:prose-lg :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-left: 1.5555556em;
  }

  .md\:prose-lg :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .md\:prose-lg :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .md\:prose-lg :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .md\:prose-lg :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .md\:prose-lg :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .md\:prose-lg :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .md\:prose-lg :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-right: 0.75em;
    padding-bottom: 0.75em;
    padding-left: 0.75em;
  }

  .md\:prose-lg
    :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0;
  }

  .md\:prose-lg
    :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-right: 0;
  }

  .md\:prose-lg
    :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-right: 0.75em;
    padding-bottom: 0.75em;
    padding-left: 0.75em;
  }

  .md\:prose-lg
    :where(tbody td:first-child, tfoot td:first-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    padding-left: 0;
  }

  .md\:prose-lg
    :where(tbody td:last-child, tfoot td:last-child):not(
      :where([class~="not-prose"], [class~="not-prose"] *)
    ) {
    padding-right: 0;
  }

  .md\:prose-lg :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .md\:prose-lg :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .md\:prose-lg :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .md\:prose-lg
    :where(.md\:prose-lg > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}
.before\:text-danger::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(var(--color-danger) / var(--tw-text-opacity));
}
.before\:text-success::before {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(var(--color-success) / var(--tw-text-opacity));
}
.first\:hidden:first-child {
  display: none;
}
.first\:border-t-0:first-child {
  border-top-width: 0px;
}
.first\:pt-0:first-child {
  padding-top: 0px;
}
.last\:hidden:last-child {
  display: none;
}
.last\:border-0:last-child {
  border-width: 0px;
}
.last\:pb-0:last-child {
  padding-bottom: 0px;
}
.empty\:hidden:empty {
  display: none;
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.group\/media:hover .group-hover\/media\:opacity-100 {
  opacity: 1;
}
.group.option-unavailable .group-\[\.option-unavailable\]\:block {
  display: block;
}
.group.has-diff-bg .group-\[\.has-diff-bg\]\:border-0 {
  border-width: 0px;
}
.group.option-unavailable .group-\[\.option-unavailable\]\:line-through {
  text-decoration-line: line-through;
}
.data-\[has-diff-bg\]\:rounded-block[data-has-diff-bg] {
  border-radius: var(--block-corner-radius);
}
.data-\[has-diff-bg\]\:border-0[data-has-diff-bg] {
  border-width: 0px;
}
.data-\[has-diff-bg\]\:p-5[data-has-diff-bg] {
  padding: 1.25rem;
}
.rfs\:mb-10 {
  margin-bottom: min(2.5rem, calc(1.375rem + 1.5vw));
}
.rfs\:mb-12 {
  margin-bottom: min(3rem, calc(1.5rem + 2vw));
}
.rfs\:mb-16 {
  margin-bottom: min(4rem, calc(1.75rem + 3vw));
}
.rfs\:mb-20 {
  margin-bottom: min(5rem, calc(2rem + 4vw));
}
.rfs\:mb-8 {
  margin-bottom: min(2rem, calc(1.25rem + 1vw));
}
.rfs\:gap-12 {
  gap: min(3rem, calc(1.5rem + 2vw));
}
.rfs\:gap-y-12 {
  row-gap: min(3rem, calc(1.5rem + 2vw));
}
.rfs\:p-12 {
  padding: min(3rem, calc(1.5rem + 2vw));
}
.rfs\:p-14 {
  padding: min(3.5rem, calc(1.625rem + 2.5vw));
}
.rfs\:px-12 {
  padding-left: min(3rem, calc(1.5rem + 2vw));
  padding-right: min(3rem, calc(1.5rem + 2vw));
}
.rfs\:px-co {
  padding-left: var(--container-outer-width);
  padding-right: var(--container-outer-width);
}
.rfs\:pb-20 {
  padding-bottom: min(5rem, calc(2rem + 4vw));
}
.rfs\:pt-20 {
  padding-top: min(5rem, calc(2rem + 4vw));
}
.rfs\:\[--section-pt-max\:2\.5rem\] {
  --section-pt-max: min(2.5rem, calc(1.375rem + 1.5vw));
}
.rfs\:\[--section-vertical-spacing\:2\.5rem\] {
  --section-vertical-spacing: min(2.5rem, calc(1.375rem + 1.5vw));
}
@media (hover: none), (pointer: coarse) {
  .touch\:hidden {
    display: none;
  }

  .touch\:scroll-area-x {
    scrollbar-width: none;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    scroll-behavior: smooth;
  }

  .touch\:scroll-area-x::-webkit-scrollbar {
    display: none;
  }
}
@media (hover: hover) and (pointer: fine) {
  .mouse\:overflow-x-auto {
    overflow-x: auto;
  }

  .mouse\:hover\:opacity-100:hover {
    opacity: 1;
  }
}
.child\:w-full > * {
  width: 100%;
}
.first\:child\:col-span-2 > *:first-child {
  grid-column: span 2 / span 2;
}
.no-js .no-js\:invisible {
  visibility: hidden;
}
.no-js .no-js\:\!block {
  display: block !important;
}
.no-js .no-js\:hidden {
  display: none;
}
.no-js .no-js\:opacity-100 {
  opacity: 1;
}
.js .js\:hidden {
  display: none;
}
.em\:w-3 {
  width: 0.75em;
}
@media not all and (min-width: 1200px) {
  .max-xl\:hidden {
    display: none;
  }

  .max-xl\:flex-col {
    flex-direction: column;
  }

  .max-xl\:justify-center {
    justify-content: center;
  }
  .max-xl\:bleed {
    --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

    margin-left: calc(var(--scroll-bleed) * -1);
    margin-right: calc(var(--scroll-bleed) * -1);

    padding-left: var(--scroll-bleed);
    padding-right: var(--scroll-bleed);

    scroll-padding-left: var(--scroll-bleed);
    scroll-padding-right: var(--scroll-bleed);
  }
}
@media not all and (min-width: 992px) {
  .max-lg\:-mx-co {
    margin-left: calc(var(--container-outer-width) * -1);
    margin-right: calc(var(--container-outer-width) * -1);
  }

  .max-lg\:hidden {
    display: none;
  }

  .max-lg\:w-full {
    width: 100%;
  }

  .max-lg\:scroll-pl-co {
    scroll-padding-left: var(--container-outer-width);
  }

  .max-lg\:px-co {
    padding-left: var(--container-outer-width);
    padding-right: var(--container-outer-width);
  }

  .max-lg\:\!pb-0 {
    padding-bottom: 0px !important;
  }
  .max-lg\:bleed {
    --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

    margin-left: calc(var(--scroll-bleed) * -1);
    margin-right: calc(var(--scroll-bleed) * -1);

    padding-left: var(--scroll-bleed);
    padding-right: var(--scroll-bleed);

    scroll-padding-left: var(--scroll-bleed);
    scroll-padding-right: var(--scroll-bleed);
  }

  .max-lg\:scroll-area-x {
    scrollbar-width: none;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    scroll-behavior: smooth;
  }

  .max-lg\:scroll-area-x::-webkit-scrollbar {
    display: none;
  }
}
@media not all and (min-width: 768px) {
  .max-md\:grid {
    display: grid;
  }

  .max-md\:hidden {
    display: none;
  }

  .max-md\:max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-md\:grid-cols-\[5rem_1fr\] {
    grid-template-columns: 5rem 1fr;
  }

  .max-md\:items-start {
    align-items: flex-start;
  }

  .max-md\:items-end {
    align-items: flex-end;
  }

  .max-md\:items-center {
    align-items: center;
  }

  .max-md\:text-left {
    text-align: left;
  }

  .max-md\:text-center {
    text-align: center;
  }

  .max-md\:text-right {
    text-align: right;
  }

  .max-md\:text-sm {
    font-size: var(--size-text-sm);
  }

  .max-md\:text-xs {
    font-size: var(--size-text-xs);
  }

  .max-md\:font-bold {
    font-weight: var(--font-body-weight-bold);
  }
  .max-md\:bleed {
    --scroll-bleed: var(--bleed-distance, var(--container-outer-width));

    margin-left: calc(var(--scroll-bleed) * -1);
    margin-right: calc(var(--scroll-bleed) * -1);

    padding-left: var(--scroll-bleed);
    padding-right: var(--scroll-bleed);

    scroll-padding-left: var(--scroll-bleed);
    scroll-padding-right: var(--scroll-bleed);
  }

  .max-md\:scroll-area-x {
    scrollbar-width: none;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    scroll-behavior: smooth;
  }

  .max-md\:scroll-area-x::-webkit-scrollbar {
    display: none;
  }

  .no-js .no-js\:max-md\:absolute {
    position: absolute;
  }

  .no-js .no-js\:max-md\:bottom-0 {
    bottom: 0px;
  }

  .no-js .no-js\:max-md\:right-0 {
    right: 0px;
  }

  .js .js\:max-md\:hidden {
    display: none;
  }
}
@media not all and (min-width: 576px) {
  .max-sm\:w-full {
    width: 100%;
  }

  .max-sm\:pr-3 {
    padding-right: 0.75rem;
  }

  .max-sm\:text-sm {
    font-size: var(--size-text-sm);
  }

  .max-sm\:text-xs {
    font-size: var(--size-text-xs);
  }

  .max-sm\:bleed-margin {
    margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
    margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  }
}
@media (min-width: 576px) {
  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:-m-2 {
    margin: -0.5rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:mb-16 {
    margin-bottom: 4rem;
  }

  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:mt-12 {
    margin-top: 3rem;
  }

  .sm\:mt-2 {
    margin-top: 0.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-14 {
    height: 3.5rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:w-14 {
    width: 3.5rem;
  }

  .sm\:w-20 {
    width: 5rem;
  }

  .sm\:w-8 {
    width: 2rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-\[5rem_auto\] {
    grid-template-columns: 5rem auto;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:gap-12 {
    gap: 3rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:gap-x-8 {
    column-gap: 2rem;
  }

  .sm\:justify-self-start {
    justify-self: start;
  }

  .sm\:justify-self-end {
    justify-self: end;
  }

  .sm\:overflow-hidden {
    overflow: hidden;
  }

  .sm\:rounded-block {
    border-radius: var(--block-corner-radius);
  }

  .sm\:rounded-b-block {
    border-bottom-right-radius: var(--block-corner-radius);
    border-bottom-left-radius: var(--block-corner-radius);
  }

  .sm\:rounded-t-block {
    border-top-left-radius: var(--block-corner-radius);
    border-top-right-radius: var(--block-corner-radius);
  }

  .sm\:p-12 {
    padding: 3rem;
  }

  .sm\:p-2 {
    padding: 0.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .sm\:pt-8 {
    padding-top: 2rem;
  }

  .sm\:shadow-block {
    --tw-shadow: var(--block-shadow);
    --tw-shadow-colored: var(--block-shadow);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }

  .sm\:bleed-margin {
    margin-left: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
    margin-right: calc(var(--bleed-distance, var(--container-outer-width)) * -1);
  }

  .sm\:\[--grid-columns\:2\.25\] {
    --grid-columns: 2.25;
  }

  .sm\:\[--grid-columns\:2\] {
    --grid-columns: 2;
  }

  @media not all and (min-width: 992px) {
    .sm\:max-lg\:rounded-b-block {
      border-bottom-right-radius: var(--block-corner-radius);
      border-bottom-left-radius: var(--block-corner-radius);
    }

    .sm\:max-lg\:rounded-t-block {
      border-top-left-radius: var(--block-corner-radius);
      border-top-right-radius: var(--block-corner-radius);
    }
  }
}
@media (min-width: 768px) {
  .md\:right-4 {
    right: 1rem;
  }

  .md\:right-8 {
    right: 2rem;
  }

  .md\:top-4 {
    top: 1rem;
  }

  .md\:top-8 {
    top: 2rem;
  }

  .md\:col-auto {
    grid-column: auto;
  }

  .md\:row-auto {
    grid-row: auto;
  }

  .md\:m-6 {
    margin: 1.5rem;
  }

  .md\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .md\:my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .md\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .md\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .md\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .md\:-mr-6 {
    margin-right: -1.5rem;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:ml-8 {
    margin-left: 2rem;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:mt-2 {
    margin-top: 0.5rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:table {
    display: table;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-10 {
    height: 2.5rem;
  }

  .md\:h-8 {
    height: 2rem;
  }

  .md\:min-h-\[6rem\] {
    min-height: 6rem;
  }

  .md\:w-10 {
    width: 2.5rem;
  }

  .md\:w-12 {
    width: 3rem;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-24 {
    width: 6rem;
  }

  .md\:w-3 {
    width: 0.75rem;
  }

  .md\:w-3\.5 {
    width: 0.875rem;
  }

  .md\:w-36 {
    width: 9rem;
  }

  .md\:w-4 {
    width: 1rem;
  }

  .md\:w-5 {
    width: 1.25rem;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:max-w-\[640px\] {
    max-width: 640px;
  }

  .md\:snap-start {
    scroll-snap-align: start;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-\[auto_1fr\] {
    grid-template-columns: auto 1fr;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-x-4 {
    column-gap: 1rem;
  }

  .md\:gap-x-6 {
    column-gap: 1.5rem;
  }

  .md\:gap-y-12 {
    row-gap: 3rem;
  }

  .md\:gap-y-4 {
    row-gap: 1rem;
  }

  .md\:gap-y-8 {
    row-gap: 2rem;
  }

  .md\:border-l {
    border-left-width: 1.25px;
  }

  .md\:border-solid {
    border-style: solid;
  }

  .md\:border-separator {
    border-color: rgb(var(--color-foreground) / 6%);
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-6 {
    padding-top: 1.5rem;
  }

  .md\:text-base {
    font-size: var(--size-text-base);
  }

  .md\:text-h3\/h3 {
    font-size: var(--size-text-h3);
    line-height: var(--line-height-h3);
  }

  .md\:text-h4 {
    font-size: var(--size-text-h4);
  }

  .md\:text-h4\/h4 {
    font-size: var(--size-text-h4);
    line-height: var(--line-height-h4);
  }

  .md\:text-h5 {
    font-size: var(--size-text-h5);
  }

  .md\:text-h5\/h5 {
    font-size: var(--size-text-h5);
    line-height: var(--line-height-h5);
  }

  .md\:text-h6 {
    font-size: var(--size-text-h6);
  }

  .md\:text-h6\/h6 {
    font-size: var(--size-text-h6);
    line-height: var(--line-height-h6);
  }

  .md\:text-lg {
    font-size: var(--size-text-lg);
  }

  .md\:text-sm {
    font-size: var(--size-text-sm);
  }

  .md\:icon-sm {
    width: 1.25rem;
    --icon-stroke-width: var(--icon-sm-stroke-width);
  }

  .md\:icon-md {
    width: 1.5rem;
    --icon-stroke-width: var(--icon-md-stroke-width);
  }

  .md\:icon-lg {
    width: 1.75rem;
    --icon-stroke-width: var(--icon-lg-stroke-width);
  }

  .md\:icon-sm-stroke {
    --icon-stroke-width: var(--icon-sm-stroke-width);
  }

  .md\:heading {
    color: rgb(var(--color-headings));
    font-family: var(--font-heading-family);
    font-style: var(--font-heading-style);
    font-weight: var(--font-heading-weight);
    text-transform: var(--heading-text-transform);
    letter-spacing: var(--heading-letter-spacing);
    overflow-wrap: anywhere;
  }
  .md\:content-top-left {
    align-self: flex-start;
    justify-self: start;
    text-align: left;
  }
  .prose-align.md\:content-top-left .prose {
    margin-left: 0;
    margin-right: 0;
  }
  .section-header--with-link.md\:content-top-left .filler-left {
    display: none;
  }

  .md\:content-top-center {
    align-self: flex-start;
    justify-self: center;
    text-align: center;
  }
  .prose-align.md\:content-top-center .prose {
    margin-left: auto;
    margin-right: auto;
  }
  @media not all and (min-width: 576px) {
    .section-header--with-link.md\:content-top-center {
      text-align: left;
    }

    .section-header--with-link.md\:content-top-center .filler-left {
      display: none;
    }
  }

  .md\:content-top-right {
    align-self: flex-start;
    justify-self: end;
    text-align: right;
  }
  .prose-align.md\:content-top-right .prose {
    margin-left: auto;
    margin-right: 0;
  }

  .md\:content-middle-left {
    align-self: center;
    justify-self: start;
    text-align: left;
  }
  .prose-align.md\:content-middle-left .prose {
    margin-left: 0;
    margin-right: 0;
  }
  .section-header--with-link.md\:content-middle-left .filler-left {
    display: none;
  }

  .md\:content-middle-center {
    align-self: center;
    justify-self: center;
    text-align: center;
  }
  .prose-align.md\:content-middle-center .prose {
    margin-left: auto;
    margin-right: auto;
  }
  @media not all and (min-width: 576px) {
    .section-header--with-link.md\:content-middle-center {
      text-align: left;
    }

    .section-header--with-link.md\:content-middle-center .filler-left {
      display: none;
    }
  }

  .md\:content-middle-right {
    align-self: center;
    justify-self: end;
    text-align: right;
  }
  .prose-align.md\:content-middle-right .prose {
    margin-left: auto;
    margin-right: 0;
  }

  .md\:content-bottom-left {
    align-self: flex-end;
    justify-self: start;
    text-align: left;
  }
  .prose-align.md\:content-bottom-left .prose {
    margin-left: 0;
    margin-right: 0;
  }
  .section-header--with-link.md\:content-bottom-left .filler-left {
    display: none;
  }

  .md\:content-bottom-center {
    align-self: flex-end;
    justify-self: center;
    text-align: center;
  }
  .prose-align.md\:content-bottom-center .prose {
    margin-left: auto;
    margin-right: auto;
  }
  @media not all and (min-width: 576px) {
    .section-header--with-link.md\:content-bottom-center {
      text-align: left;
    }

    .section-header--with-link.md\:content-bottom-center .filler-left {
      display: none;
    }
  }

  .md\:content-bottom-right {
    align-self: flex-end;
    justify-self: end;
    text-align: right;
  }
  .prose-align.md\:content-bottom-right .prose {
    margin-left: auto;
    margin-right: 0;
  }
  .md\:\[--bleed-distance\:2rem\] {
    --bleed-distance: 2rem;
  }
  .md\:\[--grid-columns\:1\.6\] {
    --grid-columns: 1.6;
  }
  .md\:\[--grid-columns\:2\.25\] {
    --grid-columns: 2.25;
  }
  .md\:\[--grid-columns\:3\] {
    --grid-columns: 3;
  }
  .md\:\[--grid-gap-min\:1\.5rem\] {
    --grid-gap-min: 1.5rem;
  }
  .md\:\[--grid-gap\:2rem\] {
    --grid-gap: 2rem;
  }
  .md\:\[--rating-font-size\:1\.25\] {
    --rating-font-size: 1.25;
  }
  .md\:\[--section-pt-max\:3rem\] {
    --section-pt-max: 3rem;
  }
  .data-\[has-diff-bg\]\:md\:p-6[data-has-diff-bg] {
    padding: 1.5rem;
  }
}
@media (min-width: 992px) {
  .lg\:bottom-4 {
    bottom: 1rem;
  }

  .lg\:right-4 {
    right: 1rem;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:mx-8 {
    margin-left: 2rem;
    margin-right: 2rem;
  }

  .lg\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .lg\:mb-8 {
    margin-bottom: 2rem;
  }

  .lg\:ml-3 {
    margin-left: 0.75rem;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-12 {
    height: 3rem;
  }

  .lg\:w-12 {
    width: 3rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-\[1fr_2fr\] {
    grid-template-columns: 1fr 2fr;
  }

  .lg\:grid-cols-\[2fr_1fr\] {
    grid-template-columns: 2fr 1fr;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-12 {
    column-gap: 3rem;
  }

  .lg\:gap-y-6 {
    row-gap: 1.5rem;
  }

  .lg\:rounded-full {
    border-radius: 9999px;
  }

  .lg\:rounded-b-block {
    border-bottom-right-radius: var(--block-corner-radius);
    border-bottom-left-radius: var(--block-corner-radius);
  }

  .lg\:rounded-l-block {
    border-top-left-radius: var(--block-corner-radius);
    border-bottom-left-radius: var(--block-corner-radius);
  }

  .lg\:rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .lg\:rounded-r-block {
    border-top-right-radius: var(--block-corner-radius);
    border-bottom-right-radius: var(--block-corner-radius);
  }

  .lg\:rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .lg\:pl-co {
    padding-left: var(--container-outer-width);
  }

  .lg\:pr-6 {
    padding-right: 1.5rem;
  }

  .lg\:pr-co {
    padding-right: var(--container-outer-width);
  }

  .lg\:pt-8 {
    padding-top: 2rem;
  }

  .lg\:icon-md {
    width: 1.5rem;
    --icon-stroke-width: var(--icon-md-stroke-width);
  }
  .lg\:grid-cols-image-with-text {
    grid-template-columns: minmax(auto, var(--image-with-text-width, 50%)) minmax(360px, 1fr);
  }

  .lg\:grid-cols-image-with-text--image-right {
    grid-template-columns: minmax(360px, 1fr) minmax(auto, var(--image-with-text-width, 50%));
  }

  .lg\:\[--bleed-distance\:3rem\] {
    --bleed-distance: 3rem;
  }

  .lg\:\[--grid-columns\:2\.2\] {
    --grid-columns: 2.2;
  }

  .lg\:\[--grid-columns\:3\] {
    --grid-columns: 3;
  }

  .lg\:\[--grid-columns\:5\] {
    --grid-columns: 5;
  }

  .lg\:rfs\:pb-20 {
    padding-bottom: min(5rem, calc(2rem + 4vw));
  }

  .lg\:rfs\:pl-12 {
    padding-left: min(3rem, calc(1.5rem + 2vw));
  }

  .lg\:rfs\:pr-12 {
    padding-right: min(3rem, calc(1.5rem + 2vw));
  }
}
@media (min-width: 1200px) {
  .xl\:col-start-1 {
    grid-column-start: 1;
  }

  .xl\:col-start-2 {
    grid-column-start: 2;
  }

  .xl\:row-start-1 {
    grid-row-start: 1;
  }

  .xl\:ml-6 {
    margin-left: 1.5rem;
  }

  .xl\:ml-auto {
    margin-left: auto;
  }

  .xl\:mt-12 {
    margin-top: 3rem;
  }

  .xl\:mt-2 {
    margin-top: 0.5rem;
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:items-center {
    align-items: center;
  }

  .xl\:gap-12 {
    gap: 3rem;
  }

  .xl\:gap-16 {
    gap: 4rem;
  }

  .xl\:gap-6 {
    gap: 1.5rem;
  }

  .xl\:p-16 {
    padding: 4rem;
  }

  .xl\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .xl\:\!pr-co {
    padding-right: var(--container-outer-width) !important;
  }

  .xl\:text-right {
    text-align: right;
  }

  .xl\:\[--grid-columns\:3\] {
    --grid-columns: 3;
  }
}
@media (min-width: 1400px) {
  .hd\:w-20 {
    width: 5rem;
  }

  .hd\:pt-12 {
    padding-top: 3rem;
  }

  .hd\:\[--grid-columns\:4\] {
    --grid-columns: 4;
  }
}
@media print {
  .print\:block {
    display: block;
  }

  .print\:hidden {
    display: none;
  }
}
.\[\&\.is-hidden\]\:pointer-events-none.is-hidden {
  pointer-events: none;
}
.\[\&\.is-hidden\]\:opacity-50.is-hidden {
  opacity: 0.5;
}
.\[\&\:\:part\(panel\)\]\:w-\[28rem\]::part(panel) {
  width: 28rem;
}
@media (min-width: 1024px) {
  .\[\@media\(min-width\:1024px\)\]\:rounded-block {
    border-radius: var(--block-corner-radius);
  }
}
