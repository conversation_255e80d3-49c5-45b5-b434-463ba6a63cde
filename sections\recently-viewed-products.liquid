{% liquid
  if request.page_type == 'product'
    assign product_ids = product.id
  elsif request.page_type == 'cart'
    assign product_ids = cart.items | map: 'product' | map: 'id' | join: ','
  endif
%}

{% capture image_sizes %}
  {% render 'product-carousel-image-sizes',
      enabled: section.settings.carousel,
      grid_columns: section.settings.grid_columns,
      grid_columns_mobile: section.settings.grid_columns_mobile
  %}
{% endcapture %}

<recently-viewed-products
  section-id="{{ section.id }}"
  product-id="{{ product_ids }}"
  limit="{{ section.settings.products_count }}"
>
  {% if search.performed and search.results_count > 0 %}
    {% render 'section-bg-number-vars' %}

    {% capture card_attrs %}
        data-animation="block"
        data-animation-group="{{ section.id }}-product-cards"
      {% endcapture %}

    <div {% render 'section-attrs', class: 'overflow-hidden' %}>
      {% render 'section-header' %}

      <div class="section-body">
        {% capture items %}
            {% for product in search.results limit: section.settings.products_count %}
              <div class="grid-carousel-item">
                {% render 'product-card', card_product: product, attrs: card_attrs, sizes: image_sizes %}
              </div>
            {% endfor %}
          {% endcapture %}

        {% render 'product-carousel',
          enabled: section.settings.carousel,
          grid_columns: section.settings.grid_columns,
          grid_columns_mobile: section.settings.grid_columns_mobile,
          slot: items
        %}
      </div>
    </div>
  {% endif %}
</recently-viewed-products>

{% schema %}
{
  "name": "t:sections.recently-viewed-products.name",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.recently-viewed-products.paragraph__0.content"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.all.full_width.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "carousel",
      "label": "t:sections.all.carousel.label",
      "default": true
    },
    {
      "type": "range",
      "id": "products_count",
      "min": 2,
      "max": 20,
      "label": "t:sections.recently-viewed-products.settings.products_count.label",
      "default": 8
    },
    {
      "type": "range",
      "id": "grid_columns",
      "label": "t:sections.recently-viewed-products.settings.grid_columns.label",
      "min": 2,
      "max": 5,
      "default": 4
    },
    {
      "type": "select",
      "id": "grid_columns_mobile",
      "label": "t:sections.recently-viewed-products.settings.grid_columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.recently-viewed-products.settings.grid_columns_mobile.options__0.label"
        },
        {
          "value": "2",
          "label": "t:sections.recently-viewed-products.settings.grid_columns_mobile.options__1.label"
        }
      ],
      "default": "2"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.subheading.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.heading.label",
      "default": "Recently viewed products"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "t:sections.all.heading_size.label",
      "options": [
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__0.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__5.label"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__6.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "t:sections.all.content.label"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.text_alignment.label",
      "options": [
        {
          "value": "text-left",
          "label": "t:sections.all.text_alignment.options.text_left.label"
        },
        {
          "value": "text-center",
          "label": "t:sections.all.text_alignment.options.text_center.label"
        },
        {
          "value": "text-right",
          "label": "t:sections.all.text_alignment.options.text_right.label"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.colors"
    },
    {
      "type": "color",
      "label": "t:sections.all.background_color.label",
      "id": "background_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.text_color.label",
      "id": "text_color"
    },
    {
      "type": "color",
      "label": "t:sections.all.heading_color.label",
      "id": "heading_color"
    },
    {
      "type": "header",
      "content": "t:sections.all.headers.seo"
    },
    {
      "type": "select",
      "id": "heading_html_tag",
      "label": "t:sections.all.heading_html_tag.label",
      "options": [
        {
          "value": "h1",
          "label": "h1"
        },
        {
          "value": "h2",
          "label": "h2"
        },
        {
          "value": "h3",
          "label": "h3"
        },
        {
          "value": "h4",
          "label": "h4"
        },
        {
          "value": "h5",
          "label": "h5"
        },
        {
          "value": "h6",
          "label": "h6"
        },
        {
          "value": "p",
          "label": "p"
        },
        {
          "value": "span",
          "label": "span"
        },
        {
          "value": "div",
          "label": "div"
        }
      ],
      "default": "p",
      "info": "t:sections.all.heading_html_tag.info"
    }
  ],
  "disabled_on": {
    "groups": [
      "header",
      "custom.overlay"
    ]
  },
  "presets": [
    {
      "name": "t:sections.recently-viewed-products.presets__0.name"
    }
  ]
}
{% endschema %}
