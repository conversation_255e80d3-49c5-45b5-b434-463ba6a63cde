{% form 'customer_address', address, data-form-button-loading: true %}
  <div class="checkbox mb-8">
    {{ form.set_as_default_checkbox }}
    <label for="address_default_address_{{ address.id | default: "new" }}">
      {{- 'customer.addresses.set_default' | t -}}
    </label>
  </div>

  <div class="grid grid-cols-2 gap-4">
    <div class="form-floating">
      <input
        type="text"
        id="AddressFirstNameNew"
        name="address[first_name]"
        value="{{ form.first_name }}"
        autocomplete="given-name"
        class="input"
        placeholder="{{ 'customer.addresses.first_name' | t }}"
      >
      <label for="AddressFirstNameNew">{{ 'customer.addresses.first_name' | t }}</label>
    </div>
    <div class="form-floating">
      <input
        type="text"
        id="AddressLastNameNew"
        name="address[last_name]"
        value="{{ form.last_name }}"
        autocomplete="family-name"
        class="input"
        placeholder="{{ 'customer.addresses.last_name' | t }}"
      >
      <label for="AddressLastNameNew">{{ 'customer.addresses.last_name' | t }}</label>
    </div>
  </div>

  <div class="form-floating mt-4">
    <input
      type="text"
      id="AddressCompanyNew"
      name="address[company]"
      value="{{ form.company }}"
      autocomplete="organization"
      class="input"
      placeholder="{{ 'customer.addresses.company' | t }}"
    >
    <label for="AddressCompanyNew">{{ 'customer.addresses.company' | t }}</label>
  </div>
  <div class="form-floating mt-4">
    <input
      type="text"
      id="AddressAddress1New"
      name="address[address1]"
      value="{{ form.address1 }}"
      autocomplete="address-line1"
      class="input"
      placeholder="{{ 'customer.addresses.address1' | t }}"
    >
    <label for="AddressAddress1New">{{ 'customer.addresses.address1' | t }}</label>
  </div>
  <div class="form-floating mt-4">
    <input
      type="text"
      id="AddressAddress2New"
      name="address[address2]"
      value="{{ form.address2 }}"
      autocomplete="address-line2"
      class="input"
      placeholder="{{ 'customer.addresses.address2' | t }}"
    >
    <label for="AddressAddress2New">{{ 'customer.addresses.address2' | t }}</label>
  </div>

  <div class="grid grid-cols-2 gap-4 mt-4">
    <div class="form-floating">
      <input
        type="text"
        id="AddressCityNew"
        name="address[city]"
        value="{{ form.city }}"
        autocomplete="address-level2"
        class="input"
        placeholder="{{ 'customer.addresses.city' | t }}"
      >
      <label for="AddressCityNew">{{ 'customer.addresses.city' | t }}</label>
    </div>

    <div class="form-floating">
      <input
        type="text"
        id="AddressZipNew"
        name="address[zip]"
        value="{{ form.zip }}"
        autocapitalize="characters"
        autocomplete="postal-code"
        class="input"
        placeholder="{{ 'customer.addresses.zip' | t }}"
      >
      <label for="AddressZipNew">{{ 'customer.addresses.zip' | t }}</label>
    </div>
  </div>

  <province-selector>
    <div class="form-floating form-select mt-4">
      <select
        id="AddressCountryNew"
        class="address-country input"
        name="address[country]"
        data-default="{{ form.country }}"
        autocomplete="country"
        placeholder="{{ 'customer.addresses.country' | t }}"
        data-country-select
      >
        {{ all_country_option_tags -}}
      </select>
      <label for="AddressCountryNew">{{ 'customer.addresses.country' | t }}</label>
    </div>

    <div class="form-floating form-select mt-4 hidden">
      <select
        class="address-province input"
        id="AddressProvinceNew"
        name="address[province]"
        data-default="{{ form.province }}"
        autocomplete="address-level1"
        placeholder="{{ 'customer.addresses.province' | t }}"
        data-province-select
      ></select>
      <label for="AddressProvinceNew">{{ 'customer.addresses.province' | t }}</label>
    </div>
  </province-selector>

  <div class="form-floating mt-4">
    <input
      type="tel"
      id="AddressPhoneNew"
      name="address[phone]"
      value="{{ form.phone }}"
      autocomplete="tel"
      class="input"
      placeholder="{{ 'customer.addresses.phone' | t }}"
    >
    <label for="AddressPhoneNew">{{ 'customer.addresses.phone' | t }}</label>
  </div>

  <div class="mt-8 sm:mt-12">
    <button type="submit" class="button button-primary">
      {% if edit %}
        {{ 'customer.addresses.edit' | t }}
      {% else %}
        {{ 'customer.addresses.add' | t }}
      {% endif %}
    </button>
    <a tabindex="0" role="button" class="styled-link ml-4" data-button-close aria-label="{{ 'accessibility.close_modal' | t }}" type="reset" id="CloseAddNewAddress">
      {{ 'customer.addresses.cancel' | t }}
    </a>
  </div>
{% endform %}
