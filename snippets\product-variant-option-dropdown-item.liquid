<button
  class="dropdown-list-item w-full group {% unless option_value.available %}option-unavailable{% endunless %}"
  data-text="{{ option_value | escape }}"
  data-value="{{ option_value | escape }}"
  tabindex="-1"
  data-option-value-id="{{ option_value.id }}"
  data-variant-id="{{ option_value.variant.id }}"
  data-product-url="{{ option_value.product_url }}"
>
  <span class="group-[.option-unavailable]:line-through">
    {{- option_value -}}
  </span>

  {% unless option_value.available %}
    <span class="ml-2 opacity-70 hidden group-[.option-unavailable]:block font-normal">
      ({{ 'products.product.unavailable' | t }})
    </span>
  {% endunless %}
</button>
