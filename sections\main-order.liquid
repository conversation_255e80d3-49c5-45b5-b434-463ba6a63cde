{% render 'section-bg-number-vars' %}

<div class="section section--full-width">
  {% render 'account-header' %}

  <div class="flex mb-4 gap-2 sm:gap-4 items-center">
    <h2 class="h5 mr-2">
      {{ 'customer.orders.order_number' | t: name: order.name }}
    </h2>

    <span class="status-pill financial-status-{{ order.financial_status }}">
      {{ order.financial_status_label }}
    </span>

    <span class="status-pill fulfillment-status-{{ order.fulfillment_status }}">
      {{ order.fulfillment_status_label }}
    </span>
  </div>

  {%- assign order_date = order.created_at | time_tag: format: 'date_at_time' -%}
  <p class="max-md:text-sm">
    {{ 'customer.order.date_html' | t: date: order_date }}
  </p>
  {%- if order.cancelled -%}
    {%- assign cancelled_at = order.cancelled_at | time_tag: format: 'date_at_time' -%}
    <p class="max-md:text-sm">{{ 'customer.order.cancelled_html' | t: date: cancelled_at }}</p>
    <p class="max-md:text-sm">{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason_label }}</p>
  {%- endif -%}

  <div class="account-order-wrapper grid lg:grid-cols-3 gap-12 mt-8">
    <div class="lg:col-span-2">
      <div class="orders-table hidden sm:block">
        <div class="horizontal-scrolling">
          <table class="table-styled w-full">
            <caption class="visually-hidden">
              {{ 'customer.orders.order_number_summary' | t: name: order.name }}
            </caption>
            <thead>
              <tr>
                <th scope="col">{{ 'customer.order.product' | t }}</th>

                <th scope="col" class="text-center">
                  {{ 'customer.order.quantity' | t }}
                </th>
                <th scope="col" class="text-right">{{ 'customer.order.total' | t }}</th>
              </tr>
            </thead>
            <tbody>
              {%- for line_item in order.line_items -%}
                <tr>
                  <td scope="row">
                    {% render 'order-line-item', item: line_item %}
                  </td>
                  <td class="text-center">
                    {{ line_item.quantity }}
                  </td>
                  <td class="text-right">
                    {%- if line_item.original_line_price != line_item.final_line_price -%}
                      <dl>
                        <dt>
                          <span class="visually-hidden">{{ 'products.product.price.regular_price' | t }}</span>
                        </dt>
                        <dd>
                          <s class="text-foreground/75">{{ line_item.original_line_price | money }}</s>
                        </dd>
                        <dt>
                          <span class="visually-hidden">{{ 'products.product.price.sale_price' | t }}</span>
                        </dt>
                        <dd>
                          <span>{{ line_item.final_line_price | money }}</span>
                        </dd>
                      </dl>
                    {%- else -%}
                      {{ line_item.original_line_price | money }}
                    {%- endif -%}
                  </td>
                </tr>
              {%- endfor -%}
            </tbody>
          </table>
        </div>
      </div>

      <div class="order-mobile-line-items flex flex-col gap-4 sm:hidden">
        {% for line_item in order.line_items %}
          <div class="border p-6 pl-4 rounded-block">
            {% render 'order-line-item', item: line_item %}
          </div>
        {% endfor %}
      </div>

      <div class="order-summary mt-8 md:mt-12 pr-4 lg:pr-6 grid grid-cols-[1fr_auto] justify-items-end gap-y-4 gap-x-12 w-full lg:gap-y-6">
        <div class="font-bold">{{ 'customer.order.subtotal' | t }}</div>
        <div class="text-right">{{ order.line_items_subtotal_price | money }}</div>

        {% if order.cart_level_discount_applications != blank %}
          {% for discount_application in order.cart_level_discount_applications %}
            <div class="font-bold flex items-center">
              <div class="icon-xs md:icon-sm shrink-0 mr-3">
                {% render 'icon-discount' %}
              </div>
              <span>{{ discount_application.title }}</span>
            </div>
            <div class="text-right">
              <div>
                <span> -{{ discount_application.total_allocated_amount | money }} </span>
              </div>
            </div>
          {% endfor %}
        {% endif %}

        {% for shipping_method in order.shipping_methods %}
          <div class="font-bold">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})</div>
          <div class="text-right">{{ shipping_method.price | money }}</div>
        {% endfor %}

        {% for tax_line in order.tax_lines %}
          <div class="font-bold">
            {{ 'customer.order.tax' | t }} ({{ tax_line.title }}
            {{ tax_line.rate | times: 100 }}%)
          </div>
          <div class="text-right">{{ tax_line.price | money }}</div>
        {% endfor %}

        {% if order.total_duties %}
          <div class="font-bold">{{ 'customer.order.total_duties' | t }}</div>
          <div class="text-right">{{ order.total_duties | money }}</div>
        {% endif %}

        <div class="font-bold">{{ 'customer.order.total' | t }}</div>
        <div class="text-right font-bold">{{ order.total_price | money_with_currency }}</div>
      </div>
    </div>

    <div class="order-adresses-details">
      <div class="p-6 border rounded-block">
        <h2 class="h5 mb-4">{{ 'customer.order.billing_address' | t }}</h2>
        {{ order.billing_address | format_address }}

        <p class="mt-6">
          <strong>{{ 'customer.order.payment_status' | t }}: </strong>
          {{ order.financial_status_label }}
        </p>
      </div>

      <div class="p-6 border rounded-block mt-8 xl:mt-12">
        <h2 class="h5 mb-4">{{ 'customer.order.shipping_address' | t }}</h2>
        {{ order.shipping_address | format_address }}

        <p class="mt-6">
          <strong>{{ 'customer.order.fulfillment_status' | t }}: </strong>
          {{ order.fulfillment_status_label }}
        </p>
      </div>
    </div>
  </div>
</div>
