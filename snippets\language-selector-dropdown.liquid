<dropdown-list
  focus-only
  list-title="{{ 'localization.language_label' | t }}"
  class="{{ class }}"
  offset="16"
  offset-cross="{{ offsetCross | default: "-24" }}"
  placement="{{ placement | default: "bottom-start" }}"
>
  <details class="relative">
    <summary class="flex items-center" data-dropdown-activator>
      <div
        class="
          {{ text_size_class | default: 'text-sm' }}
          {{ font_class | default: 'font-bold' }}
          whitespace-nowrap
        "
      >
        {{ localization.language.endonym_name | capitalize }}
      </div>

      <div class="grow"></div>

      <div class="collapse-chevron w-3 {{ collapse_spacing_class | default: 'ml-2' }}">
        {% render 'icon-chevron' %}
      </div>
    </summary>

    <div class="dropdown-menu py-2 locale-selector-dropdown">
      <div class="dropdown-list dropdown-list--small">
        {% render 'language-selector', label: false %}
      </div>
    </div>
  </details>
</dropdown-list>
