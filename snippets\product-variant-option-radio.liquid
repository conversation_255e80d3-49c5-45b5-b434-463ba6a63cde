<div
  class="variant-radio"
>
  <input
    type="radio"
    id="{{ section.id }}-{{ option.position }}-{{ index }}"
    name="{{ section.id }}-{{ option.name }}"
    value="{{ option_value | escape }}"
    form="{{ product_form_id }}"
    {% if option_value.selected %}
      checked
    {% endif %}
    class="peer visually-hidden {% unless option_value.available %} option-unavailable {% endunless %}"
    data-option-value-id="{{ option_value.id }}"
    data-variant-id="{{ option_value.variant.id }}"
    data-product-url="{{ option_value.product_url }}"
  >
  <label
    for="{{ section.id }}-{{ option.position }}-{{ index }}"
    class="block"
  >
    {{- option_value -}}
  </label>
</div>
