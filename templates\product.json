{"sections": {"main": {"type": "main-product", "blocks": {"custom_badges_gjaQEc": {"type": "custom_badges", "settings": {}}, "07b2f4eb-d5bd-46bf-8ef7-7c3a9dbff7b7": {"type": "title", "settings": {"heading_size": "h3", "content": "{{ product.title }}", "text_color": "rgba(0,0,0,0)"}}, "c0b62d1f-e562-4b86-84cb-fc22c849e93e": {"type": "price", "settings": {"show_taxes_notice": false, "use_bold_font": false}}, "5a84f252-bc58-4ca7-8b59-5a727a61c21f": {"type": "variant_picker", "settings": {"hide_sold_out_variants": false, "type": "radio", "color_picker_type": "radio-swatch", "block_style_rounded": false, "block_style": "outlined", "size_chart_page": "", "size_chart_option_name": "Size"}}, "custom_liquid_DPbPHM": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title == \"Analog Mixing & Mastering\" %}\n<div class=\"product-enhanced-section\">\n  <div class=\"product-info-card\">\n    <div class=\"product-info-header\">\n      <h3 class=\"product-info-title\">Service Information</h3>\n      <p class=\"product-info-description\">\n        <strong>Price for one song.</strong> For reamping, the price is listed per track.\n      </p>\n      <p class=\"product-info-discount\">\n        When you order five or more songs, a <strong>10% discount</strong> will be automatically applied to all services.\n      </p>\n    </div>\n\n    {% comment %}\n      Price Display Section\n      - Shows current product price\n      - Updates dynamically based on quantity and variant\n      - Visually prominent design\n    {% endcomment %}\n    <div class=\"product-price-display\">\n      <div class=\"price-row\">\n        <span class=\"price-label\">Price:</span>\n        <span class=\"current-price\"\n              data-product-id=\"{{ product.id }}\"\n              data-variant-id=\"{{ product.selected_or_first_available_variant.id }}\"\n              data-base-price=\"{{ product.selected_or_first_available_variant.price }}\">\n          <span class=\"original-price-display\">{{ product.selected_or_first_available_variant.price | money }}</span>\n        </span>\n        <span class=\"discount-indicator\">\n          (10% discount applied)\n        </span>\n      </div>\n      <div class=\"quantity-indicator\">\n        <span class=\"quantity-text\">Price for <span class=\"quantity-value\">1</span> item(s)</span>\n      </div>\n\n      {% comment %} We don't need a separate total price display as it would be redundant {% endcomment %}\n\n      {% comment %} Hidden variant price data for JavaScript to access {% endcomment %}\n      <div class=\"variant-prices\">\n        {% for variant in product.variants %}\n          <span class=\"variant-price\"\n                data-variant-id=\"{{ variant.id }}\"\n                data-price=\"{{ variant.price }}\"\n                data-compare-at-price=\"{% if variant.compare_at_price %}{{ variant.compare_at_price }}{% else %}0{% endif %}\">\n          </span>\n        {% endfor %}\n      </div>\n\n      {% comment %} Hidden price templates for all possible multipliers (1-30) for each variant {% endcomment %}\n      <div class=\"price-templates\">\n        {% for variant in product.variants %}\n          <div class=\"variant-price-templates\" data-variant-id=\"{{ variant.id }}\">\n            {% for i in (1..30) %}\n              {% assign price_template = variant.price | times: i %}\n              <span class=\"price-template\"\n                    data-quantity=\"{{ i }}\"\n                    data-price=\"{{ price_template }}\">\n                {{ price_template | money }}\n              </span>\n\n              {% comment %} Also create discounted price templates (10% off) {% endcomment %}\n              {% assign discounted_price = price_template | times: 0.9 %}\n              <span class=\"price-template-discounted\"\n                    data-quantity=\"{{ i }}\"\n                    data-price=\"{{ discounted_price }}\">\n                {{ discounted_price | money }}\n              </span>\n            {% endfor %}\n          </div>\n        {% endfor %}\n      </div>\n    </div>\n\n    {% comment %}\n      Integrated Quantity Selector Component\n      - Includes plus/minus buttons for incrementing/decrementing quantity\n      - Prevents negative quantities\n      - Updates cart when quantity changes\n      - Updates price display when quantity changes\n    {% endcomment %}\n    <div class=\"product-quantity-selector\">\n      <label for=\"quantity\" class=\"quantity-selector-label\">Quantity</label>\n      <div class=\"quantity-selector\">\n        <button type=\"button\" class=\"quantity-button quantity-down\">−</button>\n        <input type=\"number\" id=\"quantity\" name=\"quantity\" value=\"1\" min=\"1\" max=\"30\" class=\"quantity-input\">\n        <button type=\"button\" class=\"quantity-button quantity-up\">+</button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<p class=\"terms-of-service\">\n  Please read the <a href=\"https://developdevice.com/pages/terms-of-service-develop-device-mixing-mastering\" target=\"_blank\">Terms of Service</a> before ordering.\n</p>\n{% endif %}\n\n<style>\n  /* Enhanced Product Section Styles */\n  .product-enhanced-section {\n    margin: 20px 0;\n    width: 100%;\n    box-sizing: border-box;\n  }\n\n  .product-info-card {\n    background: linear-gradient(to bottom, #ffffff, #f9f9f9);\n    border-radius: 24px; /* UPDATED from 0 */\n    box-shadow: 0 4px 12px rgba(0,0,0,0.05);\n    padding: 24px;\n    border: 1px solid rgba(0,0,0,0.08);\n    transition: all 0.3s ease;\n  }\n\n  .product-info-card:hover {\n    box-shadow: 0 6px 16px rgba(0,0,0,0.08);\n    transform: translateY(-2px);\n  }\n\n  .product-info-header {\n    margin-bottom: 20px;\n  }\n\n  .product-info-title {\n    font-size: 20px;\n    font-weight: 700;\n    color: #333;\n    margin: 0 0 15px 0;\n    padding-bottom: 10px;\n    border-bottom: 2px solid rgba(0,0,0,0.06);\n  }\n\n  .product-info-description {\n    margin: 0;\n    padding: 0;\n    font-size: 15px;\n    line-height: 1.5;\n    color: #444;\n  }\n\n  .product-info-discount {\n    margin-top: 10px;\n    padding: 0;\n    font-size: 15px;\n    line-height: 1.5;\n    color: #444;\n  }\n\n  /* Price Display Styles */\n  .product-price-display {\n    background-color: rgba(0,0,0,0.02);\n    border-radius: 0; /* This remains 0 as per interpretation of \"Sekce 24px\" applying to the main card */\n    padding: 18px;\n    margin: 20px 0;\n    border: 1px solid rgba(0,0,0,0.05);\n  }\n\n  .price-row {\n    display: flex;\n    align-items: baseline;\n    flex-wrap: wrap;\n  }\n\n  .price-label {\n    font-weight: 600;\n    font-size: 16px;\n    color: #333;\n    margin-right: 10px;\n  }\n\n  .current-price {\n    font-size: 28px;\n    font-weight: 700;\n    color: #333;\n    transition: all 0.3s ease;\n  }\n\n  .discount-indicator {\n    margin-left: 12px;\n    font-size: 15px;\n    color: #e4584c;\n    font-weight: 600;\n    display: none;\n    background-color: rgba(228, 88, 76, 0.08);\n    padding: 4px 8px;\n    border-radius: 0; /* This remains 0 */\n  }\n\n  .quantity-indicator {\n    margin-top: 8px;\n    font-size: 14px;\n    color: #666;\n    display: none;\n  }\n\n  .total-price-container {\n    margin-top: 12px;\n    display: none;\n    background-color: rgba(0, 0, 0, 0.03);\n    padding: 10px 14px;\n    border-radius: 0; /* This remains 0 */\n    border-left: 3px solid #4a90e2;\n  }\n\n  .variant-prices, .price-templates {\n    display: none;\n  }\n\n  /* Quantity Selector Styles */\n  .product-quantity-selector {\n    margin-top: 20px;\n    padding-top: 20px;\n    border-top: 1px solid rgba(0,0,0,0.08);\n  }\n\n  .quantity-selector-label {\n    display: block;\n    margin-bottom: 12px;\n    font-weight: 600;\n    font-size: 15px;\n    color: #333;\n  }\n\n  .quantity-selector {\n    display: flex;\n    align-items: center;\n    max-width: 130px;\n    border-radius: 12px; /* UPDATED from 0 */\n    overflow: hidden;\n    box-shadow: 0 2px 6px rgba(0,0,0,0.08);\n  }\n\n  .quantity-button {\n    width: 40px;\n    height: 40px;\n    background: linear-gradient(to bottom, #ffffff, #f5f5f5);\n    border: 1px solid #ddd;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 18px;\n    font-weight: bold;\n    color: #555;\n    user-select: none;\n    transition: all 0.2s ease;\n  }\n\n  .quantity-down {\n    border-radius: 0; /* Remains 0; parent .quantity-selector clips it */\n  }\n\n  .quantity-up {\n    border-radius: 0; /* Remains 0; parent .quantity-selector clips it */\n  }\n\n  .quantity-input {\n    flex: 1;\n    height: 40px;\n    border: 1px solid #ddd;\n    border-left: none;\n    border-right: none;\n    text-align: center;\n    -moz-appearance: textfield;\n    margin: 0;\n    padding: 0 5px;\n    width: 50px;\n    font-size: 16px;\n    font-weight: 500;\n    color: #333;\n    background-color: #fff;\n  }\n\n  /* Hover and active states */\n  .quantity-button:hover {\n    background: linear-gradient(to bottom, #f8f8f8, #e9e9e9);\n    color: #000;\n  }\n\n  .quantity-button:active {\n    background: linear-gradient(to bottom, #e9e9e9, #f8f8f8);\n    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);\n  }\n\n  /* Focus styles for better accessibility */\n  .quantity-input:focus {\n    outline: none;\n    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.25);\n  }\n\n  .quantity-button:focus {\n    outline: none;\n    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.25);\n  }\n</style>\n\n<script>\n  document.addEventListener('DOMContentLoaded', function() {\n    // Get all quantity selectors on the page\n    const quantitySelectors = document.querySelectorAll('.quantity-selector');\n    const priceDisplay = document.querySelector('.current-price');\n    const discountIndicator = document.querySelector('.discount-indicator');\n    const quantityIndicator = document.querySelector('.quantity-indicator');\n    const quantityValue = document.querySelector('.quantity-value');\n\n    // Store variant prices in a map for quick access\n    const variantPrices = new Map();\n    document.querySelectorAll('.variant-price').forEach(variantEl => {\n      const variantId = variantEl.getAttribute('data-variant-id');\n      const price = parseFloat(variantEl.getAttribute('data-price'));\n      const compareAtPrice = parseFloat(variantEl.getAttribute('data-compare-at-price'));\n\n      variantPrices.set(variantId, {\n        price: price,\n        compareAtPrice: compareAtPrice > 0 ? compareAtPrice : null\n      });\n    });\n\n    // Get the current variant ID and price\n    let currentVariantId = priceDisplay ? priceDisplay.getAttribute('data-variant-id') : null;\n    let unitPrice = 0;\n\n    if (priceDisplay) {\n      // Get the base price from the data attribute (more reliable than parsing text)\n      unitPrice = parseFloat(priceDisplay.getAttribute('data-base-price')) || 0;\n    }\n\n    // We won't use a custom formatPrice function anymore\n    // Instead, we'll update the price display with the correct Liquid money filter\n    // which will be rendered server-side\n\n    // Function to update the price display based on quantity and variant\n    function updatePriceDisplay(quantity, variantId = currentVariantId) {\n      if (!priceDisplay) return;\n\n      // Get the price for the current variant\n      const variant = variantPrices.get(variantId) || { price: unitPrice, compareAtPrice: null };\n      unitPrice = variant.price;\n\n      let discountApplied = false;\n\n      // Apply 10% discount for 5 or more items\n      if (quantity >= 5) {\n        discountApplied = true;\n      }\n\n      // Find the variant-specific price templates container\n      const variantTemplatesContainer = document.querySelector(`.variant-price-templates[data-variant-id=\"${variantId}\"]`);\n\n      // Find the pre-rendered price template that matches our quantity\n      const selector = discountApplied ? '.price-template-discounted' : '.price-template';\n      let templates;\n\n      // If we have variant-specific templates, use those\n      if (variantTemplatesContainer) {\n        templates = variantTemplatesContainer.querySelectorAll(selector);\n      } else {\n        // Fallback to all templates if variant-specific ones aren't found\n        templates = document.querySelectorAll(selector);\n      }\n\n      let priceTemplate = null;\n\n      // Find the template that matches our quantity\n      for (const template of templates) {\n        if (parseInt(template.getAttribute('data-quantity'), 10) === quantity) {\n          priceTemplate = template;\n          break;\n        }\n      }\n\n      if (!priceTemplate && templates.length > 0) {\n        // If we don't have an exact match, use the first template as a fallback\n        priceTemplate = templates[0];\n      }\n\n      // Get the formatted price from the template\n      const formattedPrice = priceTemplate ? priceTemplate.textContent.trim() : '';\n\n      // Update the price display\n      const originalPriceDisplay = priceDisplay.querySelector('.original-price-display');\n      if (originalPriceDisplay && formattedPrice) {\n        originalPriceDisplay.innerHTML = formattedPrice;\n      }\n\n      // Add a highlight effect to the price\n      priceDisplay.classList.add('price-highlight');\n      setTimeout(() => priceDisplay.classList.remove('price-highlight'), 500);\n\n      // We don't need to show the total price container since it would display the same price\n      // and create a duplicate display\n\n      // Show/hide the discount indicator with animation\n      if (discountApplied) {\n        discountIndicator.style.display = 'inline-block';\n        discountIndicator.classList.add('fade-in');\n      } else {\n        discountIndicator.style.display = 'none';\n        discountIndicator.classList.remove('fade-in');\n      }\n\n      // Show/hide the quantity indicator with animation\n      if (quantity > 1) {\n        quantityIndicator.style.display = 'block';\n        quantityIndicator.classList.add('fade-in');\n        quantityValue.textContent = quantity;\n      } else {\n        quantityIndicator.style.display = 'none';\n        quantityIndicator.classList.remove('fade-in');\n      }\n    }\n\n    // Function to update the current variant\n    function updateCurrentVariant(variantId) {\n      if (!variantId || !priceDisplay) return;\n\n      currentVariantId = variantId;\n      priceDisplay.setAttribute('data-variant-id', variantId);\n\n      // Get the variant price\n      const variant = variantPrices.get(variantId);\n      if (variant) {\n        unitPrice = variant.price;\n        priceDisplay.setAttribute('data-base-price', unitPrice);\n\n        // Update the price display with the current quantity\n        const quantityInput = document.querySelector('.quantity-input');\n        const quantity = quantityInput ? parseInt(quantityInput.value, 10) || 1 : 1;\n        updatePriceDisplay(quantity, variantId);\n      }\n    }\n\n    // Listen for variant changes (typically from variant selectors)\n    document.addEventListener('variant:change', function(e) {\n      if (e.detail && e.detail.variant && e.detail.variant.id) {\n        updateCurrentVariant(e.detail.variant.id);\n      }\n    });\n\n    // Listen for product:variant:change event (used by some themes)\n    document.addEventListener('product:variant:change', function(e) {\n      if (e.detail && e.detail.variant && e.detail.variant.id) {\n        updateCurrentVariant(e.detail.variant.id);\n      } else if (e.detail && e.detail.variantId) {\n        updateCurrentVariant(e.detail.variantId);\n      }\n    });\n\n    // Also listen for changes to variant selector inputs\n    const variantSelectors = document.querySelectorAll('select[name=\"id\"], input[name=\"id\"]:checked, input[name=\"id\"][type=\"hidden\"]');\n    variantSelectors.forEach(selector => {\n      selector.addEventListener('change', function() {\n        updateCurrentVariant(this.value);\n      });\n    });\n\n    // Add mutation observer to watch for changes to variant selectors\n    // This catches cases where the value is changed programmatically without triggering a change event\n    variantSelectors.forEach(selector => {\n      const observer = new MutationObserver(function(mutations) {\n        mutations.forEach(function(mutation) {\n          if (mutation.type === 'attributes' && mutation.attributeName === 'value') {\n            updateCurrentVariant(selector.value);\n          }\n        });\n      });\n\n      observer.observe(selector, { attributes: true });\n    });\n\n    quantitySelectors.forEach(function(selector) {\n      const minusButton = selector.querySelector('.quantity-down');\n      const plusButton = selector.querySelector('.quantity-up');\n      const input = selector.querySelector('.quantity-input');\n\n      if (!minusButton || !plusButton || !input) return;\n\n      // Function to update quantity with visual feedback\n      function updateQuantity(change) {\n        let currentValue = parseInt(input.value, 10) || 1;\n        let newValue = currentValue + change;\n\n        // Prevent negative quantities\n        if (newValue < 1) {\n          newValue = 1;\n          // Add subtle shake animation for visual feedback when trying to go below 1\n          minusButton.classList.add('shake-animation');\n          setTimeout(() => minusButton.classList.remove('shake-animation'), 500);\n          return;\n        }\n\n        // Prevent quantities above 30\n        if (newValue > 30) {\n          newValue = 30;\n          // Add subtle shake animation for visual feedback when trying to go above 30\n          plusButton.classList.add('shake-animation');\n          setTimeout(() => plusButton.classList.remove('shake-animation'), 500);\n          return;\n        }\n\n        // Update input value with a subtle highlight effect\n        input.value = newValue;\n        input.classList.add('highlight');\n        setTimeout(() => input.classList.remove('highlight'), 300);\n\n        // Update the price display with current variant and new quantity\n        updatePriceDisplay(newValue, currentVariantId);\n\n        // Trigger change event to notify any listeners\n        const event = new Event('change', { bubbles: true });\n        input.dispatchEvent(event);\n\n        // Directly update all form quantity inputs to ensure they have the latest value\n        updateAllFormQuantityInputs(newValue);\n      }\n\n      // Add event listeners to buttons with visual feedback\n      minusButton.addEventListener('click', function() {\n        updateQuantity(-1);\n        // Add tactile feedback\n        this.classList.add('active');\n        setTimeout(() => this.classList.remove('active'), 150);\n      });\n\n      plusButton.addEventListener('click', function() {\n        updateQuantity(1);\n        // Add tactile feedback\n        this.classList.add('active');\n        setTimeout(() => this.classList.remove('active'), 150);\n      });\n\n      // Add event listener to input to validate manual entry\n      input.addEventListener('change', function() {\n        let value = parseInt(input.value, 10);\n\n        // Ensure value is a positive integer\n        if (isNaN(value) || value < 1) {\n          value = 1;\n          input.value = value;\n          // Add subtle shake animation for visual feedback\n          input.classList.add('shake-animation');\n          setTimeout(() => input.classList.remove('shake-animation'), 500);\n        }\n\n        // Ensure value doesn't exceed maximum (30)\n        if (value > 30) {\n          value = 30;\n          input.value = value;\n          // Add subtle shake animation for visual feedback\n          input.classList.add('shake-animation');\n          setTimeout(() => input.classList.remove('shake-animation'), 500);\n        }\n\n        // Update the price display with current variant and new quantity\n        updatePriceDisplay(value, currentVariantId);\n\n        // Update all form quantity inputs\n        updateAllFormQuantityInputs(value);\n      });\n\n      // Prevent the default behavior of number inputs in Firefox\n      input.addEventListener('keydown', function(e) {\n        if (e.key === 'ArrowUp') {\n          e.preventDefault();\n          updateQuantity(1);\n        } else if (e.key === 'ArrowDown') {\n          e.preventDefault();\n          updateQuantity(-1);\n        }\n      });\n\n      // Initialize price display with current quantity\n      updatePriceDisplay(parseInt(input.value, 10) || 1, currentVariantId);\n    });\n\n    // Function to update all form quantity inputs\n    function updateAllFormQuantityInputs(value) {\n      const productForms = document.querySelectorAll('form[action*=\"/cart/add\"]');\n      productForms.forEach(function(form) {\n        const formQuantityInput = form.querySelector('input[name=\"quantity\"]');\n        if (formQuantityInput) {\n          formQuantityInput.value = value;\n        } else {\n          // If no quantity input exists in the form, create one\n          const hiddenInput = document.createElement('input');\n          hiddenInput.type = 'hidden';\n          hiddenInput.name = 'quantity';\n          hiddenInput.value = value;\n          form.appendChild(hiddenInput);\n        }\n      });\n    }\n\n    // Initial setup - find existing quantity inputs in forms and sync with our custom input\n    const productForms = document.querySelectorAll('form[action*=\"/cart/add\"]');\n    const quantityInput = document.querySelector('.quantity-input');\n\n    if (productForms.length > 0 && quantityInput) {\n      // Check if any form already has a quantity value set\n      let initialQuantity = 1;\n\n      productForms.forEach(function(form) {\n        const formQuantityInput = form.querySelector('input[name=\"quantity\"]');\n        if (formQuantityInput && formQuantityInput.value && parseInt(formQuantityInput.value, 10) > 1) {\n          initialQuantity = parseInt(formQuantityInput.value, 10);\n        }\n      });\n\n      // Set our custom input to match any existing quantity\n      quantityInput.value = initialQuantity;\n\n      // Update price display with initial quantity and current variant\n      updatePriceDisplay(initialQuantity, currentVariantId);\n\n      // Make sure all forms have the correct quantity\n      updateAllFormQuantityInputs(initialQuantity);\n\n      // Add event listeners to all add-to-cart buttons to ensure quantity is correct before submission\n      productForms.forEach(function(form) {\n        form.addEventListener('submit', function(e) {\n          // Get the current value from our custom input\n          const currentQuantity = parseInt(quantityInput.value, 10) || 1;\n\n          // Update the form's quantity input one last time before submission\n          const formQuantityInput = form.querySelector('input[name=\"quantity\"]');\n          if (formQuantityInput) {\n            formQuantityInput.value = currentQuantity;\n          } else {\n            // If no quantity input exists in the form, create one\n            const hiddenInput = document.createElement('input');\n            hiddenInput.type = 'hidden';\n            hiddenInput.name = 'quantity';\n            hiddenInput.value = currentQuantity;\n            form.appendChild(hiddenInput);\n          }\n        });\n      });\n    }\n\n    // Check for variant selectors that might be using radio buttons or other custom selectors\n    // This is needed because some themes use custom variant selectors that don't trigger standard change events\n    const optionSelectors = document.querySelectorAll('.single-option-selector, [data-option-selector], [data-product-option], .swatch-element input, .color-swatch input, .variant-input, [data-index=\"option1\"], [data-index=\"option2\"], [data-index=\"option3\"]');\n    optionSelectors.forEach(selector => {\n      selector.addEventListener('change', function() {\n        // Give the theme's JavaScript time to update the main variant selector\n        setTimeout(() => {\n          const variantInput = document.querySelector('select[name=\"id\"], input[name=\"id\"]:checked, input[name=\"id\"][type=\"hidden\"]');\n          if (variantInput) {\n            updateCurrentVariant(variantInput.value);\n          }\n        }, 100);\n      });\n    });\n\n    // Listen for click events on swatch elements that might not trigger change events\n    const swatchElements = document.querySelectorAll('.swatch-element, .color-swatch, .variant-swatch, .variant-input-wrap label');\n    swatchElements.forEach(swatch => {\n      swatch.addEventListener('click', function() {\n        // Give the theme's JavaScript time to update the main variant selector\n        setTimeout(() => {\n          const variantInput = document.querySelector('select[name=\"id\"], input[name=\"id\"]:checked, input[name=\"id\"][type=\"hidden\"]');\n          if (variantInput) {\n            updateCurrentVariant(variantInput.value);\n          }\n        }, 100);\n      });\n    });\n\n    // Initial check for the current variant\n    const initialVariantInput = document.querySelector('select[name=\"id\"], input[name=\"id\"]:checked, input[name=\"id\"][type=\"hidden\"]');\n    if (initialVariantInput) {\n      updateCurrentVariant(initialVariantInput.value);\n    }\n\n    // Add a global click handler to catch any variant selection that might have been missed\n    document.addEventListener('click', function(e) {\n      // Check if the click was on or inside a variant selection element\n      if (e.target.closest('.variant-input-wrap, .swatch, .swatch-element, .color-swatch, .selector-wrapper')) {\n        // Give the theme's JavaScript time to update the main variant selector\n        setTimeout(() => {\n          const variantInput = document.querySelector('select[name=\"id\"], input[name=\"id\"]:checked, input[name=\"id\"][type=\"hidden\"]');\n          if (variantInput) {\n            updateCurrentVariant(variantInput.value);\n          }\n        }, 100);\n      }\n    });\n  });\n</script>\n\n<style>\n  /* Animation styles */\n  @keyframes highlight {\n    0% { background-color: #fff; }\n    50% { background-color: rgba(0, 102, 204, 0.1); }\n    100% { background-color: #fff; }\n  }\n\n  @keyframes shake {\n    0% { transform: translateX(0); }\n    25% { transform: translateX(-3px); }\n    50% { transform: translateX(3px); }\n    75% { transform: translateX(-3px); }\n    100% { transform: translateX(0); }\n  }\n\n  @keyframes priceHighlight {\n    0% { color: #333; transform: scale(1); }\n    30% { color: #e4584c; transform: scale(1.05); }\n    100% { color: #333; transform: scale(1); }\n  }\n\n  @keyframes fadeIn {\n    from { opacity: 0; transform: translateY(-5px); }\n    to { opacity: 1; transform: translateY(0); }\n  }\n\n  @keyframes pulse {\n    0% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4); }\n    70% { box-shadow: 0 0 0 6px rgba(74, 144, 226, 0); }\n    100% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0); }\n  }\n\n  .highlight {\n    animation: highlight 0.3s ease-in-out;\n  }\n\n  .shake-animation {\n    animation: shake 0.4s ease-in-out;\n  }\n\n  .active {\n    transform: scale(0.95);\n  }\n\n  .price-highlight {\n    animation: priceHighlight 0.7s ease-in-out;\n  }\n\n  .fade-in {\n    animation: fadeIn 0.4s ease-out forwards;\n  }\n\n  .pulse {\n    animation: pulse 1.5s infinite;\n  }\n\n  /* Terms of Service styling */\n  .terms-of-service {\n    margin-top: 24px;\n    padding: 0;\n    font-size: 14px;\n    color: #555;\n    text-align: left;\n    line-height: 1.5;\n  }\n\n  .terms-of-service a {\n    color: #4a90e2;\n    font-weight: 600;\n    text-decoration: none;\n    transition: all 0.2s ease;\n    border-bottom: 1px solid transparent;\n  }\n\n  .terms-of-service a:hover {\n    color: #2a6fc9;\n    border-bottom: 1px solid #2a6fc9;\n  }\n</style>", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_LHpkF6": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if customer %}\n  {% assign has_bought_product = false %}\n  {% assign purchase_order = null %}\n  \n  {% for order in customer.orders %}\n    {% for line_item in order.line_items %}\n      {% if line_item.product_id == product.id %}\n        {% assign has_bought_product = true %}\n        {% assign purchase_order = order %}\n        {% break %}\n      {% endif %}\n    {% endfor %}\n    \n    {% if has_bought_product %}\n      {% break %}\n    {% endif %}\n  {% endfor %}\n  \n  {% if has_bought_product and purchase_order %}\n    <div class=\"already-purchased-notice\" style=\"display: none;\">\n      <p>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"icon glow\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\n        </svg>\n        You've already purchased this product (Order #{{ purchase_order.order_number }}) on {{ purchase_order.created_at | date: \"%B %d, %Y\" }}\n      </p>\n    </div>\n    <style>\n      .already-purchased-notice {\n        background-color: #ffffff;\n        border: 1px solid #000000;\n        border-radius: 24px; /* <--- Změněno z 0 na 24px */\n        padding: 1rem;\n        margin-bottom: 1.5rem;\n        display: flex;\n        align-items: center;\n      }\n      .already-purchased-notice p {\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n        margin: 0;\n        color: #000000;\n        font-size: 0.875rem;\n      }\n      .already-purchased-notice .icon {\n        width: 1.25rem;\n        height: 1.25rem;\n        color: green;\n      }\n      .already-purchased-notice .icon.glow {\n        filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.8));\n        animation: pulse 3s ease-in-out infinite;\n      }\n      @keyframes pulse {\n        0% {\n          filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.6));\n          transform: scale(1);\n        }\n        25% {\n          filter: drop-shadow(0 0 12px rgba(0, 0, 0, 1));\n          transform: scale(1.05);\n        }\n        50% {\n          filter: drop-shadow(0 0 16px rgba(0, 0, 0, 0.9));\n          transform: scale(1.1);\n        }\n        75% {\n          filter: drop-shadow(0 0 12px rgba(0, 0, 0, 1));\n          transform: scale(1.05);\n        }\n        100% {\n          filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.6));\n          transform: scale(1);\n        }\n      }\n\n      /* Pro tlačítka byste přidali podobný styl, např.:\n      .nejake-tlacitko {\n        border-radius: 12px;\n        // ... další styly pro tlačítko\n      }\n      */\n    </style>\n    <script>\n      document.addEventListener('DOMContentLoaded', function() {\n        const notice = document.querySelector('.already-purchased-notice');\n        if (notice) {\n          notice.style.display = 'block';\n        }\n      });\n    </script>\n  {% endif %}\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_iF8XyT": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.type == \"Neural DSP Quad Cortex Captures & Presets\" %}\n<div class=\"qc-neural-compatibility\" style=\"max-width: 1500px; margin: 40px auto 20px; background: #2C2C2C; border-left: 3px solid #444444; padding: 16px 20px; display: flex; align-items: center; border-radius: 24px; color: #CCCCCC;\">\n    <svg style=\"width: 24px; height: 24px; margin-right: 15px; flex-shrink: 0;\" viewBox=\"0 0 24 24\" fill=\"#E0E0E0\">\n        <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n    </svg>\n    <div style=\"flex: 1;\">\n        <span style=\"font-weight: 600; color: #FFFFFF;\">FULL COMPATIBILITY:</span> All neural captures are fully compatible with Quad Cortex and Nano Cortex devices.\n    </div>\n</div>\n\n<!-- NEW UPDATE NOTICE -->\n<div class=\"qc-neural-update-notice\" style=\"max-width: 1500px; margin: 20px auto 40px; background: linear-gradient(135deg, #1a472a 0%, #2d5a3d 100%); border: 2px solid #4CAF50; padding: 20px 24px; border-radius: 24px; box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);\">\n    <div style=\"display: flex; align-items: center;\">\n        <div style=\"background: #4CAF50; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; margin-right: 20px; flex-shrink: 0;\">\n            <svg style=\"width: 28px; height: 28px;\" viewBox=\"0 0 24 24\" fill=\"white\">\n                <path d=\"M7.5 5.6L5 7l1.4-2.5L5 2l2.5 1.4L10 2 8.6 4.5 10 7 7.5 5.6zm12 9.8L22 14l-1.4 2.5L22 19l-2.5-1.4L17 19l1.4-2.5L17 14l2.5 1.4zM22 2l-2.5 1.4L17 2l1.4 2.5L17 7l2.5-1.4L22 7l-1.4-2.5L22 2zM13.34 12.78l-2.12-2.12L3.5 18.38l2.12 2.12 7.72-7.72zm3.54-3.54l-1.41 1.41 1.41 1.41 1.42-1.41a3.002 3.002 0 0 0-.02-4.24l.02-.02a3 3 0 0 0-4.24 0l-.02.02 1.43 1.42 1.41-1.41z\"/>\n            </svg>\n        </div>\n        <div style=\"flex: 1;\">\n            <h3 style=\"margin: 0 0 8px 0; color: #FFFFFF; font-size: 20px; font-weight: 600; letter-spacing: 0.5px;\">🎉 Great News! Faster Delivery Now Available</h3>\n            <p style=\"margin: 0; color: #E8F5E9; font-size: 16px; line-height: 1.6;\">\n                Thanks to <strong style=\"color: #FFFFFF;\">significant improvements by Neural DSP</strong> in their Cortex Cloud delivery system, \n                I can now process and deliver your orders <strong style=\"color: #FFFFFF;\">much faster than before</strong>. \n                This enhanced system allows me to handle more orders in less time, ensuring you get your neural captures sooner!\n            </p>\n        </div>\n    </div>\n</div>\n\n<div class=\"qc-neural-container\" style=\"max-width: 1500px; margin: 40px auto; box-shadow: 0 6px 28px rgba(0,0,0,0.25); overflow: hidden; border-radius: 24px;\">\n    <div class=\"qc-neural-header\" style=\"background: black; color: white; padding: 18px 24px;\">\n        <h2 style=\"margin: 0; font-size: 18px; font-weight: 500; letter-spacing: 0.3px;\">⚠️ Important Order Instructions</h2>\n    </div>\n    \n    <div class=\"qc-neural-content\" style=\"padding: 24px 30px 30px; background: #1E1E1E; color: #B0B0B0;\">\n        <div class=\"qc-neural-section\" style=\"margin-bottom: 32px;\">\n            <h3 class=\"qc-neural-section-title\" style=\"font-size: 15px; margin: 0 0 16px 0; font-weight: 600; color: #E0E0E0; text-transform: uppercase; letter-spacing: 1px;\">Required Steps</h3>\n            \n            <div class=\"qc-neural-step\" style=\"display: flex; margin-bottom: 14px; align-items: flex-start;\">\n                <span style=\"display: inline-flex; align-items: center; justify-content: center; flex-shrink: 0; background: #FFFFFF; color: #000000; width: 22px; height: 22px; font-size: 12px; margin-right: 12px; border-radius: 50%;\">1</span>\n                <div style=\"padding-top: 2px;\">Enter your <span style=\"font-weight: 500; color: #DCDCDC;\">Cortex Cloud nickname</span> below </div>\n            </div>\n            \n            <div class=\"qc-neural-step\" style=\"display: flex; margin-bottom: 18px; align-items: flex-start;\">\n                <span style=\"display: inline-flex; align-items: center; justify-content: center; flex-shrink: 0; background: #FFFFFF; color: #000000; width: 22px; height: 22px; font-size: 12px; margin-right: 12px; border-radius: 50%;\">2</span>\n                <div style=\"padding-top: 2px;\">Follow the profile <span style=\"font-weight: 500; color: #DCDCDC;\">\"DevelopDevice\"</span> on Cortex Cloud</div>\n            </div>\n            \n            <div style=\"background: #2A2A2A; padding: 12px 16px; margin-top: 6px; border-left: 2px solid #FFA500; font-size: 14px;\">\n                <p style=\"margin: 0; font-weight: 500; color: #FAFAFA;\">⚠️ Without completing these steps, it will not be possible to deliver your neural captures.</p>\n            </div>\n        </div>\n        \n        <div class=\"qc-neural-section\">\n            <h3 class=\"qc-neural-section-title\" style=\"font-size: 15px; margin: 0 0 16px 0; font-weight: 600; color: #E0E0E0; text-transform: uppercase; letter-spacing: 1px;\">Delivery Timeline</h3>\n            \n            <div style=\"margin-bottom: 8px;\">\n                <span style=\"font-weight: 500; color: #DCDCDC; display: inline-block; width: 150px;\">Standard delivery:</span>\n                <span><strong style=\"color: #4CAF50;\">Much faster now!</strong> Usually within 12-24 hours</span>\n            </div>\n            \n            <div style=\"margin-bottom: 16px;\">\n                <span style=\"font-weight: 500; color: #DCDCDC; display: inline-block; width: 150px;\">Typical delivery:</span>\n                <span>Often <strong style=\"color: #4CAF50;\">within a few hours</strong> during business hours!</span>\n            </div>\n            \n            <div style=\"font-size: 13px; color: #999999; background: #2A2A2A; padding: 12px 16px; line-height: 1.5;\">\n                Note: Thanks to the improved Neural DSP system, delivery times are now significantly reduced. Manual processing still applies, but the enhanced workflow allows for much quicker turnaround.\n            </div>\n        </div>\n    </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_Kb99Ae": {"type": "custom_liquid", "settings": {"custom_liquid": "{% comment %}\n  Display system requirements based on product name\n  Include this snippet in your product template with: {% render 'product-requirements' %}\n{% endcomment %}\n\n{%- if product.title contains 'HumBeat' or product.title contains 'ShredSync' or product.title contains 'StringSync' -%}\n  <div class=\"product-requirements\">\n    <p class=\"requirements-heading\">👉 For Windows and MacOS 👈</p>\n    <ul>\n      <li>MacOS 10.13 (High Sierra) and later for computers with Intel processors</li>\n      <li>MacOS 11.0 (Big Sur) and later for computers with Apple Silicon processors</li>\n      <li>Windows 10/11</li>\n    </ul>\n  </div>\n{%- elsif product.title contains 'IR-Alchemist' -%}\n  <div class=\"product-requirements\">\n    <p class=\"requirements-heading\">👉 Windows Only 👈</p>\n    <ul>\n      <li>MacOS version coming soon!</li>\n    </ul>\n  </div>\n{%- endif -%}\n\n<style>\n  .product-requirements {\n    border: 1px solid white;\n    border-radius: 24px; /* <<< Změněno na 24px pro sekci */\n    padding: 15px;\n    margin: 20px 0;\n    background-color: #f4f4f4;\n    color: black;\n    text-align: left;\n  }\n  .requirements-heading {\n    font-weight: bold;\n    text-align: left;\n    margin-bottom: 10px;\n  }\n  .product-requirements ul {\n    margin: 0;\n    padding-left: 20px;\n  }\n  .product-requirements li {\n    margin-bottom: 5px;\n  }\n\n  /* Styl pro tlačítka (pokud budou přidána do této sekce) */\n  .product-requirements .custom-button, /* Pro tlačítka s třídou .custom-button */\n  .product-requirements button {       /* Pro standardní <button> elementy */\n    border-radius: 12px; /* <<< Přidáno 12px pro tlačítka */\n    /* Poznámka: Podle potřeby doplňte další styly pro tlačítka,\n       jako např. padding, background-color, color atd.\n       Například:\n       padding: 8px 16px;\n       background-color: #007bff;\n       color: white;\n       border: none;\n       cursor: pointer;\n    */\n  }\n</style>", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_HBB6V3": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains '<PERSON><PERSON>' %}\n<div style=\"\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\n  border-radius: 16px;\n  padding: 32px;\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  margin-top: 24px;\n\">\n  <!-- Decorative gradient orb -->\n  <div style=\"\n    position: absolute;\n    top: -50px;\n    right: -50px;\n    width: 150px;\n    height: 150px;\n    background: radial-gradient(circle, #f39c12 0%, #e67e22 50%, transparent 70%);\n    border-radius: 50%;\n    opacity: 0.3;\n    filter: blur(40px);\n  \"></div>\n  \n  <!-- Icon and heading -->\n  <div style=\"display: flex; align-items: center; margin-bottom: 20px;\">\n    <div style=\"\n      width: 48px;\n      height: 48px;\n      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);\n      border-radius: 12px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 16px;\n      box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);\n    \">\n      <!-- Compatibility icon -->\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"white\" stroke-width=\"2\">\n        <path d=\"M12 2L2 7L12 12L22 7L12 2Z\"></path>\n        <path d=\"M2 17L12 22L22 17\"></path>\n        <path d=\"M2 12L12 17L22 12\"></path>\n      </svg>\n    </div>\n    <h3 style=\"\n      color: #f39c12;\n      margin: 0;\n      font-size: 24px;\n      font-weight: 600;\n      letter-spacing: -0.5px;\n    \">Full Compatibility Guaranteed</h3>\n  </div>\n  \n  <!-- Content -->\n  <div style=\"color: #e0e0e0; font-size: 16px; line-height: 1.8;\">\n    <div style=\"\n      background: rgba(255, 255, 255, 0.05);\n      border-left: 3px solid #f39c12;\n      padding: 16px 20px;\n      border-radius: 8px;\n      margin-bottom: 16px;\n      backdrop-filter: blur(10px);\n    \">\n      <p style=\"margin: 0 0 12px 0;\">\n        <strong style=\"color: #ffffff;\">Kemper Profiler MK I</strong> profiles work seamlessly with the new <strong style=\"color: #ffffff;\">Kemper Profiler MK II</strong>.\n      </p>\n      <p style=\"margin: 0;\">\n        MK II profiles are backward compatible with MK I units — some advanced features may require newer hardware.\n      </p>\n    </div>\n    \n    <div style=\"\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 20px;\n      background: rgba(243, 156, 18, 0.1);\n      border-radius: 8px;\n      border: 1px solid rgba(243, 156, 18, 0.2);\n    \">\n      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#f39c12\" stroke-width=\"2\">\n        <path d=\"M20 6L9 17L4 12\"></path>\n      </svg>\n      <span style=\"color: #f39c12; font-weight: 500;\">\n        Use all your profiles across both generations — no conversion needed\n      </span>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_TVhBGy": {"type": "custom_liquid", "settings": {"custom_liquid": "{% comment %}\n  Line 6 Helix Stadium/Stadium XL Compatibility Information Block\n  This block displays only on products with \"Line 6 Helix\" in the title\n{% endcomment %}\n\n{% if product.title contains 'Line 6 Helix' %}\n  <div class=\"helix-compatibility-notice\" style=\"background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n    <div style=\"display: flex; align-items: center; gap: 12px;\">\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"flex-shrink: 0;\">\n        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#0066cc\" stroke-width=\"2\"/>\n        <path d=\"M12 8v4M12 16h.01\" stroke=\"#0066cc\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n      </svg>\n      <div>\n        <h4 style=\"margin: 0 0 8px 0; color: #0066cc; font-weight: 600; font-size: 16px;\">\n          Compatible with Helix Stadium Series\n        </h4>\n        <p style=\"margin: 0; color: #333; font-size: 14px; line-height: 1.5;\">\n          <strong>Good news!</strong> All existing presets for Line 6 Helix are fully compatible with the new Helix Stadium and Stadium XL models. You can use these presets on your new Stadium series device without any modifications.\n        </p>\n      </div>\n    </div>\n  </div>\n{% endif %}\n\n{% comment %}\n  Alternative version with more compact styling\n{% endcomment %}\n\n{% comment %}\n{% if product.title contains 'Line 6 Helix' %}\n  <div class=\"helix-compatibility-badge\" style=\"background-color: #28a745; color: white; padding: 12px 16px; border-radius: 6px; margin: 15px 0; display: inline-flex; align-items: center; gap: 8px;\">\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"/>\n    </svg>\n    <span style=\"font-weight: 500;\">✓ Compatible with Helix Stadium & Stadium XL</span>\n  </div>\n{% endif %}\n{% endcomment %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "globo_product_option_app_block_6jhCWt": {"type": "shopify://apps/globo-product-option/blocks/app-block/fdc9fad5-1a0f-4bd4-9c8a-1af6a6eef6b8", "settings": {}}, "a730fe0e-baa3-47bc-90c9-21c5a5d75f2d": {"type": "buy_buttons", "settings": {"show_dynamic_checkout_buttons": true, "show_gift_card_recipient": true, "add_to_cart_background_color": "rgba(0,0,0,0)", "add_to_cart_text_color": "rgba(0,0,0,0)", "buy_it_now_background_color": "rgba(0,0,0,0)", "buy_it_now_text_color": "rgba(0,0,0,0)"}}, "custom_liquid_wWVPBw": {"type": "custom_liquid", "settings": {"custom_liquid": "{% comment %}\n  Dynamic Product Information Section - Shopify Integration\n  This section displays detailed information about the All-Access Pass\n  ONLY for \"All-Access Pass\" product\n{% endcomment %}\n\n{% comment %}\n  Check if current product is \"All-Access Pass\" before displaying anything\n{% endcomment %}\n{% if product.title == \"All-Access Pass\" %}\n\n<style>\n  :root {\n    /* Info Section Variables */\n    --bubble-space-xs: 0.25rem;\n    --bubble-space-sm: 0.375rem;\n    --bubble-space-md: 0.5rem;\n    --bubble-space-lg: 0.75rem;\n    --bubble-space-xl: 1rem;\n\n    --bubble-color-primary: #7D427D;\n    --bubble-color-secondary: #1980B7;\n    --bubble-color-text: #ffffff;\n    --bubble-color-text-muted: rgba(255, 255, 255, 0.85);\n    --bubble-color-bg-dark: #121212;\n    --bubble-color-bg-medium: #2a2a2a;\n    --bubble-color-bg-light: rgba(255, 255, 255, 0.05);\n    --bubble-color-bg-hover: rgba(255, 255, 255, 0.1);\n    --bubble-border-color: rgba(255, 255, 255, 0.15);\n    --bubble-border-color-hover: rgba(255, 255, 255, 0.25);\n\n    --bubble-font-size-xs: 0.7rem;\n    --bubble-font-size-sm: 0.8rem;\n    --bubble-font-size-base: 0.875rem;\n\n    --bubble-border-radius: 20px;\n    --bubble-border-radius-lg: 25px;\n  }\n\n  /* Main Info Section */\n  .categories-info-section {\n    padding: var(--bubble-space-xl) var(--bubble-space-md);\n    background: linear-gradient(135deg, var(--bubble-color-bg-dark) 0%, var(--bubble-color-bg-medium) 100%);\n    border-radius: 16px;\n    margin: var(--bubble-space-xl) auto;\n    max-width: 900px;\n    position: relative;\n    overflow: hidden;\n  }\n\n  @media (min-width: 768px) {\n    .categories-info-section {\n      padding: var(--bubble-space-xl);\n    }\n  }\n\n  .info-header {\n    text-align: center;\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  .info-title {\n    font-size: 1.5rem;\n    font-weight: 800;\n    color: var(--bubble-color-text);\n    margin: 0 0 var(--bubble-space-sm) 0;\n    background: linear-gradient(90deg, var(--bubble-color-secondary), var(--bubble-color-primary));\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n\n  @media (min-width: 768px) {\n    .info-title {\n      font-size: 1.75rem;\n    }\n  }\n\n  .info-subtitle {\n    font-size: var(--bubble-font-size-base);\n    color: var(--bubble-color-text-muted);\n    margin: 0;\n  }\n\n  /* Platform Coverage - Full Width Section */\n  .platform-coverage-section {\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  .platform-coverage-section .info-card {\n    background: var(--bubble-color-bg-light);\n    border: 1px solid var(--bubble-border-color);\n    border-radius: 12px;\n    padding: var(--bubble-space-lg);\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n  }\n\n  .platform-coverage-section .info-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n\n  /* Three Column Grid for Other Cards - Optimized for 900px */\n  .info-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: var(--bubble-space-md);\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  @media (max-width: 900px) {\n    .info-grid {\n      grid-template-columns: 1fr;\n      gap: var(--bubble-space-md);\n    }\n  }\n\n  @media (min-width: 901px) and (max-width: 1200px) {\n    .info-grid {\n      grid-template-columns: repeat(3, minmax(250px, 1fr));\n      gap: var(--bubble-space-sm);\n    }\n  }\n\n  @media (max-width: 767px) {\n    .info-grid {\n      grid-template-columns: 1fr;\n      gap: var(--bubble-space-md);\n    }\n  }\n\n  .info-card {\n    background: var(--bubble-color-bg-light);\n    border: 1px solid var(--bubble-border-color);\n    border-radius: 12px;\n    padding: var(--bubble-space-lg);\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n  }\n\n  .info-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n\n  .info-card-header {\n    display: flex;\n    align-items: center;\n    gap: var(--bubble-space-md);\n    margin-bottom: var(--bubble-space-lg);\n  }\n\n  .info-icon {\n    font-size: 1.5rem;\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: linear-gradient(135deg, var(--bubble-color-primary), var(--bubble-color-secondary));\n    border-radius: 10px;\n    flex-shrink: 0;\n  }\n\n  .info-card-header h3 {\n    font-size: 1.1rem;\n    font-weight: 700;\n    color: var(--bubble-color-text);\n    margin: 0;\n  }\n\n  .info-card-content {\n    color: var(--bubble-color-text-muted);\n  }\n\n  /* Stats Grid - Optimized for smaller width */\n  .info-stats {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: var(--bubble-space-sm);\n    margin-bottom: var(--bubble-space-lg);\n  }\n\n  @media (max-width: 600px) {\n    .info-stats {\n      grid-template-columns: 1fr;\n      gap: var(--bubble-space-sm);\n    }\n  }\n\n  .stat-item {\n    text-align: center;\n    padding: var(--bubble-space-md);\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: 8px;\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n\n  .stat-number {\n    display: block;\n    font-size: 1.5rem;\n    font-weight: 900;\n    color: var(--bubble-color-secondary);\n    margin-bottom: var(--bubble-space-xs);\n  }\n\n  .stat-label {\n    font-size: var(--bubble-font-size-sm);\n    color: var(--bubble-color-text-muted);\n  }\n\n  /* Platform Highlights - Better spacing for 900px */\n  .platform-highlights {\n    display: flex;\n    flex-wrap: wrap;\n    gap: var(--bubble-space-xs);\n    justify-content: center;\n  }\n\n  @media (min-width: 768px) {\n    .platform-highlights {\n      gap: var(--bubble-space-sm);\n    }\n  }\n\n  .highlight-tag {\n    padding: var(--bubble-space-xs) var(--bubble-space-sm);\n    background: rgba(255, 255, 255, 0.1);\n    border: 1px solid var(--bubble-border-color);\n    border-radius: 15px;\n    font-size: var(--bubble-font-size-xs);\n    color: var(--bubble-color-text-muted);\n    font-weight: 500;\n    text-align: center;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  @media (max-width: 600px) {\n    .highlight-tag {\n      font-size: 0.65rem;\n      padding: 2px var(--bubble-space-xs);\n    }\n  }\n\n  .highlight-tag.popular {\n    background: linear-gradient(135deg, var(--bubble-color-primary), #9d67ad);\n    color: var(--bubble-color-text);\n    border-color: var(--bubble-color-primary);\n  }\n\n  /* Value Breakdown */\n  .value-breakdown {\n    display: flex;\n    flex-direction: column;\n    gap: var(--bubble-space-lg);\n  }\n\n  .value-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    padding: var(--bubble-space-md);\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: 8px;\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n\n  .value-amount {\n    font-size: 1.5rem;\n    font-weight: 900;\n    color: var(--bubble-color-secondary);\n    margin-bottom: var(--bubble-space-xs);\n  }\n\n  .value-description {\n    font-size: var(--bubble-font-size-sm);\n    color: var(--bubble-color-text-muted);\n  }\n\n  /* Feature List */\n  .feature-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .feature-list li {\n    padding: var(--bubble-space-sm) 0;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    font-size: var(--bubble-font-size-sm);\n    line-height: 1.4;\n  }\n\n  .feature-list li:last-child {\n    border-bottom: none;\n  }\n\n  .feature-list li strong {\n    color: var(--bubble-color-text);\n  }\n\n  /* Quality Points */\n  .quality-points {\n    display: flex;\n    flex-direction: column;\n    gap: var(--bubble-space-md);\n  }\n\n  .quality-point {\n    display: flex;\n    align-items: flex-start;\n    gap: var(--bubble-space-md);\n    padding: var(--bubble-space-md);\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: 8px;\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n\n  .quality-icon {\n    font-size: 1.2rem;\n    flex-shrink: 0;\n  }\n\n  .quality-content {\n    display: flex;\n    flex-direction: column;\n    gap: var(--bubble-space-xs);\n  }\n\n  .quality-content strong {\n    color: var(--bubble-color-text);\n    font-size: var(--bubble-font-size-base);\n    font-weight: 600;\n  }\n\n  .quality-content span {\n    color: var(--bubble-color-text-muted);\n    font-size: var(--bubble-font-size-sm);\n  }\n\n  /* CTA Banner */\n  .info-cta-banner {\n    background: linear-gradient(135deg, var(--bubble-color-primary), var(--bubble-color-secondary));\n    border-radius: 12px;\n    padding: var(--bubble-space-xl);\n    margin-top: var(--bubble-space-lg);\n  }\n\n  .cta-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: var(--bubble-space-lg);\n  }\n\n  @media (max-width: 767px) {\n    .cta-content {\n      flex-direction: column;\n      text-align: center;\n      gap: var(--bubble-space-md);\n    }\n  }\n\n  .cta-text h3 {\n    font-size: 1.25rem;\n    font-weight: 800;\n    color: var(--bubble-color-text);\n    margin: 0 0 var(--bubble-space-sm) 0;\n  }\n\n  .cta-text p {\n    color: rgba(255, 255, 255, 0.9);\n    margin: 0;\n    font-size: var(--bubble-font-size-base);\n  }\n\n  .cta-savings {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-end;\n    text-align: right;\n  }\n\n  @media (max-width: 767px) {\n    .cta-savings {\n      align-items: center;\n      text-align: center;\n    }\n  }\n\n  .savings-text {\n    font-size: var(--bubble-font-size-sm);\n    color: rgba(255, 255, 255, 0.8);\n    margin-bottom: var(--bubble-space-xs);\n  }\n\n  .savings-amount {\n    font-size: 2rem;\n    font-weight: 900;\n    color: var(--bubble-color-text);\n  }\n\n  @media (max-width: 480px) {\n    .categories-info-section {\n      padding: var(--bubble-space-md);\n      max-width: 100%;\n      margin: var(--bubble-space-md) auto;\n    }\n    \n    .info-stats {\n      grid-template-columns: 1fr;\n    }\n    \n    .stat-number {\n      font-size: 1.25rem;\n    }\n    \n    .value-amount {\n      font-size: 1.25rem;\n    }\n    \n    .savings-amount {\n      font-size: 1.5rem;\n    }\n    \n    .info-title {\n      font-size: 1.25rem;\n    }\n\n    .platform-highlights {\n      justify-content: flex-start;\n    }\n\n    .highlight-tag {\n      min-width: 0;\n    }\n  }\n</style>\n\n<!-- Information Section -->\n<section class=\"categories-info-section\">\n  <div class=\"info-header\">\n    <h2 class=\"info-title\">Complete Access Details</h2>\n    <p class=\"info-subtitle\">Everything you need to know about your All-Access Pass membership</p>\n  </div>\n\n  <!-- Platform Coverage - Full Width -->\n  <div class=\"platform-coverage-section\">\n    <div class=\"info-card\">\n      <div class=\"info-card-header\">\n        <div class=\"info-icon\">🎛️</div>\n        <h3>Platform Coverage</h3>\n      </div>\n      <div class=\"info-card-content\">\n        <div class=\"info-stats\">\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">140+</span>\n            <span class=\"stat-label\">Guitar Products</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">244+</span>\n            <span class=\"stat-label\">Drum Products</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-number\">61+</span>\n            <span class=\"stat-label\">DAW Products</span>\n          </div>\n        </div>\n        <div class=\"platform-highlights\">\n          <span class=\"highlight-tag popular\">Superior Drummer 3 Presets</span>\n          <span class=\"highlight-tag popular\">Fractal Axe-Fx III Presets</span>\n          <span class=\"highlight-tag popular\">EZdrummer 3 Templates</span>\n          <span class=\"highlight-tag\">Fractal FM3 / FM9 Presets</span>\n          <span class=\"highlight-tag popular\">Neural DSP Quad Cortex Captures & Presets</span>\n          <span class=\"highlight-tag\">Kemper Profiles</span>\n          <span class=\"highlight-tag\">Fractal Axe Fx II Presets</span>\n          <span class=\"highlight-tag\">GetGood Drums Templates</span>\n          <span class=\"highlight-tag\">Cabinet IRs</span>\n          <span class=\"highlight-tag\">Cubase Premixed Templates</span>\n          <span class=\"highlight-tag\">Line 6 POD Go Presets</span>\n          <span class=\"highlight-tag\">Neural DSP Presets & IRs</span>\n          <span class=\"highlight-tag\">Perfect Drums Templates</span>\n          <span class=\"highlight-tag\">MODO Drum Presets</span>\n          <span class=\"highlight-tag\">Line 6 HX Stomp Presets</span>\n          <span class=\"highlight-tag\">Line 6 Helix Presets</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Three Column Grid for Other Cards -->\n  <div class=\"info-grid\">\n    <!-- Library Size & Value -->\n    <div class=\"info-card\">\n      <div class=\"info-card-header\">\n        <div class=\"info-icon\">📊</div>\n        <h3>Library Statistics</h3>\n      </div>\n      <div class=\"info-card-content\">\n        <div class=\"value-breakdown\">\n          <div class=\"value-item\">\n            <span class=\"value-amount\">€25,689</span>\n            <span class=\"value-description\">Total retail value of all included products</span>\n          </div>\n          <div class=\"value-item\">\n            <span class=\"value-amount\">2,500+</span>\n            <span class=\"value-description\">Individual presets and templates</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Access & Downloads -->\n    <div class=\"info-card\">\n      <div class=\"info-card-header\">\n        <div class=\"info-icon\">⚡</div>\n        <h3>Access & Downloads</h3>\n      </div>\n      <div class=\"info-card-content\">\n        <ul class=\"feature-list\">\n          <li><strong>Instant Access:</strong> Download immediately after purchase</li>\n          <li><strong>Keep Forever:</strong> All downloads remain yours permanently</li>\n          <li><strong>No Restrictions:</strong> Download as many times as needed</li>\n          <li><strong>Regular Updates:</strong> New content added monthly</li>\n          <li><strong>Multiple Formats:</strong> Compatible with all major platforms</li>\n        </ul>\n      </div>\n    </div>\n\n    <!-- Professional Usage -->\n    <div class=\"info-card\">\n      <div class=\"info-card-header\">\n        <div class=\"info-icon\">🎯</div>\n        <h3>Professional Quality</h3>\n      </div>\n      <div class=\"info-card-content\">\n        <div class=\"quality-points\">\n          <div class=\"quality-point\">\n            <span class=\"quality-icon\">🎸</span>\n            <div class=\"quality-content\">\n              <strong>Studio-Grade Presets</strong>\n              <span>Used by artists from major metal bands</span>\n            </div>\n          </div>\n          <div class=\"quality-point\">\n            <span class=\"quality-icon\">🥁</span>\n            <div class=\"quality-content\">\n              <strong>Producer-Ready Templates</strong>\n              <span>Pre-mixed for instant professional results</span>\n            </div>\n          </div>\n          <div class=\"quality-point\">\n            <span class=\"quality-icon\">🎚️</span>\n            <div class=\"quality-content\">\n              <strong>Industry Standard</strong>\n              <span>Compatible with professional workflows</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Bottom CTA Banner -->\n  <div class=\"info-cta-banner\">\n    <div class=\"cta-content\">\n      <div class=\"cta-text\">\n        <h3>Ready to Transform Your Sound?</h3>\n        <p>Join thousands of musicians and producers who trust our presets for their professional projects</p>\n      </div>\n      <div class=\"cta-savings\">\n        <span class=\"savings-text\">You Save:</span>\n        <span class=\"savings-amount\">€25,689</span>\n      </div>\n    </div>\n  </div>\n</section>\n\n{% comment %}\n  IMPORTANT: Close the conditional statement\n  This ensures the section only displays for \"All-Access Pass\" product\n{% endcomment %}\n{% endif %}\n\n{% comment %}\n  Instructions for customization:\n  \n  1. Update statistics and numbers to match your actual data:\n     - Platform counts (20+ Guitar Platforms, 9+ Drum Software, etc.)\n     - Total value (€25,689)\n     - Number of presets (2,500+)\n     - Library size (150GB+)\n  \n  2. Customize platform highlights:\n     - Replace with your actual supported platforms\n     - Add .popular class to highlight special platforms\n  \n  3. Modify feature list in Access & Downloads section:\n     - Add or remove features based on your actual offering\n  \n  4. Alternative conditions you can use:\n     - Check by product handle: {% if product.handle == \"all-access-pass\" %}\n     - Check by product ID: {% if product.id == 123456789 %}\n     - Check by product tag: {% if product.tags contains \"all-access\" %}\n     - Check by product type: {% if product.type == \"Membership\" %}\n  \n  5. Customize CTA banner:\n     - Update savings amount\n     - Modify call-to-action text\n{% endcomment %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_6K6yyi": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.type == \"Neural DSP Quad Cortex Captures & Presets\" %}\n<div class=\"quad-benefits-24\">\n  <style>\n    .quad-benefits-24 {\n      padding: 0;\n      border-radius: 24px; /* <<< ZMĚNA ZDE */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden;\n    }\n\n    .quad-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      /* <PERSON>kud chcete, aby obrázek respektoval zaoblení sekce, přidejte: */\n      /* border-top-left-radius: 24px; */\n      /* border-top-right-radius: 24px; */\n      /* overflow: hidden; */ /* Toto je pot<PERSON>, aby se obr<PERSON><PERSON>k oří<PERSON>l podle zaoblení */\n    }\n\n    .quad-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      object-position: center;\n      display: block;\n    }\n\n    .quad-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 100px;\n      background: linear-gradient(180deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 1) 100%\n      );\n      /* Pokud chcete, aby gradient respektoval zaoblení sekce, přidejte: */\n      /* border-bottom-left-radius: 24px; */ /* Pouze pokud je content-wrapper uvnitř */\n      /* border-bottom-right-radius: 24px; */ /* Pouze pokud je content-wrapper uvnitř */\n    }\n\n    .quad-content-wrapper {\n      padding: 24px;\n      background: #ffffff;\n      /* Pokud je content-wrapper posledním prvkem a chcete zaoblení dole: */\n      /* border-bottom-left-radius: 24px; */\n      /* border-bottom-right-radius: 24px; */\n    }\n\n    .quad-benefits-24:hover {\n      /* border-radius: 24px; */ /* <<< ZMĚNA ZDE (pokud chcete zachovat stejné zaoblení i při hoveru) */\n      /* Pokud chcete, aby se při hoveru neměnilo zaoblení, tuto řádku smažte nebo zakomentujte.\n         Pokud chcete jiné zaoblení při hoveru, upravte hodnotu.\n         Váš původní kód zde měl `border-radius: 0;` což by zaoblení při hoveru odstranilo.\n         Předpokládám, že chcete zaoblení zachovat, takže jsem ji zakomentoval.\n         Pokud chcete i při hoveru 24px, odkomentujte a nastavte na 24px.\n      */\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n      transform: translateY(-2px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    .quad-title-24 {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .quad-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    @media (max-width: 640px) {\n      .quad-grid-24 {\n        grid-template-columns: 1fr;\n      }\n\n      .quad-hero-section {\n        height: 300px;\n      }\n    }\n\n    .quad-item-24 {\n      padding: 20px;\n      border-radius: 12px; /* <<< ZMĚNA ZDE */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.3s ease;\n    }\n\n    .quad-item-24:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n      border-color: rgba(0, 0, 0, 0.16);\n      /* border-radius: 12px; */ /* Pokud chcete zachovat stejné zaoblení i při hoveru */\n    }\n\n    .quad-icon-24 {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .quad-item-title-24 {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .quad-text-24 {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #333333;\n    }\n\n    @media (max-width: 768px) {\n      .quad-content-wrapper {\n        padding: 20px;\n      }\n\n      .quad-title-24 {\n        font-size: 20px;\n      }\n\n      .quad-item-24 {\n        padding: 15px;\n      }\n    }\n  </style>\n\n  <div class=\"quad-hero-section\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/20221101-PB010310-ORF.jpg?v=1730712066\"\n      alt=\"Quad Cortex Neural Captures\"\n      class=\"quad-hero-image\"\n    />\n    <div class=\"quad-gradient-overlay\"></div>\n  </div>\n\n  <div class=\"quad-content-wrapper\">\n    <h2 class=\"quad-title-24\">Why Choose Develop Device Neural Captures?</h2>\n\n    <div class=\"quad-grid-24\">\n      <div class=\"quad-item-24\">\n        <svg class=\"quad-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z\"/>\n        </svg>\n        <h3 class=\"quad-item-title-24\">Professional Quality</h3>\n        <p class=\"quad-text-24\">Each neural capture is precisely crafted and extensively tested to ensure outstanding tone quality.</p>\n      </div>\n\n      <div class=\"quad-item-24\">\n        <svg class=\"quad-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"/>\n        </svg>\n        <h3 class=\"quad-item-title-24\">Studio & Stage Ready</h3>\n        <p class=\"quad-text-24\">Perfectly optimized for both studio recording and live performance, delivering consistent tone in any situation.</p>\n      </div>\n\n      <div class=\"quad-item-24\">\n        <svg class=\"quad-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z\"/>\n        </svg>\n        <h3 class=\"quad-item-title-24\">Fine-Tuned Settings</h3>\n        <p class=\"quad-text-24\">Each capture comes with optimized gain staging and carefully adjusted parameters for the best possible tone.</p>\n      </div>\n\n      <div class=\"quad-item-24\">\n        <svg class=\"quad-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M22 9V7h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2v-2h-2V9h2zm-4 10H4V5h14v14zM6 13h5v4H6zm6-6h4v3h-4zm0 4h4v3h-4zm-6-4h5v3H6z\"/>\n        </svg>\n        <h3 class=\"quad-item-title-24\">Neural DSP Compatibility</h3>\n        <p class=\"quad-text-24\">All neural captures are fully compatible with both Quad Cortex and Nano Cortex devices.</p>\n      </div>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_QnFLU3": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains \"HumBeat\" %}\n<style>\n/* Namespace všech CSS pravidel pro HumBeat */\n.humbeat-embed-v15 {\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n    box-sizing: border-box;\n}\n\n.humbeat-embed-v15 *,\n.humbeat-embed-v15 *::before,\n.humbeat-embed-v15 *::after {\n    box-sizing: inherit;\n}\n\n.humbeat-embed-v15 .humbeat-wrapper {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin: 0;\n    padding: 0;\n}\n\n.humbeat-embed-v15 .particles {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 1;\n    pointer-events: none;\n}\n\n.humbeat-embed-v15 .particle {\n    position: absolute;\n    background: rgba(110, 107, 225, 0.4);\n    animation: humbeat-float-v15 8s infinite ease-in-out;\n    pointer-events: none;\n    border-radius: 50%;\n}\n\n@keyframes humbeat-float-v15 {\n    0% {\n        transform: translateY(0) translateX(0);\n        opacity: 0;\n    }\n    25% {\n        transform: translateY(-30px) translateX(20px);\n        opacity: 0.7;\n    }\n    50% {\n        transform: translateY(-50px) translateX(0px);\n        opacity: 0.4;\n    }\n    75% {\n        transform: translateY(-30px) translateX(-20px);\n        opacity: 0.7;\n    }\n    100% {\n        transform: translateY(0) translateX(0);\n        opacity: 0;\n    }\n}\n\n.humbeat-embed-v15 #humbeat-release-note-v15 {\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(10px);\n    -webkit-backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 40px;\n    max-width: 1800px;\n    width: 100%;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n    z-index: 10;\n    position: relative;\n    color: #ffffff;\n    overflow: hidden;\n    background: linear-gradient(135deg, #392C7D, #6E6BE1);\n    border-radius: 24px;\n    margin: 20px auto;\n}\n\n.humbeat-embed-v15 .humbeat-container h1 {\n    font-size: 32px;\n    margin: 0 0 20px 0;\n    color: white;\n    font-weight: 700;\n    text-align: center;\n    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n    line-height: 1.2;\n}\n\n.humbeat-embed-v15 .humbeat-container p {\n    font-size: 18px;\n    line-height: 1.6;\n    margin: 0 0 30px 0;\n    text-align: center;\n    color: rgba(255, 255, 255, 0.9);\n}\n\n.humbeat-embed-v15 .humbeat-features {\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 16px;\n    margin: 0 0 30px 0;\n    padding: 0;\n    list-style: none;\n}\n\n.humbeat-embed-v15 .feature-item {\n    background: rgba(57, 44, 125, 0.3);\n    padding: 20px;\n    display: flex;\n    align-items: flex-start;\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\n    border-radius: 24px;\n    overflow: hidden;\n    margin: 0;\n}\n\n.humbeat-embed-v15 .feature-item:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n}\n\n.humbeat-embed-v15 .feature-icon {\n    background: linear-gradient(135deg, #392C7D, #6E6BE1);\n    width: 40px;\n    height: 40px;\n    border-radius: 50%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-right: 16px;\n    flex-shrink: 0;\n}\n\n.humbeat-embed-v15 .feature-icon svg {\n    width: 20px;\n    height: 20px;\n    fill: white;\n}\n\n.humbeat-embed-v15 .feature-text {\n    flex-grow: 1;\n}\n\n.humbeat-embed-v15 .feature-text h3 {\n    font-size: 18px;\n    margin: 0 0 8px 0;\n    color: #FFFFFF;\n    line-height: 1.3;\n    font-weight: 700;\n    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n}\n\n.humbeat-embed-v15 .feature-text p {\n    font-size: 16px;\n    line-height: 1.5;\n    margin: 0;\n    text-align: left;\n    color: rgba(255, 255, 255, 0.8);\n}\n\n.humbeat-embed-v15 .humbeat-download {\n    display: block;\n    width: fit-content;\n    margin: 0 auto;\n    background: linear-gradient(90deg, #6E6BE1, #392C7D);\n    color: white;\n    font-weight: 600;\n    padding: 14px 32px;\n    text-decoration: none;\n    font-size: 18px;\n    transition: all 0.3s ease;\n    position: relative;\n    overflow: hidden;\n    border: none;\n    cursor: pointer;\n    box-shadow: 0 4px 20px rgba(57, 44, 125, 0.4);\n    border-radius: 12px;\n}\n\n.humbeat-embed-v15 .humbeat-download::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));\n    transition: left 0.6s ease;\n}\n\n.humbeat-embed-v15 .humbeat-download:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 25px rgba(110, 107, 225, 0.6);\n}\n\n.humbeat-embed-v15 .humbeat-download:hover::before {\n    left: 100%;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n    .humbeat-embed-v15 #humbeat-release-note-v15 {\n        padding: 20px;\n        margin: 10px;\n    }\n\n    .humbeat-embed-v15 .humbeat-container h1 {\n        font-size: 24px;\n    }\n\n    .humbeat-embed-v15 .humbeat-container p {\n        font-size: 16px;\n    }\n\n    .humbeat-embed-v15 .feature-item {\n        padding: 15px;\n    }\n\n    .humbeat-embed-v15 .feature-text h3 {\n        font-size: 16px;\n    }\n\n    .humbeat-embed-v15 .feature-text p {\n        font-size: 14px;\n    }\n}\n</style>\n\n<div class=\"humbeat-embed-v15\">\n    <div class=\"humbeat-wrapper\">\n        <div id=\"humbeat-release-note-v15\">\n            <div class=\"particles\" id=\"humbeat-particles-v15\"></div>\n\n            <div class=\"humbeat-container\">\n                <h1>HumBeat v1.5 – Major Update!</h1>\n                <p>Revolutionary improvements that make your drums sound more human than ever.</p>\n                <p style=\"background: rgba(57, 44, 125, 0.4); padding: 15px; border-radius: 12px; margin: 20px 0; border-left: 4px solid #6E6BE1; font-weight: 600;\">\n                    🎉 <strong>Free Update</strong> for all existing customers! Download now from your account.\n                </p>\n                \n                <div class=\"humbeat-features\">\n                    <div class=\"feature-item\">\n                        <div class=\"feature-icon\">\n                            <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z\" fill=\"currentColor\"/>\n                                <path d=\"M21 9V7L15 6.5V4.5L12 5.5L9 4.5V6.5L3 7V9L9 8V10L3 11V13L9 12V14L3 15V17L9 16V18.5L12 19.5L15 18.5V16L21 17V15L15 14V12L21 13V11L15 10V8L21 9Z\" fill=\"currentColor\"/>\n                            </svg>\n                        </div>\n                        <div class=\"feature-text\">\n                            <h3>Revolutionary Natural Timing</h3>\n                            <p>Advanced Gaussian distribution algorithms create timing variations that perfectly mimic real drummers.</p>\n                        </div>\n                    </div>\n                    \n                    <div class=\"feature-item\">\n                        <div class=\"feature-icon\">\n                            <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path d=\"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM7 13.5C6.17 13.5 5.5 12.83 5.5 12S6.17 10.5 7 10.5 8.5 11.17 8.5 12 7.83 13.5 7 13.5ZM12 13.5C11.17 13.5 10.5 12.83 10.5 12S11.17 10.5 12 10.5 13.5 11.17 13.5 12 12.83 13.5 12 13.5ZM17 13.5C16.17 13.5 15.5 12.83 15.5 12S16.17 10.5 17 10.5 18.5 11.17 18.5 12 17.83 13.5 17 13.5Z\" fill=\"currentColor\"/>\n                            </svg>\n                        </div>\n                        <div class=\"feature-text\">\n                            <h3>Smart Musical Intelligence</h3>\n                            <p>Preserves musical relationships - quiet notes stay quiet, accents remain strong, just like real drummers.</p>\n                        </div>\n                    </div>\n                    \n                    <div class=\"feature-item\">\n                        <div class=\"feature-icon\">\n                            <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path d=\"M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                            </svg>\n                        </div>\n                        <div class=\"feature-text\">\n                            <h3>Rock-Solid Reliability</h3>\n                            <p>Enhanced error handling and input validation ensures smooth processing of any MIDI file.</p>\n                        </div>\n                    </div>\n                    \n                    <div class=\"feature-item\">\n                        <div class=\"feature-icon\">\n                            <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path d=\"M13 9H18.5L13 3.5V9ZM6 2H14L20 8V20C20 21.1 19.1 22 18 22H6C4.89 22 4 21.1 4 20V4C4 2.9 4.89 2 6 2ZM15 18V16H6V18H15ZM18 14V12H6V14H18Z\" fill=\"currentColor\"/>\n                            </svg>\n                        </div>\n                        <div class=\"feature-text\">\n                            <h3>Professional Performance</h3>\n                            <p>Faster processing, better memory management, and enhanced compatibility with complex MIDI files.</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<script>\n(function() {\n    'use strict';\n    \n    // Namespace pro HumBeat v1.5 - zabránění konfliktů\n    const HUMBEAT_V15_NAMESPACE = 'humbeatV15_' + Math.random().toString(36).substr(2, 9);\n    \n    // Ensure this script runs only when the humbeat-release-note element exists\n    const humbeatReleaseNote = document.getElementById('humbeat-release-note-v15');\n\n    if (humbeatReleaseNote) {\n        // Create hi-tech particles\n        const particlesContainer = document.getElementById('humbeat-particles-v15');\n        if (particlesContainer) {\n            const particleCount = 40;\n            \n            // Generate connecting lines\n            const lines = [];\n            \n            for (let i = 0; i < particleCount; i++) {\n                const particle = document.createElement('div');\n                particle.classList.add('particle');\n                particle.setAttribute('data-humbeat-v15', 'true');\n                \n                // Smaller, more tech-looking particles\n                const size = Math.random() * 10 + 2;\n                particle.style.width = size + 'px';\n                particle.style.height = size + 'px';\n                \n                // Random position\n                const posX = Math.random() * 100;\n                const posY = Math.random() * 100;\n                particle.style.left = posX + '%';\n                particle.style.top = posY + '%';\n                \n                // Random animation delay\n                particle.style.animationDelay = (Math.random() * 8) + 's';\n                \n                // Random animation duration\n                particle.style.animationDuration = (Math.random() * 10 + 8) + 's';\n                \n                // Add random opacity and glow effect\n                const opacity = Math.random() * 0.5 + 0.2;\n                particle.style.opacity = opacity;\n                particle.style.boxShadow = '0 0 ' + (Math.random() * 10 + 5) + 'px rgba(110, 107, 225, ' + opacity + ')';\n                \n                // Add particle to container\n                particlesContainer.appendChild(particle);\n                \n                // Store position for connecting lines\n                lines.push({\n                    x: posX,\n                    y: posY,\n                    size: size,\n                    animDelay: Math.random() * 8,\n                    animDuration: Math.random() * 10 + 8\n                });\n            }\n            \n            // Create canvas for connecting lines\n            const canvas = document.createElement('canvas');\n            canvas.style.position = 'absolute';\n            canvas.style.top = '0';\n            canvas.style.left = '0';\n            canvas.style.width = '100%';\n            canvas.style.height = '100%';\n            canvas.style.opacity = '0.2';\n            canvas.style.zIndex = '0';\n            canvas.setAttribute('data-humbeat-v15', 'true');\n            particlesContainer.appendChild(canvas);\n            \n            // Size canvas to container\n            function resizeCanvas() {\n                if (!canvas.parentNode) return; // Safety check\n                const containerRect = particlesContainer.getBoundingClientRect();\n                canvas.width = containerRect.width;\n                canvas.height = containerRect.height;\n            }\n            \n            let animationId;\n            \n            // Draw connecting lines\n            const ctx = canvas.getContext('2d');\n            function animate() {\n                if (!canvas.parentNode) return; // Safety check - stop animation if canvas removed\n                \n                ctx.clearRect(0, 0, canvas.width, canvas.height);\n                \n                for (let i = 0; i < lines.length; i++) {\n                    for (let j = i + 1; j < lines.length; j++) {\n                        const dx = (lines[i].x / 100 * canvas.width) - (lines[j].x / 100 * canvas.width);\n                        const dy = (lines[i].y / 100 * canvas.height) - (lines[j].y / 100 * canvas.height);\n                        const distance = Math.sqrt(dx * dx + dy * dy);\n                        \n                        if (distance < canvas.width * 0.2) {\n                            const opacity = (1 - distance / (canvas.width * 0.2)) * 0.5;\n                            ctx.strokeStyle = 'rgba(110, 107, 225, ' + opacity + ')';\n                            ctx.lineWidth = 1;\n                            ctx.beginPath();\n                            ctx.moveTo(lines[i].x / 100 * canvas.width, lines[i].y / 100 * canvas.height);\n                            ctx.lineTo(lines[j].x / 100 * canvas.width, lines[j].y / 100 * canvas.height);\n                            ctx.stroke();\n                        }\n                    }\n                }\n                \n                animationId = requestAnimationFrame(animate);\n            }\n            \n            // Initial setup\n            resizeCanvas();\n            animate();\n            \n            // Resize listener s namespace\n            const resizeHandler = function() {\n                resizeCanvas();\n            };\n            \n            window.addEventListener('resize', resizeHandler);\n            \n            // Cleanup function pro případ, že by se embed odstranil\n            window[HUMBEAT_V15_NAMESPACE + '_cleanup'] = function() {\n                if (animationId) {\n                    cancelAnimationFrame(animationId);\n                }\n                window.removeEventListener('resize', resizeHandler);\n                \n                // Remove particles\n                const particlesToRemove = document.querySelectorAll('[data-humbeat-v15=\"true\"]');\n                particlesToRemove.forEach(function(particle) {\n                    if (particle.parentNode) {\n                        particle.parentNode.removeChild(particle);\n                    }\n                });\n                \n                delete window[HUMBEAT_V15_NAMESPACE + '_cleanup'];\n            };\n        }\n    }\n})();\n</script>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_DcXV37": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.metafields.custom.required_toontrack_sdx_ezx_extensions != blank and product.type == \"Superior Drummer 3 Presets\" %}\n<style>\n  :root {\n    /* --- Inverted black and white color scheme --- */\n    --bg-primary: #ffffff; /* White base */\n    --bg-secondary: #f9f9f9; /* Very light gray */\n    --bg-tertiary: #f3f3f3; /* Light gray */\n    \n    --accent-primary: #000000; /* Black */\n    --accent-secondary: #333333; /* Dark gray */\n    --accent-tertiary: #666666; /* Medium gray */\n    \n    --text-primary: #000000; /* Black text */\n    --text-secondary: #333333; /* Dark gray text */\n    --text-tertiary: #666666; /* Medium gray text */\n    \n    --border-color: rgba(0, 0, 0, 0.08); /* Very subtle black border */\n    --border-highlight: var(--accent-primary);\n    \n    --shadow-color: rgba(0, 0, 0, 0.05); /* Very soft black shadow */\n    --shadow-sm: 0 2px 8px var(--shadow-color);\n    --shadow-md: 0 4px 16px var(--shadow-color);\n    --shadow-lg: 0 8px 24px var(--shadow-color);\n    \n    --spacing-base: 8px;\n  }\n  \n  .required-extensions {\n    background: var(--bg-primary);\n    border: none;\n    padding: calc(var(--spacing-base) * 5);\n    margin: calc(var(--spacing-base) * 4) 0;\n    box-shadow: var(--shadow-sm);\n    color: var(--text-secondary);\n    position: relative;\n    overflow: hidden;\n    border-radius: 24px; /* <<< Přidáno zaoblení sekce */\n  }\n  \n  /* Remove the top edge line */\n  .required-extensions::before {\n    display: none;\n  }\n  \n  .required-extensions__title {\n    color: var(--text-primary);\n    font-size: 36px;\n    font-weight: 800;\n    margin: 0 0 calc(var(--spacing-base) * 4);\n    padding-bottom: calc(var(--spacing-base) * 2);\n    border-bottom: 1px solid var(--border-color);\n    position: relative;\n    display: inline-block;\n  }\n  \n  /* Lighter title underline */\n  .required-extensions__title::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    width: 100px;\n    height: 2px;\n    background: var(--accent-primary);\n    opacity: 0.5;\n  }\n  \n  .extensions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: calc(var(--spacing-base) * 3);\n    margin-bottom: calc(var(--spacing-base) * 5);\n    position: relative;\n    z-index: 1;\n  }\n  \n  .extension-card {\n    background: var(--bg-secondary);\n    border: none;\n    padding: calc(var(--spacing-base) * 3);\n    display: flex;\n    align-items: center;\n    gap: calc(var(--spacing-base) * 2.5);\n    transition: all 0.4s ease;\n    box-shadow: var(--shadow-sm);\n    position: relative;\n    overflow: hidden;\n    border-radius: 16px; /* <<< Přidáno pro konzistenci - můžete upravit nebo odstranit */\n  }\n  \n  .extension-card:hover {\n    transform: translateY(-8px);\n    box-shadow: var(--shadow-md);\n  }\n  \n  .extension-card__image {\n    width: 85px;\n    height: 85px;\n    object-fit: cover;\n    border: none;\n    transition: all 0.4s ease;\n    z-index: 1;\n    border-radius: 8px; /* <<< Přidáno pro konzistenci - můžete upravit nebo odstranit */\n  }\n  \n  .extension-card:hover .extension-card__image {\n    transform: scale(1.12);\n  }\n  \n  .extension-card__content {\n    flex: 1;\n    z-index: 1;\n  }\n  \n  .extension-card__title {\n    color: var(--text-primary);\n    font-size: 18px;\n    font-weight: 700;\n    margin: 0 0 calc(var(--spacing-base) * 2);\n    line-height: 1.3;\n  }\n  \n  .extension-card__button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    background: var(--accent-primary);\n    color: var(--bg-primary);\n    padding: calc(var(--spacing-base) * 1.5) calc(var(--spacing-base) * 3.5);\n    text-decoration: none;\n    font-size: 15px;\n    font-weight: 700;\n    letter-spacing: 0.5px;\n    transition: all 0.3s ease;\n    position: relative;\n    overflow: hidden;\n    border: none;\n    border-radius: 12px; /* <<< Přidáno zaoblení tlačítka */\n  }\n  \n  .extension-card__button:hover {\n    background: var(--bg-tertiary);\n    color: var(--text-primary);\n  }\n  \n  .important-info {\n    background: var(--bg-secondary);\n    padding: calc(var(--spacing-base) * 4);\n    border: none;\n    border-left: 2px solid var(--accent-secondary);\n    box-shadow: var(--shadow-sm);\n    position: relative;\n    z-index: 1;\n    border-radius: 16px; /* <<< Přidáno pro konzistenci - můžete upravit nebo odstranit */\n  }\n  \n  .important-info__header {\n    display: flex;\n    align-items: center;\n    gap: calc(var(--spacing-base) * 2);\n    margin-bottom: calc(var(--spacing-base) * 3);\n  }\n  \n  .important-info__icon {\n    background: var(--accent-primary);\n    color: var(--bg-primary);\n    min-width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: 900;\n    font-size: 22px;\n    border-radius: 8px; /* <<< Přidáno pro konzistenci - můžete upravit nebo odstranit */\n  }\n  \n  .important-info__title {\n    color: var(--text-primary);\n    font-size: 24px;\n    font-weight: 700;\n    margin: 0;\n  }\n  \n  .important-info__list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n  \n  .important-info__item {\n    display: flex;\n    align-items: flex-start;\n    gap: calc(var(--spacing-base) * 1.5);\n    padding: calc(var(--spacing-base) * 2) 0;\n    border-bottom: 1px solid var(--border-color);\n    transition: transform 0.3s ease, background-color 0.3s ease;\n  }\n  \n  .important-info__item:hover {\n    transform: translateX(5px);\n    background-color: var(--bg-tertiary);\n  }\n  \n  .important-info__item:last-child {\n    border-bottom: none;\n  }\n  \n  .important-info__bullet {\n    color: var(--accent-primary);\n    font-size: 22px;\n    line-height: 1.4;\n    font-weight: 700;\n    margin-top: -2px;\n    opacity: 0.7;\n  }\n  \n  .important-info__text {\n    color: var(--text-secondary);\n    font-size: 15px;\n    line-height: 1.6;\n    flex: 1;\n  }\n  \n  .important-info__text a {\n    font-weight: 600;\n    color: var(--accent-primary);\n    text-decoration: none;\n    position: relative;\n    transition: all 0.3s ease;\n    padding: 0 3px;\n  }\n  \n  .important-info__text a:hover {\n    text-decoration: underline;\n  }\n  \n  .important-info__note {\n    margin-top: calc(var(--spacing-base) * 3);\n    padding-top: calc(var(--spacing-base) * 3);\n    border-top: 1px solid var(--border-color);\n    font-size: 14px;\n    color: var(--text-tertiary);\n    font-style: normal;\n    opacity: 0.8;\n    position: relative;\n  }\n  \n  /* Remove the line above note */\n  .important-info__note::before {\n    display: none;\n  }\n</style>\n\n<div class=\"required-extensions\">\n  <h3 class=\"required-extensions__title\">Required Extensions</h3>\n  <div class=\"extensions-grid\">\n    {% for extension in product.metafields.custom.required_toontrack_sdx_ezx_extensions.value %}\n      <div class=\"extension-card\">\n        {% if extension.image %}\n          <img\n            src=\"{{ extension.image | image_url: width: 170 }}\"\n            alt=\"{{ extension.name }}\"\n            class=\"extension-card__image\"\n            width=\"85\" \n            height=\"85\"\n            loading=\"lazy\"\n          >\n        {% endif %}\n        <div class=\"extension-card__content\">\n          <h4 class=\"extension-card__title\">{{ extension.name }}</h4>\n          <a\n            href=\"{{ extension.link_to_sdx_ezx_extension }}\"\n            class=\"extension-card__button\"\n            target=\"_blank\"\n            rel=\"noopener\"\n          >\n            More Information\n          </a>\n        </div>\n      </div>\n    {% endfor %}\n  </div>\n\n  <div class=\"important-info\">\n    <div class=\"important-info__header\">\n      <div class=\"important-info__icon\">!</div>\n      <h3 class=\"important-info__title\">Important Information:</h3>\n    </div>\n    <ul class=\"important-info__list\">\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">▸</span>\n        <span class=\"important-info__text\">Software not included: Requires ownership of <a href=\"https://www.thomann.de/cz/toontrack_superior_drummer_3.htm\" target=\"_blank\" rel=\"noopener\">Toontrack Superior Drummer 3</a> AND all SDX/EZX libraries listed above.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">▸</span>\n        <span class=\"important-info__text\">In addition to the specific SDX/EZX extensions, a complete installation of the Superior Drummer 3 CORE library is required.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">▸</span>\n        <span class=\"important-info__text\">Before use, make sure all your Toontrack software is fully updated via the Toontrack Product Manager.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">▸</span>\n        <span class=\"important-info__text\">Once requirements are met, load the provided SD3P preset file via the Superior Drummer 3 \"File\" > \"Open project...\" menu.</span>\n      </li>\n    </ul>\n    <p class=\"important-info__note\">Note: This is an independent third-party product and is not affiliated with, sponsored by, or endorsed by Toontrack Music AB.</p>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_bYfdtq": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.type == 'Fractal Axe Fx II Presets' %}\n<div class=\"axefx-benefits-24\">\n  <style>\n    .axefx-benefits-24 {\n      padding: 0;\n      border-radius: 24px; /* Změněno z 0 na 24px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden; /* Důležité pro oříznutí vnitřního obsahu podle border-radius */\n    }\n\n    .axefx-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      /* Okraje hero sekce budou oříznuty rodičovským prvkem .axefx-benefits-24 díky overflow: hidden */\n    }\n\n    .axefx-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      object-position: center;\n      display: block;\n    }\n\n    .axefx-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 100px;\n      background: linear-gradient(180deg, \n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 1) 100%\n      );\n    }\n\n    .axefx-content-wrapper {\n      padding: 24px;\n      background: #ffffff;\n      /* Okraje content wrapperu budou oříznuty rodičovským prvkem .axefx-benefits-24 */\n    }\n\n    .axefx-benefits-24:hover {\n      /* border-radius: 0;  Původní hodnota, nyní zachováváme zaoblení */\n      border-radius: 24px; /* Zachování zaoblení i při hoveru */\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n      transform: translateY(-2px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    .axefx-title-24 {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .axefx-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    @media (max-width: 640px) {\n      .axefx-grid-24 {\n        grid-template-columns: 1fr;\n      }\n      \n      .axefx-hero-section {\n        height: 300px;\n      }\n    }\n\n    .axefx-item-24 { /* TOTO JSOU \"TLAČÍTKA\" / POLOŽKY */\n      padding: 20px;\n      border-radius: 12px; /* Změněno z 0 na 12px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.3s ease;\n    }\n\n    .axefx-item-24:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n      border-color: rgba(0, 0, 0, 0.16);\n    }\n\n    .axefx-icon-24 {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .axefx-item-title-24 {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .axefx-text-24 {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #000000;\n    }\n\n    @media (max-width: 768px) {\n      .axefx-content-wrapper {\n        padding: 20px;\n      }\n      \n      .axefx-title-24 {\n        font-size: 20px;\n      }\n      \n      .axefx-item-24 {\n        padding: 15px;\n      }\n    }\n  </style>\n\n  <div class=\"axefx-hero-section\">\n    <img \n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/P9040009.jpg?v=1730711237\"\n      alt=\"Fractal Axe-Fx II Presets\"\n      class=\"axefx-hero-image\"\n    />\n    <div class=\"axefx-gradient-overlay\"></div>\n  </div>\n  \n  <div class=\"axefx-content-wrapper\">\n    <h2 class=\"axefx-title-24\">Why Choose Develop Device Axe-Fx II Presets?</h2>\n    \n    <div class=\"axefx-grid-24\">\n      <div class=\"axefx-item-24\">\n        <svg class=\"axefx-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M3 2h18c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2h-18c-1.1 0-2-.9-2-2v-12c0-1.1.9-2 2-2zm0 14h18v-12h-18v12zm16-10h-14v2h14v-2zm-14 4h14v2h-14v-2zm14 4h-14v2h14v-2z\"/>\n        </svg>\n        <h3 class=\"axefx-item-title-24\">Professional Experience</h3>\n        <p class=\"axefx-text-24\">Created by a professional audio engineer with over a decade of experience in mixing and sound design.</p>\n      </div>\n\n      <div class=\"axefx-item-24\">\n        <svg class=\"axefx-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"/>\n        </svg>\n        <h3 class=\"axefx-item-title-24\">Studio-Grade Tones</h3>\n        <p class=\"axefx-text-24\">Each preset is meticulously crafted for professional sound quality in both studio and live settings.</p>\n      </div>\n\n      <div class=\"axefx-item-24\">\n        <svg class=\"axefx-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M7 4v16h10V4H7zm5 15c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm3-4H9V5h6v10z\"/>\n        </svg>\n        <h3 class=\"axefx-item-title-24\">Multiple Scenes</h3>\n        <p class=\"axefx-text-24\">Each preset includes various scenes setup for instant switching between different sounds during performance.</p>\n      </div>\n\n      <div class=\"axefx-item-24\">\n        <svg class=\"axefx-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM9 18H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zM19 6h-8v4h8V6z\"/>\n        </svg>\n        <h3 class=\"axefx-item-title-24\">Full Compatibility</h3>\n        <p class=\"axefx-text-24\">Works flawlessly with all Axe-Fx II models including Mark I, II, and II XL/XL+.</p>\n      </div>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_UUKnQr": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains 'Line 6 Helix' %}\n<div class=\"helix-benefits-24\">\n  <style>\n    .helix-benefits-24 {\n      padding: 0;\n      border-radius: 24px; /* Změněno z 0 na 24px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden; /* Důležité pro oříznutí vnitřního obsahu podle border-radius */\n    }\n\n    .helix-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      /* Okraje hero sekce budou oříznuty rodičovským prvkem .helix-benefits-24 díky overflow: hidden */\n    }\n\n    .helix-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      object-position: center;\n      display: block;\n    }\n\n    .helix-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 100px;\n      background: linear-gradient(180deg, \n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 1) 100%\n      );\n    }\n\n    .helix-content-wrapper {\n      padding: 24px;\n      background: #ffffff;\n      /* Okraje content wrapperu budou oříznuty rodičovským prvkem .helix-benefits-24 */\n    }\n\n    .helix-benefits-24:hover {\n      /* border-radius: 0; Původní hodnota, nyní zachováváme zaoblení */\n      border-radius: 24px; /* Zachování zaoblení i při hoveru */\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n      transform: translateY(-2px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    .helix-title-24 {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .helix-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    @media (max-width: 640px) {\n      .helix-grid-24 {\n        grid-template-columns: 1fr;\n      }\n      \n      .helix-hero-section {\n        height: 300px;\n      }\n    }\n\n    .helix-item-24 { /* TOTO JSOU \"TLAČÍTKA\" / POLOŽKY */\n      padding: 20px;\n      border-radius: 12px; /* Změněno z 0 na 12px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.3s ease;\n    }\n\n    .helix-item-24:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n      border-color: rgba(0, 0, 0, 0.16);\n    }\n\n    .helix-icon-24 {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .helix-item-title-24 {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .helix-text-24 {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #000000;\n    }\n\n    @media (max-width: 768px) {\n      .helix-content-wrapper {\n        padding: 20px;\n      }\n      \n      .helix-title-24 {\n        font-size: 20px;\n      }\n      \n      .helix-item-24 {\n        padding: 15px;\n      }\n    }\n  </style>\n\n  <div class=\"helix-hero-section\">\n    <img \n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/20221101-PB010371-ORF.jpg?v=1730712414\"\n      alt=\"Line 6 Helix Presets\"\n      class=\"helix-hero-image\"\n    />\n    <div class=\"helix-gradient-overlay\"></div>\n  </div>\n  \n  <div class=\"helix-content-wrapper\">\n    <h2 class=\"helix-title-24\">Why Choose Develop Device Helix Presets?</h2>\n    \n    <div class=\"helix-grid-24\">\n      <div class=\"helix-item-24\">\n        <svg class=\"helix-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z\"/>\n        </svg>\n        <h3 class=\"helix-item-title-24\">Professional Quality</h3>\n        <p class=\"helix-text-24\">Each preset is meticulously crafted and tested to deliver exceptional sound quality for both studio and live performance.</p>\n      </div>\n\n      <div class=\"helix-item-24\">\n        <svg class=\"helix-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"/>\n        </svg>\n        <h3 class=\"helix-item-title-24\">Studio-Grade Tones</h3>\n        <p class=\"helix-text-24\">Developed using professional-grade equipment to ensure the highest quality sound reproduction.</p>\n      </div>\n\n      <div class=\"helix-item-24\">\n        <svg class=\"helix-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM17 13l-5 5-5-5h3V9h4v4h3z\"/>\n        </svg>\n        <h3 class=\"helix-item-title-24\">Instant Download</h3>\n        <p class=\"helix-text-24\">Get immediate access to your presets after purchase, ready to use with your Helix device.</p>\n      </div>\n\n      <div class=\"helix-item-24\">\n        <svg class=\"helix-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M22 9V7h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2v-2h-2V9h2zm-4 10H4V5h14v14zM6 13h5v4H6zm6-6h4v3h-4zm0 4h4v3h-4zm-6-4h5v3H6z\"/>\n        </svg>\n        <h3 class=\"helix-item-title-24\">Universal Helix Compatibility</h3>\n        <p class=\"helix-text-24\">All presets work seamlessly with every Helix unit (Floor, LT, Rack) and Helix Native plugin.</p>\n      </div>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_q6mpQW": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains 'Fractal FM3/FM9' %}\n<div class=\"fractal-benefits-24\">\n  <style>\n    .fractal-benefits-24 {\n      padding: 0;\n      border-radius: 24px; /* Změněno z 0 na 24px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden; /* Důležité pro oříznutí vnitřního obsahu podle border-radius */\n    }\n\n    .fractal-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      /* Okraje hero sekce budou oříznuty rodičovským prvkem .fractal-benefits-24 díky overflow: hidden */\n    }\n\n    .fractal-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      object-position: top; /* <PERSON><PERSON><PERSON><PERSON> z<PERSON>stat top, pokud je to záměr pro tento obrázek */\n      display: block;\n    }\n\n    .fractal-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 100px;\n      background: linear-gradient(180deg, \n        rgba(0, 0, 0, 0) 0%, /* Opraveno z rgba(0, 0, 0, 0) na rgba(255, 255, 255, 0) pro konzistenci s ostatními, pokud má být průhledná bílá */\n        rgba(255, 255, 255, 1) 100%\n      );\n    }\n\n    .fractal-content-wrapper {\n      padding: 24px;\n      background: #ffffff;\n      /* Okraje content wrapperu budou oříznuty rodičovským prvkem .fractal-benefits-24 */\n    }\n\n    .fractal-benefits-24:hover {\n      /* border-radius: 0; Původní hodnota, nyní zachováváme zaoblení */\n      border-radius: 24px; /* Zachování zaoblení i při hoveru */\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n      transform: translateY(-2px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    .fractal-title-24 {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .fractal-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    @media (max-width: 640px) {\n      .fractal-grid-24 {\n        grid-template-columns: 1fr;\n      }\n      \n      .fractal-hero-section {\n        height: 300px;\n      }\n    }\n\n    .fractal-item-24 { /* TOTO JSOU \"TLAČÍTKA\" / POLOŽKY */\n      padding: 20px;\n      border-radius: 12px; /* Změněno z 0 na 12px */\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.3s ease;\n    }\n\n    .fractal-item-24:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n      border-color: rgba(0, 0, 0, 0.16);\n    }\n\n    .fractal-icon-24 {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .fractal-item-title-24 {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .fractal-text-24 {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #000000;\n    }\n\n    @media (max-width: 768px) {\n      .fractal-content-wrapper {\n        padding: 20px;\n      }\n      \n      .fractal-title-24 {\n        font-size: 20px;\n      }\n      \n      .fractal-item-24 {\n        padding: 15px;\n      }\n    }\n  </style>\n\n  <div class=\"fractal-hero-section\">\n    <img \n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/FM3_6e41243b-6ef1-4308-af59-e3a3ed025b5b.jpg?v=1730711013\"\n      alt=\"Fractal FM3/FM9 Presets\"\n      class=\"fractal-hero-image\"\n    />\n    <div class=\"fractal-gradient-overlay\"></div>\n  </div>\n  \n  <div class=\"fractal-content-wrapper\">\n    <h2 class=\"fractal-title-24\">Why Choose Develop Device Fractal FM3/FM9 Presets?</h2>\n    \n    <div class=\"fractal-grid-24\">\n      <div class=\"fractal-item-24\">\n        <svg class=\"fractal-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z\"/>\n        </svg>\n        <h3 class=\"fractal-item-title-24\">Professional Quality</h3>\n        <p class=\"fractal-text-24\">Each preset is meticulously crafted and tested to deliver exceptional sound quality for both studio and live performance.</p>\n      </div>\n\n      <div class=\"fractal-item-24\">\n        <svg class=\"fractal-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"/>\n        </svg>\n        <h3 class=\"fractal-item-title-24\">Studio-Grade Tones</h3>\n        <p class=\"fractal-text-24\">Developed using professional-grade equipment to ensure the highest quality sound reproduction.</p>\n      </div>\n\n      <div class=\"fractal-item-24\">\n        <svg class=\"fractal-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM17 13l-5 5-5-5h3V9h4v4h3z\"/>\n        </svg>\n        <h3 class=\"fractal-item-title-24\">Instant Download</h3>\n        <p class=\"fractal-text-24\">Get immediate access to your presets after purchase, ready to use with your FM3 or FM9.</p>\n      </div>\n\n      <div class=\"fractal-item-24\">\n        <svg class=\"fractal-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M22 9V7h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2v-2h-2V9h2zm-4 10H4V5h14v14zM6 13h5v4H6zm6-6h4v3h-4zm0 4h4v3h-4zm-6-4h5v3H6z\"/>\n        </svg>\n        <h3 class=\"fractal-item-title-24\">Full Compatibility with Fractal FM9</h3>\n        <p class=\"fractal-text-24\">All presets are fully compatible with your Fractal FM9 device.</p>\n      </div>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_mbBEcp": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains 'Fractal Axe-Fx III' %}\n<div class=\"tc-fractal-benefits-embed-v1\">\n  <style>\n    .tc-fractal-benefits-embed-v1 {\n      padding: 24px;\n      border-radius: 24px;\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden;\n    }\n\n    .tc-fractal-benefits-embed-v1:hover {\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n      transform: translateY(-2px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-header {\n      margin: -24px -24px 24px -24px;\n      overflow: hidden;\n      position: relative;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-header img {\n      width: 100%;\n      display: block;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-header::after {\n      content: '';\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 120px;\n      background: linear-gradient(to bottom, \n        rgba(255,255,255,0) 0%,\n        rgba(255,255,255,0.5) 50%,\n        rgba(255,255,255,0.8) 80%,\n        rgba(255,255,255,1) 100%\n      );\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-title {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-grid {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-item {\n      padding: 20px;\n      border-radius: 12px;\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.3s ease;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-item:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n      border-color: rgba(0, 0, 0, 0.16);\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-icon {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-item-title {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .tc-fractal-benefits-embed-v1 .tc-fb-text {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #000000;\n    }\n\n    @media (max-width: 640px) {\n      .tc-fractal-benefits-embed-v1 .tc-fb-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .tc-fractal-benefits-embed-v1 {\n        padding: 20px;\n      }\n      \n      .tc-fractal-benefits-embed-v1 .tc-fb-title {\n        font-size: 20px;\n      }\n      \n      .tc-fractal-benefits-embed-v1 .tc-fb-item {\n        padding: 15px;\n      }\n\n      .tc-fractal-benefits-embed-v1 .tc-fb-header {\n        margin: -20px -20px 20px -20px;\n        height: 300px;\n      }\n    }\n  </style>\n\n  <div class=\"tc-fb-header\">\n    <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/Tonecraft_66975096-b477-4d54-8427-1fd1881756c0.jpg?v=1724399096\" alt=\"Fractal Axe-Fx III Tonecraft\">\n  </div>\n\n  <h2 class=\"tc-fb-title\">Why Choose Develop Device Axe-Fx III Presets?</h2>\n  \n  <div class=\"tc-fb-grid\">\n    <div class=\"tc-fb-item\">\n      <svg class=\"tc-fb-icon\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z\"/>\n      </svg>\n      <h3 class=\"tc-fb-item-title\">Professional Quality</h3>\n      <p class=\"tc-fb-text\">Each preset is meticulously crafted and tested in professional studio environment.</p>\n    </div>\n\n    <div class=\"tc-fb-item\">\n      <svg class=\"tc-fb-icon\" viewBox=\"0 0 24 24\">\n        <path d=\"M21 10.12h-6.78l2.74-2.82c-2.73-2.7-7.15-2.8-9.88-.1-2.73 2.71-2.73 7.08 0 9.79s7.15 2.71 9.88 0C18.32 15.65 19 14.08 19 12.1h2c0 1.98-.88 4.55-2.64 6.29-3.51 3.48-9.21 3.48-12.72 0-3.5-3.47-3.53-9.11-.02-12.58s9.14-3.47 12.65 0L21 3v7.12zM12.5 8v4.25l3.5 2.08-.72 1.21L11 13V8h1.5z\"/>\n      </svg>\n      <h3 class=\"tc-fb-item-title\">Always Up-to-Date</h3>\n      <p class=\"tc-fb-text\">Regular updates ensure compatibility with the latest Axe-Fx III firmware versions.</p>\n    </div>\n\n    <div class=\"tc-fb-item\">\n      <svg class=\"tc-fb-icon\" viewBox=\"0 0 24 24\">\n        <path d=\"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM17 13l-5 5-5-5h3V9h4v4h3z\"/>\n      </svg>\n      <h3 class=\"tc-fb-item-title\">Instant Download</h3>\n      <p class=\"tc-fb-text\">Get immediate access to your high-quality presets after purchase.</p>\n    </div>\n\n    <div class=\"tc-fb-item\">\n      <svg class=\"tc-fb-icon\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"/>\n      </svg>\n      <h3 class=\"tc-fb-item-title\">Ready to Play</h3>\n      <p class=\"tc-fb-text\">Optimized settings for both live performance and studio recording.</p>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_Cf3NWy": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.title contains '<PERSON><PERSON>' %}\n<div class=\"kemper-benefits-24\">\n  <style>\n    .kemper-benefits-24 {\n      padding: 0;\n      border-radius: 24px; /* Změněno z 0 na 24px */\n      background: #ffffff;\n      border: 1px solid #f4f4f4;\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);\n      transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease; /* Přidána border-color do transition pro konzistenci, i když zde není hover efekt na border */\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden; /* Důležité pro oříznutí vnitřního obsahu podle border-radius */\n    }\n\n    .kemper-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      /* Okraje hero sekce budou oříznuty rodičovským prvkem .kemper-benefits-24 díky overflow: hidden */\n    }\n\n    .kemper-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      display: block;\n    }\n\n    .kemper-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 100px;\n      background: linear-gradient(180deg, \n        rgba(255, 255, 255, 0) 0%,\n        #ffffff 100% /* Může být i rgba(255, 255, 255, 1) pro plnou bílou */\n      );\n    }\n\n    .kemper-content-wrapper {\n      padding: 24px;\n      background: #ffffff;\n      /* Okraje content wrapperu budou oříznuty rodičovským prvkem .kemper-benefits-24 */\n    }\n\n    .kemper-benefits-24:hover {\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\n      transform: translateY(-2px);\n      /* border-radius: 24px; není nutné explicitně definovat, zdědí se */\n    }\n\n    .kemper-title-24 {\n      font-size: 24px;\n      font-weight: 700;\n      margin-bottom: 20px;\n      text-align: center;\n      color: #000000;\n    }\n\n    .kemper-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 20px;\n    }\n\n    @media (max-width: 640px) {\n      .kemper-grid-24 {\n        grid-template-columns: 1fr;\n      }\n      \n      .kemper-hero-section {\n        height: 300px;\n      }\n    }\n\n    .kemper-item-24 { /* TOTO JSOU \"TLAČÍTKA\" / POLOŽKY */\n      padding: 20px;\n      border-radius: 12px; /* Změněno z 0 na 12px */\n      background: #ffffff;\n      border: 1px solid #f4f4f4;\n      transition: all 0.3s ease;\n    }\n\n    .kemper-item-24:hover {\n      transform: translateY(-5px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n      /* Můžete zvážit i změnu border-color při hoveru, pokud chcete zvýraznit */\n      /* border-color: #e0e0e0; */\n    }\n\n    .kemper-icon-24 {\n      width: 40px;\n      height: 40px;\n      margin-bottom: 15px;\n      fill: #000000;\n    }\n\n    .kemper-item-title-24 {\n      font-size: 18px;\n      font-weight: 600;\n      margin-bottom: 10px;\n      color: #000000;\n    }\n\n    .kemper-text-24 {\n      font-size: 14px;\n      line-height: 1.5;\n      color: #000000;\n    }\n\n    @media (max-width: 768px) {\n      .kemper-content-wrapper {\n        padding: 20px;\n      }\n      \n      .kemper-title-24 {\n        font-size: 20px;\n      }\n      \n      .kemper-item-24 {\n        padding: 15px;\n      }\n    }\n  </style>\n\n  <div class=\"kemper-hero-section\">\n    <img \n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/192581754_967329620763789_591416218845238837_n_d56ed1b7-2f98-4776-be90-c2c3cb231369.jpg?v=1724397070\"\n      alt=\"Kemper Profiler\"\n      class=\"kemper-hero-image\"\n    />\n    <div class=\"kemper-gradient-overlay\"></div>\n  </div>\n  \n  <div class=\"kemper-content-wrapper\">\n    <h2 class=\"kemper-title-24\">Why Choose Develop Device Kemper Profiles?</h2>\n    \n    <div class=\"kemper-grid-24\">\n      <div class=\"kemper-item-24\">\n        <svg class=\"kemper-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n        </svg>\n        <h3 class=\"kemper-item-title-24\">Professional Quality</h3>\n        <p class=\"kemper-text-24\">Each profile is carefully tuned and tested to achieve perfect sound both in studio and on stage.</p>\n      </div>\n\n      <div class=\"kemper-item-24\">\n        <svg class=\"kemper-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\n        </svg>\n        <h3 class=\"kemper-item-title-24\">Studio-Grade Tone</h3>\n        <p class=\"kemper-text-24\">Captured using top-tier equipment in a professional studio environment for the best possible tone.</p>\n      </div>\n\n      <div class=\"kemper-item-24\">\n        <svg class=\"kemper-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.5 2.54l2.54 1.27c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-8.92-9.99zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05C5.94 2.55 2 6.81 2 12c0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z\"/>\n        </svg>\n        <h3 class=\"kemper-item-title-24\">Instant Download</h3>\n        <p class=\"kemper-text-24\">Get immediate access to your high-quality profile after purchase.</p>\n      </div>\n\n      <div class=\"kemper-item-24\">\n        <svg class=\"kemper-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 9h-2V5h2v6zm0 4h-2v-2h2v2z\"/>\n        </svg>\n        <h3 class=\"kemper-item-title-24\">Technical Support</h3>\n        <p class=\"kemper-text-24\">I provide complete support and tutorials for trouble-free profile usage.</p>\n      </div>\n    </div>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}, "custom_liquid_tLgmiP": {"type": "custom_liquid", "settings": {"custom_liquid": "{% if product.metafields.custom.required_toontrack_sdx_ezx_extensions != blank and product.title contains 'EZDrummer 3' %}\n<style>\n  :root {\n    --primary-color: #000000; /* Black */\n    --secondary-color: #000000; /* Black */\n    --text-primary: #000000;\n    --text-secondary: #333333;\n    --text-tertiary: #666666;\n    --border-color: #f4f4f4;\n    --shadow-sm: 0 2px 12px rgba(0,0,0,0.04);\n    --shadow-md: 0 4px 24px rgba(0,0,0,0.06);\n    --shadow-lg: 0 8px 24px rgba(0,0,0,0.12);\n    --spacing-base: 8px;\n  }\n\n  @keyframes borderPulse {\n    0% { border-color: #f4f4f4; } /* Můžete upravit barvy pro pulsaci, pokud je to žádoucí */\n    70% { border-color: #f4f4f4; }\n    100% { border-color: #f4f4f4; }\n  }\n\n  .required-extensions { /* HLAVNÍ SEKCE */\n    background: #ffffff;\n    padding: calc(var(--spacing-base) * 3);\n    margin: calc(var(--spacing-base) * 2.5) 0;\n    box-shadow: var(--shadow-md);\n    border: 1px solid var(--border-color);\n    border-radius: 24px; /* Přidáno zaoblení pro sekci */\n    overflow: hidden; /* Důležité pro oříznutí vnitřního obsahu podle border-radius */\n  }\n\n  .required-extensions__title {\n    color: #000000;\n    font-size: 24px;\n    font-weight: 600;\n    margin: 0 0 calc(var(--spacing-base) * 3);\n    padding-bottom: calc(var(--spacing-base) * 2);\n    border-bottom: 2px solid #000000;\n    position: relative;\n  }\n\n  .required-extensions__title::after {\n    content: '';\n    position: absolute;\n    bottom: -2px;\n    left: 0;\n    width: 180px;\n    height: 2px;\n    background: #000000;\n  }\n\n  .extensions-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: calc(var(--spacing-base) * 2.5);\n    margin-bottom: calc(var(--spacing-base) * 4);\n  }\n\n  .extension-card { /* KARTA ROZŠÍŘENÍ */\n    background: white;\n    padding: calc(var(--spacing-base) * 2.5);\n    display: flex;\n    align-items: center;\n    gap: calc(var(--spacing-base) * 2.5);\n    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;\n    box-shadow: var(--shadow-sm);\n    border: 1px solid #f4f4f4;\n    border-radius: 12px; /* Přidáno zaoblení pro kartu */\n  }\n\n  .extension-card:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n    border-color: #e0e0e0; /* Mírně tmavší border při hoveru pro lepší efekt */\n  }\n\n  .extension-card__image { /* OBRÁZEK V KARTĚ */\n    width: 90px;\n    height: 90px;\n    object-fit: cover;\n    box-shadow: var(--shadow-sm);\n    border-radius: 8px; /* Přidáno zaoblení pro obrázek */\n  }\n\n  .extension-card__content {\n    flex: 1;\n  }\n\n  .extension-card__title {\n    color: var(--text-primary);\n    font-size: 16px;\n    font-weight: 600;\n    margin-bottom: calc(var(--spacing-base) * 1.5);\n    line-height: 1.3;\n  }\n\n  .extension-card__button { /* TLAČÍTKO */\n    display: inline-flex;\n    align-items: center;\n    background: #000000;\n    color: white;\n    padding: calc(var(--spacing-base) * 1.25) calc(var(--spacing-base) * 2.5);\n    text-decoration: none;\n    font-size: 14px;\n    font-weight: 500;\n    transition: background-color 0.3s ease, transform 0.3s ease, color 0.3s ease; /* Přidána color transition */\n    border-radius: 12px; /* Přidáno zaoblení pro tlačítko */\n  }\n\n  .extension-card__button:hover {\n    background: #333333; /* Tmavší hover pro černé tlačítko */\n    color: white; /* Ujistit se, že text zůstane bílý */\n    transform: translateY(-1px);\n  }\n\n  .important-info { /* SEKCE DŮLEŽITÝCH INFORMACÍ */\n    background: #ffffff;\n    padding: calc(var(--spacing-base) * 3);\n    border: 1px solid #f4f4f4;\n    border-radius: 12px; /* Přidáno zaoblení */\n    /* overflow: hidden; Pokud by obsah přesahoval, ale zde by neměl být problém */\n  }\n\n  .important-info__header {\n    display: flex;\n    align-items: center;\n    gap: calc(var(--spacing-base) * 1.5);\n    margin-bottom: calc(var(--spacing-base) * 2);\n  }\n\n  .important-info__icon { /* IKONA VYKŘIČNÍKU */\n    background: #000000;\n    color: white;\n    width: 32px;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: bold;\n    font-size: 20px;\n    animation: borderPulse 2s infinite;\n    border-radius: 8px; /* Přidáno zaoblení */\n  }\n\n  .important-info__title {\n    color: #000000;\n    font-size: 18px;\n    font-weight: 600;\n    margin: 0;\n  }\n\n  .important-info__list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .important-info__item {\n    display: flex;\n    align-items: flex-start;\n    gap: calc(var(--spacing-base) * 1.5);\n    padding: calc(var(--spacing-base) * 1.5) 0;\n    border-bottom: 1px solid #f4f4f4;\n  }\n\n  .important-info__item:last-child {\n    border-bottom: none;\n  }\n\n  .important-info__bullet {\n    color: #000000;\n    font-size: 20px;\n    line-height: 1.2;\n  }\n\n  .important-info__text {\n    color: var(--text-secondary);\n    font-size: 14px;\n    line-height: 1.5;\n    flex: 1;\n  }\n\n  .important-info__text a {\n    font-weight: 600;\n    color: #000000;\n    text-decoration: underline;\n    transition: color 0.3s ease;\n  }\n\n  .important-info__text a:hover {\n    color: #666666;\n  }\n\n  .important-info__note {\n    margin-top: calc(var(--spacing-base) * 2);\n    padding-top: calc(var(--spacing-base) * 2);\n    border-top: 1px solid #f4f4f4;\n    font-size: 13px;\n    color: var(--text-tertiary);\n    font-style: italic;\n  }\n\n  .custom-template { /* PRO PŘÍPADNÉ POUŽITÍ */\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #f4f4f4;\n    background: rgba(0, 0, 0, 0.02);\n    border-radius: 12px; /* Přidáno zaoblení */\n  }\n\n  .custom-template p:first-child {\n    font-weight: bold;\n    margin-bottom: 10px;\n    color: #000000;\n  }\n\n  .custom-template a {\n    color: #000000;\n    text-decoration: underline;\n    transition: color 0.3s ease;\n  }\n\n  .custom-template a:hover {\n    color: #666666;\n  }\n</style>\n\n<div class=\"required-extensions\">\n  <h3 class=\"required-extensions__title\">Required Extensions</h3>\n  <div class=\"extensions-grid\">\n    {% for extension in product.metafields.custom.required_toontrack_sdx_ezx_extensions.value %}\n      <div class=\"extension-card\">\n        {% if extension.image %}\n          <img \n            src=\"{{ extension.image | image_url: width: 160 }}\"\n            alt=\"{{ extension.name }}\"\n            class=\"extension-card__image\"\n            width=\"90\"\n            height=\"90\"\n            loading=\"lazy\"\n          >\n        {% endif %}\n        <div class=\"extension-card__content\">\n          <h4 class=\"extension-card__title\">{{ extension.name }}</h4>\n          <a \n            href=\"{{ extension.link_to_sdx_ezx_extension }}\" \n            class=\"extension-card__button\"\n            target=\"_blank\"\n            rel=\"noopener\"\n          >\n            Learn More\n          </a>\n        </div>\n      </div>\n    {% endfor %}\n  </div>\n\n  <div class=\"important-info\">\n    <div class=\"important-info__header\">\n      <div class=\"important-info__icon\">!</div>\n      <h3 class=\"important-info__title\">Important Information Before You Buy:</h3>\n    </div>\n    <ul class=\"important-info__list\">\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">•</span>\n        <span class=\"important-info__text\">No software is included! You must own <a href=\"https://www.thomann.de/cz/toontrack_ezdrummer_3.htm\" target=\"_blank\" rel=\"noopener\">Toontrack EZdrummer 3</a> and all required EZX libraries listed above.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">•</span>\n        <span class=\"important-info__text\">Besides the required EZX libraries, you always need a full installation of the CORE library.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">•</span>\n        <span class=\"important-info__text\">Make sure all software is up to date via Toontrack Product Manager.</span>\n      </li>\n      <li class=\"important-info__item\">\n        <span class=\"important-info__bullet\">•</span>\n        <span class=\"important-info__text\">You must own a compatible Digital Audio Workstation (DAW) and any third-party plugins mentioned in the product description.</span>\n      </li>\n    </ul>\n    <p class=\"important-info__note\">Note: This is an independent third-party product and is not affiliated with, sponsored by, or endorsed by Toontrack Music AB.</p>\n  </div>\n</div>\n{% endif %}", "collapse_content": false, "collapse_open": false, "heading": ""}}}, "block_order": ["custom_badges_gjaQEc", "07b2f4eb-d5bd-46bf-8ef7-7c3a9dbff7b7", "c0b62d1f-e562-4b86-84cb-fc22c849e93e", "5a84f252-bc58-4ca7-8b59-5a727a61c21f", "custom_liquid_DPbPHM", "custom_liquid_LHpkF6", "custom_liquid_iF8XyT", "custom_liquid_Kb99Ae", "custom_liquid_HBB6V3", "custom_liquid_TVhBGy", "globo_product_option_app_block_6jhCWt", "a730fe0e-baa3-47bc-90c9-21c5a5d75f2d", "custom_liquid_wWVPBw", "custom_liquid_6K6yyi", "custom_liquid_QnFLU3", "custom_liquid_DcXV37", "custom_liquid_bYfdtq", "custom_liquid_UUKnQr", "custom_liquid_q6mpQW", "custom_liquid_mbBEcp", "custom_liquid_Cf3NWy", "custom_liquid_tLgmiP"], "settings": {"show_breadcrumbs": false, "desktop_media_width": 50, "media_size": "", "media_layout_desktop": "grid_main", "media_mobile_indicator": "thumbnails", "media_zoom": "lightbox", "media_enable_video_autoplay": false, "media_enable_video_looping": false, "media_fit_height": true, "show_sticky_add_to_cart": true, "sticky_add_to_cart_position": "right", "background_color": "rgba(0,0,0,0)", "text_color": "", "heading_color": ""}}, "main-details": {"type": "main-product-details", "blocks": {"5bbfd876-e943-47d3-b984-06f967dfd314": {"type": "description", "settings": {"collapse_content": false, "collapse_open": false, "heading": ""}}}, "block_order": ["5bbfd876-e943-47d3-b984-06f967dfd314"], "settings": {}}, "custom_liquid_KMaFaw": {"type": "custom-liquid", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% comment %}\n  Dynamic Product Categories Bubbles - Shopify Integration\n  This section dynamically displays collections and their product counts\n  ONLY for \"All-Access Pass\" product\n{% endcomment %}\n\n{% comment %}\n  Check if current product is \"All-Access Pass\" before displaying anything\n{% endcomment %}\n{% if product.title == \"All-Access Pass\" %}\n\n<style>\n  :root {\n    /* Category Bubbles Variables */\n    --bubble-space-xs: 0.25rem;\n    --bubble-space-sm: 0.375rem;\n    --bubble-space-md: 0.5rem;\n    --bubble-space-lg: 0.75rem;\n    --bubble-space-xl: 1rem;\n\n    --bubble-color-primary: #7D427D;\n    --bubble-color-secondary: #1980B7;\n    --bubble-color-text: #ffffff;\n    --bubble-color-text-muted: rgba(255, 255, 255, 0.85);\n    --bubble-color-bg-dark: #121212;\n    --bubble-color-bg-medium: #2a2a2a;\n    --bubble-color-bg-light: rgba(255, 255, 255, 0.05);\n    --bubble-color-bg-hover: rgba(255, 255, 255, 0.1);\n    --bubble-border-color: rgba(255, 255, 255, 0.15);\n    --bubble-border-color-hover: rgba(255, 255, 255, 0.25);\n\n    --bubble-font-size-xs: 0.7rem;\n    --bubble-font-size-sm: 0.8rem;\n    --bubble-font-size-base: 0.875rem;\n\n    --bubble-border-radius: 20px;\n    --bubble-border-radius-lg: 25px;\n  }\n\n  /* Main Categories Container */\n  .categories-bubbles-section {\n    padding: var(--bubble-space-xl) var(--bubble-space-md);\n    background: linear-gradient(135deg, var(--bubble-color-bg-dark) 0%, var(--bubble-color-bg-medium) 100%);\n    border-radius: 16px;\n    margin: var(--bubble-space-xl) auto;\n    max-width: 1800px;\n    position: relative;\n    overflow: hidden;\n  }\n\n  @media (min-width: 768px) {\n    .categories-bubbles-section {\n      padding: var(--bubble-space-xl);\n    }\n  }\n\n  /* Section Header */\n  .categories-header {\n    text-align: center;\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  .categories-title {\n    font-size: 1.5rem;\n    font-weight: 800;\n    color: var(--bubble-color-text);\n    margin: 0 0 var(--bubble-space-sm) 0;\n    background: linear-gradient(90deg, var(--bubble-color-text), var(--bubble-color-secondary));\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n\n  @media (min-width: 768px) {\n    .categories-title {\n      font-size: 1.75rem;\n    }\n  }\n\n  .categories-subtitle {\n    font-size: var(--bubble-font-size-base);\n    color: var(--bubble-color-text-muted);\n    margin: 0;\n  }\n\n  /* Categories Grid - Two columns for first two sections */\n  .categories-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: var(--bubble-space-lg);\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  @media (max-width: 767px) {\n    .categories-grid {\n      grid-template-columns: 1fr;\n      gap: var(--bubble-space-md);\n    }\n  }\n\n  /* Full Width Section for DAW Templates & Software */\n  .full-width-section {\n    display: block;\n    margin-bottom: var(--bubble-space-xl);\n  }\n\n  /* Category Group */\n  .category-group {\n    background: var(--bubble-color-bg-light);\n    border: 1px solid var(--bubble-border-color);\n    border-radius: 12px;\n    padding: var(--bubble-space-lg);\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n  }\n\n  .category-group:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n  }\n\n  .category-group-title {\n    font-size: var(--bubble-font-size-base);\n    font-weight: 700;\n    color: var(--bubble-color-text);\n    margin: 0 0 var(--bubble-space-md) 0;\n    display: flex;\n    align-items: center;\n    gap: var(--bubble-space-sm);\n  }\n\n  .category-icon {\n    width: 20px;\n    height: 20px;\n    color: var(--bubble-color-secondary);\n    flex-shrink: 0;\n  }\n\n  /* Bubbles Container */\n  .bubbles-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: var(--bubble-space-sm);\n  }\n\n  /* Full width bubbles container with better spacing */\n  .full-width-section .bubbles-container {\n    justify-content: center;\n    gap: var(--bubble-space-md);\n  }\n\n  @media (min-width: 768px) {\n    .full-width-section .bubbles-container {\n      gap: var(--bubble-space-lg);\n    }\n  }\n\n  /* Individual Bubble */\n  .category-bubble {\n    display: inline-flex;\n    align-items: center;\n    gap: var(--bubble-space-xs);\n    padding: var(--bubble-space-xs) var(--bubble-space-md);\n    background: var(--bubble-color-bg-light);\n    border: 1px solid var(--bubble-border-color);\n    border-radius: var(--bubble-border-radius);\n    color: var(--bubble-color-text-muted);\n    font-size: var(--bubble-font-size-sm);\n    font-weight: 500;\n    text-decoration: none;\n    transition: all 0.2s ease;\n    cursor: pointer;\n    white-space: nowrap;\n  }\n\n  .category-bubble:hover {\n    background: var(--bubble-color-bg-hover);\n    border-color: var(--bubble-border-color-hover);\n    color: var(--bubble-color-text);\n    transform: translateY(-1px);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  }\n\n  /* Featured/Popular Bubble */\n  .category-bubble.featured {\n    background: linear-gradient(135deg, var(--bubble-color-primary), #9d67ad);\n    border-color: var(--bubble-color-primary);\n    color: var(--bubble-color-text);\n    font-weight: 600;\n  }\n\n  .category-bubble.featured:hover {\n    background: linear-gradient(135deg, #9d67ad, var(--bubble-color-primary));\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(125, 66, 125, 0.3);\n  }\n\n  /* New Collection Badge */\n  .category-bubble.new::after {\n    content: \"NEW\";\n    font-size: var(--bubble-font-size-xs);\n    font-weight: 700;\n    color: var(--bubble-color-text);\n    background: var(--bubble-color-secondary);\n    padding: 1px var(--bubble-space-xs);\n    border-radius: 8px;\n    margin-left: var(--bubble-space-xs);\n  }\n\n  /* Popular Badge */\n  .category-bubble.popular::after {\n    content: \"🔥\";\n    margin-left: var(--bubble-space-xs);\n  }\n\n  /* Small Bubble Variant */\n  .category-bubble.small {\n    padding: 2px var(--bubble-space-sm);\n    font-size: var(--bubble-font-size-xs);\n    border-radius: 15px;\n  }\n\n  /* Count Badge */\n  .bubble-count {\n    background: rgba(255, 255, 255, 0.2);\n    color: var(--bubble-color-text);\n    font-size: var(--bubble-font-size-xs);\n    font-weight: 600;\n    padding: 1px var(--bubble-space-xs);\n    border-radius: 10px;\n    margin-left: var(--bubble-space-xs);\n  }\n\n  /* Active state */\n  .category-bubble.active {\n    background: var(--bubble-color-primary) !important;\n    border-color: var(--bubble-color-primary) !important;\n    color: var(--bubble-color-text) !important;\n    transform: translateY(-1px);\n    box-shadow: 0 3px 10px rgba(125, 66, 125, 0.4);\n  }\n\n  /* All Categories Row */\n  .all-categories-row {\n    display: flex;\n    flex-wrap: wrap;\n    gap: var(--bubble-space-xs);\n    justify-content: center;\n    padding-top: var(--bubble-space-lg);\n    border-top: 1px solid var(--bubble-border-color);\n  }\n\n  @media (min-width: 768px) {\n    .all-categories-row {\n      gap: var(--bubble-space-sm);\n    }\n  }\n\n  /* Empty state */\n  .category-group.empty {\n    opacity: 0.5;\n  }\n\n  .no-products-message {\n    color: var(--bubble-color-text-muted);\n    font-style: italic;\n    font-size: var(--bubble-font-size-sm);\n  }\n\n  /* Value Arguments */\n  .value-arguments {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: var(--bubble-space-lg);\n    margin: var(--bubble-space-xl) 0;\n    padding: var(--bubble-space-lg);\n    background: var(--bubble-color-bg-light);\n    border-radius: 12px;\n    border: 1px solid var(--bubble-border-color);\n  }\n\n  @media (max-width: 767px) {\n    .value-arguments {\n      grid-template-columns: 1fr;\n      gap: var(--bubble-space-md);\n      padding: var(--bubble-space-md);\n    }\n  }\n\n  .value-argument {\n    display: flex;\n    align-items: flex-start;\n    gap: var(--bubble-space-md);\n    padding: var(--bubble-space-md);\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: var(--bubble-border-radius-sm);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n  }\n\n  .value-argument:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n  }\n\n  .value-icon {\n    font-size: 1.5rem;\n    flex-shrink: 0;\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: linear-gradient(135deg, var(--bubble-color-primary), var(--bubble-color-secondary));\n    border-radius: 50%;\n    margin-top: 2px;\n  }\n\n  .value-content {\n    flex-grow: 1;\n  }\n\n  .value-content strong {\n    display: block;\n    font-size: var(--bubble-font-size-base);\n    font-weight: 700;\n    color: var(--bubble-color-text);\n    margin-bottom: var(--bubble-space-xs);\n  }\n\n  .value-content span {\n    font-size: var(--bubble-font-size-sm);\n    color: var(--bubble-color-text-muted);\n    line-height: 1.4;\n  }\n  \n  @media (max-width: 480px) {\n    .categories-bubbles-section {\n      padding: var(--bubble-space-md);\n    }\n    \n    .category-group {\n      padding: var(--bubble-space-md);\n    }\n    \n    .categories-title {\n      font-size: 1.25rem;\n    }\n    \n    .bubbles-container {\n      gap: var(--bubble-space-xs);\n    }\n    \n    .category-bubble {\n      font-size: var(--bubble-font-size-xs);\n      padding: var(--bubble-space-xs) var(--bubble-space-sm);\n    }\n  }\n</style>\n\n{% comment %}\n  Define category mappings and featured collections\n  You can customize these arrays based on your actual collection handles\n{% endcomment %}\n\n{% assign guitar_collections = 'presets-and-cabinet-irs-for-fractal-audio-units,fractal-axe-fx-iii-presets,fractal-axe-fx-ii-presets,fractal-fm3-presets,fractal-ax8-presets,neural-dsp-quad-cortex-captures,neural-dsp-presets-and-irs,kemper-profiles,line-6-helix-presets,line-6-hx-stomp-presets,line-6-pod-go,line-6-metallurgy-presets,boss-gt-1000-1000core-presets,hotone-ampero,headrush-mx5-pedalboard-gigboard-presets,ik-multimedia-tonex-captures,mooer-mnrs-profiles,nux-mg-30-presets,amplitube-5-presets,tonecraft-guitar-bass-patches' | split: ',' %}\n\n{% assign drum_collections = 'superior-drummer-3-presets,artist-presets-for-superior-drummer-3,ezdrummer-3-templates,ezdrummer-3-core-library,ezdrummer-2-library,ggd-drum-templates,perfect-drums-templates,modo-drum-presets,addictive-drums-2-presets' | split: ',' %}\n\n{% assign software_collections = 'cabinet-irs,daw-templates,cubase-templates,pro-tools-premixed-templates,reaper-templates,studio-one-premixed-templates,fl-studio-premixed-templates,ableton-live-premixed-templates,logic-pro-templates,apps' | split: ',' %}\n\n{% comment %}\n  Featured collections (will get special styling)\n  Replace these handles with your actual high-priority collection handles\n{% endcomment %}\n{% assign featured_collections = 'superior-drummer-3-presets,fractal-axe-fx-iii-presets,neural-dsp-quad-cortex-captures,ezdrummer-3-templates,kemper-profiles,daw-templates' | split: ',' %}\n\n{% comment %}\n  New collections (will get NEW badge)\n  Replace with recently added collection handles\n{% endcomment %}\n{% assign new_collections = 'neural-dsp-quad-cortex-captures,ik-multimedia-tonex-captures,nux-mg-30-presets,apps' | split: ',' %}\n\n{% comment %}\n  Popular collections (will get fire emoji)\n  Replace with your best-selling collection handles\n{% endcomment %}\n{% assign popular_collections = 'neural-dsp-quad-cortex-captures,fractal-axe-fx-iii-presets,superior-drummer-3-presets' | split: ',' %}\n\n<section class=\"categories-bubbles-section\">\n  <div class=\"categories-header\">\n    <h2 class=\"categories-title\">What's Included</h2>\n    <p class=\"categories-subtitle\">Instant access to our entire arsenal - thousands of professional presets across dozens of platforms</p>\n    <div style=\"text-align: center; margin: var(--bubble-space-md) 0; padding: var(--bubble-space-md) var(--bubble-space-lg); background: linear-gradient(135deg, var(--bubble-color-primary), var(--bubble-color-secondary)); border-radius: 8px; color: var(--bubble-color-text);\">\n      <span style=\"font-size: 1.75rem; font-weight: 900; margin-right: var(--bubble-space-xs);\">€25,689</span>\n      <span style=\"font-size: var(--bubble-font-size-sm); opacity: 0.9;\">Total Value (Regular Prices)</span>\n    </div>\n    \n    <!-- Urgency & Value Arguments -->\n    <div class=\"value-arguments\">\n      <div class=\"value-argument\">\n        <div class=\"value-icon\">💰</div>\n        <div class=\"value-content\">\n          <strong>Save Thousands</strong>\n          <span>Buying individually would cost thousands. Get everything for just one annual price.</span>\n        </div>\n      </div>\n      <div class=\"value-argument\">\n        <div class=\"value-icon\">⚡</div>\n        <div class=\"value-content\">\n          <strong>Ready in Seconds</strong>\n          <span>Download instantly. Load any preset and start creating professional music immediately.</span>\n        </div>\n      </div>\n      <div class=\"value-argument\">\n        <div class=\"value-icon\">🎯</div>\n        <div class=\"value-content\">\n          <strong>Used by Pros</strong>\n          <span>Artists from major metal bands and professional producers use these exact presets.</span>\n        </div>\n      </div>\n      <div class=\"value-argument\">\n        <div class=\"value-icon\">🔒</div>\n        <div class=\"value-content\">\n          <strong>Keep Forever</strong>\n          <span>Download everything during your membership. All products remain yours permanently, even if you choose not to renew your annual membership.</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Two-column grid for first two sections -->\n  <div class=\"categories-grid\">\n    <!-- Drum Software & Templates -->\n    <div class=\"category-group{% if drum_collections.size == 0 %} empty{% endif %}\">\n      <h3 class=\"category-group-title\">\n        <svg class=\"category-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n          <path d=\"M12 6v6l4 2\"/>\n        </svg>\n        Drum Software & Templates\n      </h3>\n      <div class=\"bubbles-container\">\n        {% if drum_collections.size > 0 %}\n          {% for collection_handle in drum_collections %}\n            {% assign collection = collections[collection_handle] %}\n            {% if collection and collection.products.size > 0 %}\n              {% assign is_featured = false %}\n              {% assign is_new = false %}\n              {% assign is_popular = false %}\n              \n              {% if featured_collections contains collection_handle %}\n                {% assign is_featured = true %}\n              {% endif %}\n              \n              {% if new_collections contains collection_handle %}\n                {% assign is_new = true %}\n              {% endif %}\n              \n              {% if popular_collections contains collection_handle %}\n                {% assign is_popular = true %}\n              {% endif %}\n              \n              <a href=\"{{ collection.url }}\" \n                 class=\"category-bubble{% if is_featured %} featured{% endif %}{% if is_new %} new{% endif %}{% if is_popular %} popular{% endif %}\"\n                 data-collection-handle=\"{{ collection_handle }}\"\n                 data-product-count=\"{{ collection.products.size }}\">\n                {{ collection.title }}\n                <span class=\"bubble-count\">{{ collection.products.size }}+</span>\n              </a>\n            {% endif %}\n          {% endfor %}\n        {% else %}\n          <p class=\"no-products-message\">No drum software collections found.</p>\n        {% endif %}\n      </div>\n    </div>\n\n    <!-- Guitar Processors & Amps -->\n    <div class=\"category-group{% if guitar_collections.size == 0 %} empty{% endif %}\">\n      <h3 class=\"category-group-title\">\n        <svg class=\"category-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <path d=\"M12 2v20M2 7l10-5 10 5-10 5z\"/>\n        </svg>\n        Guitar Processors & Amps\n      </h3>\n      <div class=\"bubbles-container\">\n        {% if guitar_collections.size > 0 %}\n          {% for collection_handle in guitar_collections %}\n            {% assign collection = collections[collection_handle] %}\n            {% if collection and collection.products.size > 0 %}\n              {% assign is_featured = false %}\n              {% assign is_new = false %}\n              {% assign is_popular = false %}\n              \n              {% if featured_collections contains collection_handle %}\n                {% assign is_featured = true %}\n              {% endif %}\n              \n              {% if new_collections contains collection_handle %}\n                {% assign is_new = true %}\n              {% endif %}\n              \n              {% if popular_collections contains collection_handle %}\n                {% assign is_popular = true %}\n              {% endif %}\n              \n              <a href=\"{{ collection.url }}\" \n                 class=\"category-bubble{% if is_featured %} featured{% endif %}{% if is_new %} new{% endif %}{% if is_popular %} popular{% endif %}\"\n                 data-collection-handle=\"{{ collection_handle }}\"\n                 data-product-count=\"{{ collection.products.size }}\">\n                {{ collection.title }}\n                <span class=\"bubble-count\">{{ collection.products.size }}+</span>\n              </a>\n            {% endif %}\n          {% endfor %}\n        {% else %}\n          <p class=\"no-products-message\">No guitar processor collections found.</p>\n        {% endif %}\n      </div>\n    </div>\n  </div>\n\n  <!-- Full-width section for DAW Templates & Software -->\n  <div class=\"full-width-section\">\n    <div class=\"category-group{% if software_collections.size == 0 %} empty{% endif %}\">\n      <h3 class=\"category-group-title\">\n        <svg class=\"category-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"/>\n          <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"/>\n          <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"/>\n        </svg>\n        DAW Templates & Software\n      </h3>\n      <div class=\"bubbles-container\">\n        {% if software_collections.size > 0 %}\n          {% for collection_handle in software_collections %}\n            {% assign collection = collections[collection_handle] %}\n            {% if collection and collection.products.size > 0 %}\n              {% assign is_featured = false %}\n              {% assign is_new = false %}\n              {% assign is_popular = false %}\n              \n              {% if featured_collections contains collection_handle %}\n                {% assign is_featured = true %}\n              {% endif %}\n              \n              {% if new_collections contains collection_handle %}\n                {% assign is_new = true %}\n              {% endif %}\n              \n              {% if popular_collections contains collection_handle %}\n                {% assign is_popular = true %}\n              {% endif %}\n              \n              <a href=\"{{ collection.url }}\" \n                 class=\"category-bubble{% if is_featured %} featured{% endif %}{% if is_new %} new{% endif %}{% if is_popular %} popular{% endif %}\"\n                 data-collection-handle=\"{{ collection_handle }}\"\n                 data-product-count=\"{{ collection.products.size }}\">\n                {{ collection.title }}\n                <span class=\"bubble-count\">{{ collection.products.size }}+</span>\n              </a>\n            {% endif %}\n          {% endfor %}\n        {% else %}\n          <p class=\"no-products-message\">No software collections found.</p>\n        {% endif %}\n      </div>\n    </div>\n  </div>\n</section>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n  // Add click handlers for category bubbles\n  const categoryBubbles = document.querySelectorAll('.category-bubble');\n  \n  categoryBubbles.forEach(bubble => {\n    bubble.addEventListener('click', function(e) {\n      // Don't prevent default since we want normal navigation to work\n      // but add visual feedback\n      \n      // Get collection data\n      const collectionHandle = this.dataset.collectionHandle;\n      const productCount = this.dataset.productCount;\n      \n      console.log('Selected collection:', collectionHandle, 'Products:', productCount);\n      \n      // Add temporary visual feedback\n      this.style.transform = 'scale(0.95)';\n      setTimeout(() => {\n        this.style.transform = '';\n      }, 150);\n      \n      // Track analytics if needed\n      if (typeof gtag !== 'undefined') {\n        gtag('event', 'collection_click', {\n          'collection_handle': collectionHandle,\n          'product_count': productCount\n        });\n      }\n    });\n    \n    // Add hover analytics tracking\n    bubble.addEventListener('mouseenter', function() {\n      const collectionHandle = this.dataset.collectionHandle;\n      // Track collection hover events for analytics\n      console.log('Hovered collection:', collectionHandle);\n    });\n  });\n  \n  // Initialize current collection highlighting\n  const currentPath = window.location.pathname;\n  categoryBubbles.forEach(bubble => {\n    const bubbleHref = bubble.getAttribute('href');\n    if (bubbleHref && currentPath.includes(bubbleHref)) {\n      bubble.classList.add('active');\n    }\n  });\n});\n</script>\n\n{% comment %}\n  IMPORTANT: Close the conditional statement\n  This ensures the entire section only displays for \"All-Access Pass\" product\n{% endcomment %}\n{% endif %}\n\n{% comment %}\n  Instructions for implementation:\n  \n  1. Replace collection handles in the arrays at the top with your actual Shopify collection handles\n  \n  2. Customize the featured_collections, new_collections, and popular_collections arrays \n     to match your business priorities\n  \n  3. Add any custom collection metafields if you want additional data:\n     - collection.metafields.custom.priority_level\n     - collection.metafields.custom.category_icon\n     - collection.metafields.custom.is_featured\n  \n  4. Optional: Add collection images to bubbles:\n     {% if collection.image %}\n       <img src=\"{{ collection.image | image_url: width: 20 }}\" alt=\"{{ collection.title }}\" style=\"width: 16px; height: 16px; margin-right: 4px;\">\n     {% endif %}\n  \n  5. For better performance with many collections, consider pagination or limiting results\n  \n  6. Alternative conditions you can use:\n     - Check by product handle: {% if product.handle == \"all-access-pass\" %}\n     - Check by product ID: {% if product.id == 123456789 %}\n     - Check by product tag: {% if product.tags contains \"all-access\" %}\n     - Check by product type: {% if product.type == \"Membership\" %}\n{% endcomment %}", "background_color": "", "text_color": ""}}, "custom_liquid_hNBzAn": {"type": "custom-liquid", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.title == \"Analog Mixing & Mastering\" %}\n  <div class=\"image-gallery\" style=\"display: flex; flex-wrap: wrap; justify-content: space-between; max-width: 1800px; margin: 20px auto;\">\n    <div class=\"gallery-item\" style=\"flex: 0 0 32%; margin-bottom: 15px; background-color: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 4px; padding: 15px;\">\n      <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/summary_tile_Jaroslav_Holub.png?v=1743579997&width=832\" alt=\"Summary\" style=\"width: 100%; height: auto; display: block;\">\n    </div>\n    <div class=\"gallery-item\" style=\"flex: 0 0 32%; margin-bottom: 15px; background-color: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 4px; padding: 15px;\">\n      <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/charts_tile_Jaroslav_Holub.png?v=1743579996&width=832\" alt=\"Charts\" style=\"width: 100%; height: auto; display: block;\">\n    </div>\n    <div class=\"gallery-item\" style=\"flex: 0 0 32%; margin-bottom: 15px; background-color: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 4px; padding: 15px;\">\n      <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/credits_tile_Jaroslav_Holub.png?v=1743579996&width=832\" alt=\"Credits\" style=\"width: 100%; height: auto; display: block;\">\n    </div>\n  </div>\n{% endif %}", "background_color": "", "text_color": ""}}, "custom_liquid_YgtwTk": {"type": "custom-liquid", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.title == \"Analog Mixing & Mastering\" %}\n<div class=\"analog-hardware-section\">\n  <h2 class=\"hardware-title\">Analog Hardware That I Use for Mixing and Mastering</h2>\n\n  <div class=\"hardware-grid\">\n    <a href=\"https://www.thomann.de/gr/manley_massiv_passiv_stereo_mastering.htm\" target=\"_blank\" class=\"hardware-link\">\n      <div class=\"hardware-item\">\n        <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/17221559_800.jpg?v=1724397057&width=832\" alt=\"Manley Massive Passive Mastering\" class=\"hardware-image\">\n        <p class=\"hardware-name\">Manley Massive Passive Mastering</p>\n      </div>\n    </a>\n\n    <a href=\"https://www.thomann.de/gr/manley_variable_mu_mastering.htm\" target=\"_blank\" class=\"hardware-link\">\n      <div class=\"hardware-item\">\n        <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/17223103_800.jpg?v=1724397060&width=832\" alt=\"Manley Variable MU Mastering\" class=\"hardware-image\">\n        <p class=\"hardware-name\">Manley Variable MU Mastering</p>\n      </div>\n    </a>\n\n    <a href=\"https://www.thomann.de/gr/rupert_neve_designs_master_buss_processor_black.htm\" target=\"_blank\" class=\"hardware-link\">\n      <div class=\"hardware-item\">\n        <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/16712574_800.jpg?v=1724397052&width=832\" alt=\"Rupert Neve Designs Portico II Master Buss\" class=\"hardware-image\">\n        <p class=\"hardware-name\">Rupert Neve Designs<br>Portico II Master Buss</p>\n      </div>\n    </a>\n\n    <a href=\"https://www.thomann.de/gr/ssl_bus.htm\" target=\"_blank\" class=\"hardware-link\">\n      <div class=\"hardware-item\">\n        <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/17141393_800.jpg?v=1724397055&width=832\" alt=\"SSL BUS+\" class=\"hardware-image\">\n        <p class=\"hardware-name\">SSL BUS+</p>\n      </div>\n    </a>\n\n    <a href=\"https://www.thomann.de/gr/ssl_fusion.htm\" target=\"_blank\" class=\"hardware-link\">\n      <div class=\"hardware-item\">\n        <img src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/14008437_800.jpg?v=1724397051&width=832\" alt=\"SSL Fusion\" class=\"hardware-image\">\n        <p class=\"hardware-name\">SSL Fusion</p>\n      </div>\n    </a>\n  </div>\n</div>\n\n<style>\n  .analog-hardware-section {\n    margin: 60px 0;\n    text-align: center;\n    max-width: 1800px; /* Maximální šířka je nastavena zde */\n    margin-left: auto;\n    margin-right: auto; /* Centrálce elementu */\n    padding: 20px;\n    background-color: #f4f4f4;\n    border-radius: 24px; /* === ZAOBLENÍ SEKCE === */\n  }\n\n  .hardware-title {\n    font-size: 28px;\n    font-weight: 700;\n    margin-bottom: 40px;\n    color: #333;\n  }\n\n  .hardware-grid {\n    display: flex;\n    flex-wrap: nowrap;\n    justify-content: space-between;\n    gap: 20px;\n  }\n\n  .hardware-item { /* Toto je považováno za \"tlačítko\" nebo kartu */\n    flex: 1 1 0;\n    min-width: 0;\n    background-color: #fff;    /* Přidáno bílé pozadí pro vizuální efekt karty */\n    border-radius: 12px;       /* === ZAOBLENÍ \"TLAČÍTKA\" === */\n    overflow: hidden;          /* Zajistí, že obrázek bude oříznut do zaoblených rohů */\n    /* Pokud by položky měly různou výšku textu a chtěli byste je zarovnat: */\n    /* display: flex; */\n    /* flex-direction: column; */\n    /* height: 100%; */\n  }\n\n  .hardware-image {\n    width: 100%;\n    height: auto;\n    margin-bottom: 15px; /* Odsazení mezi obrázkem a textem */\n    /* border-radius: 4px; /* Odstraněno - rodič .hardware-item se stará o oříznutí */\n    display: block; /* Doporučené pro obrázky, aby se zabránilo nechtěnému prostoru pod nimi */\n    transition: transform 0.3s ease;\n  }\n\n  .hardware-image:hover {\n    transform: scale(1.03);\n  }\n\n  .hardware-name {\n    font-size: 14px;\n    font-weight: 600;\n    color: #333;\n    margin: 0;\n    padding: 0 15px 15px; /* Horní padding 0 (odsazení řeší margin obrázku), L/R 15px, Dolní 15px */\n    text-align: center;   /* Zajistí centrování textu, pokud by byl delší */\n    /* Pokud by .hardware-item byl flex kontejner (column): */\n    /* margin-top: auto; /* Posunulo by text na spodek karty, pokud by obrázky měly různou výšku */\n  }\n\n  .hardware-link {\n    text-decoration: none;\n    color: inherit;\n    display: block;\n    flex: 1 1 0;\n    min-width: 0;\n  }\n\n  @media (max-width: 992px) {\n    .hardware-grid {\n      flex-wrap: wrap;\n      justify-content: center;\n    }\n\n    .hardware-link {\n      flex: 0 0 auto;\n      width: calc(50% - 20px); /* Pozor: toto může znamenat větší mezeru než 20px mezi prvky */\n                               /* Pro mezeru 20px by bylo spíše calc(50% - 10px) */\n    }\n\n    .hardware-item {\n      width: 100%; /* .hardware-item vyplní .hardware-link */\n    }\n  }\n\n  @media (max-width: 576px) {\n    .hardware-link {\n      width: 100%;\n      max-width: 300px; /* Omezuje šířku na menších obrazovkách */\n    }\n\n    .hardware-title {\n      font-size: 22px;\n    }\n  }\n</style>\n{% endif %}", "background_color": "rgba(0,0,0,0)", "text_color": ""}}, "custom_liquid_wKND9g": {"type": "custom-liquid", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.title == \"Analog Mixing & Mastering\" %}\n<style>\n    /* Existují<PERSON><PERSON> styly */\n    .dd-reset {\n        box-sizing: border-box;\n        margin: 0;\n        padding: 0;\n    }\n\n    .dd-body {\n        line-height: 1.6;\n    }\n\n    /* Kontejner pro maximální šířku a centrování */\n    .dd-container {\n        max-width: 1800px; /* Maximální šířka celého bloku */\n        margin: 0 auto; /* Centrování bloku */\n        /* background-color: #f4f4f4; - TOTO BYLO ODSTRANĚNO */\n        padding: 0; /* Odebrání výchozího odsazení */\n        /* Volitelně můžete přidat padding zde, pokud chcete mezery mezi okrajem .dd-container a bílými sekcemi */\n        /* padding: 20px 0; */\n    }\n\n    /* Styl pro jednotlivé sekce video/text */\n    .dd-video-with-text {\n        display: flex;\n        padding: 3rem 2rem; /* Vnitřní odsazení BÍLÉ sekce */\n        background-color: #ffffff; /* Pozadí samotné sekce je bílé */\n        margin-bottom: 2rem; /* Mezera MEZI jednotlivými BÍLÝMI sekcemi */\n        border-radius: 24px; /* NOVÉ: Zaoblení rohů sekce */\n        /* max-width, margin-auto a background-color byly přesunuty do .dd-container a nyní je background-color v .dd-container odstraněno */\n    }\n\n    .dd-video-with-text.dd-text-right {\n        flex-direction: row-reverse;\n    }\n\n    .dd-video-container {\n        flex: 1;\n        padding: 1rem; /* Odsazení uvnitř kontejneru videa/textu */\n    }\n\n    /* 16:9 Aspect Ratio Container */\n    .dd-video-wrapper {\n        position: relative;\n        width: 100%;\n        height: 0;\n        padding-bottom: 56.25%; /* 16:9 aspect ratio (9/16 = 0.5625) */\n        overflow: hidden;\n        border-radius: 24px; /* UPRAVENO: Pro konzistenci se zaoblením sekce */\n        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n    }\n\n    .dd-video-wrapper iframe,\n    .dd-video-wrapper video {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n        border: none;\n    }\n\n    /* Custom YouTube poster images */\n    .yt-poster-parts::before {\n        content: \"\";\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-image: url('https://cdn.shopify.com/s/files/1/2599/2974/files/2024Album_FrontCover_Final.png?v=1728224192');\n        background-size: cover;\n        background-position: center;\n        z-index: 1;\n        cursor: pointer;\n    }\n\n    .yt-poster-tranquility::before {\n        content: \"\";\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-image: url('https://cdn.shopify.com/s/files/1/2599/2974/files/MCA_128_Preview.jpg?v=1727425825');\n        background-size: cover;\n        background-position: center;\n        z-index: 1;\n        cursor: pointer;\n    }\n\n    .yt-poster-parts iframe,\n    .yt-poster-tranquility iframe {\n        z-index: 2;\n        opacity: 0;\n    }\n\n    .yt-poster-parts:hover iframe,\n    .yt-poster-tranquility:hover iframe {\n        opacity: 1;\n    }\n\n    .yt-poster-parts:hover::before,\n    .yt-poster-tranquility:hover::before {\n        display: none;\n    }\n\n    .dd-text-container {\n        flex: 1;\n        padding: 2rem; /* Odsazení uvnitř kontejneru videa/textu */\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n    }\n\n    .dd-text-left .dd-text-container {\n        text-align: left;\n    }\n\n    .dd-text-right .dd-text-container {\n        text-align: right;\n    }\n\n    .dd-subheading {\n        font-size: 0.9rem;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n        margin-bottom: 0.5rem;\n        color: #1d1d1d; /* UPRAVENO */\n    }\n\n    .dd-heading {\n        font-size: 2.5rem;\n        font-weight: bold;\n        margin-bottom: 1rem;\n        color: #1d1d1d; /* UPRAVENO */\n    }\n\n    .dd-text p {\n        font-size: 1rem;\n        margin-bottom: 1rem;\n        color: #1d1d1d; /* UPRAVENO */\n    }\n\n    /* Hover effects (na bílých sekcích) */\n    .dd-video-with-text:hover {\n        transition: all 0.3s ease;\n        background-color: #f9f9f9;\n    }\n\n    /* Responsive styles */\n    @media (max-width: 768px) {\n        .dd-video-with-text,\n        .dd-video-with-text.dd-text-right {\n            flex-direction: column;\n        }\n\n        .dd-video-with-text {\n             padding: 1.5rem 1rem; /* Menší padding na mobilu uvnitř bílé sekce */\n        }\n\n        .dd-text-container {\n            text-align: center;\n            padding: 1rem; /* Menší padding na mobilu uvnitř textového kontejneru */\n        }\n\n        .dd-heading {\n            font-size: 2rem;\n        }\n    }\n</style>\n\n<!-- Kontejner pro nastavení maximální šířky a centrování (bez pozadí) -->\n<div class=\"dd-container\">\n\n    <!-- Parts (CAN) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper yt-poster-parts\">\n                <iframe src=\"https://www.youtube.com/embed/tJNd38VEWRk\" allowfullscreen></iframe>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Parts (CAN)</div>\n            <h1 class=\"dd-heading\">Parts</h1>\n            <div class=\"dd-text\"><p>Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Develop Device (CZ) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper yt-poster-tranquility\">\n                <iframe src=\"https://www.youtube.com/embed/iGMT7LjqnMU\" allowfullscreen></iframe>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Develop Device (CZ)</div>\n            <h1 class=\"dd-heading\">Tranquility</h1>\n            <div class=\"dd-text\"><p>Composing, Recording, Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Streambleed (AUT) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/a3663832826_16.jpg?v=1743775605\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/93a514b18d8f4afc863d5a0e31229b15/93a514b18d8f4afc863d5a0e31229b15.HD-1080p-2.5Mbps-23842933.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Streambleed (AUT)</div>\n            <h1 class=\"dd-heading\">Enslave the World Forever</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Indorsia (AUT) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/0012600431_10.webp?v=1724397031\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/c4d8e54afa9c45ee8053ca83225c9aad/c4d8e54afa9c45ee8053ca83225c9aad.HD-1080p-3.3Mbps-15317765.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Indorsia (AUT)</div>\n            <h1 class=\"dd-heading\">For the Lions to Feed</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Hooker Spit (CAN) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/a0734498856_16_9fd402df-18e0-4820-b3f6-5d562cddb9cd.jpg?v=1724397277\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/bf46dcc83da747469416913d2a45e5ce/bf46dcc83da747469416913d2a45e5ce.HD-1080p-4.8Mbps-23842932.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Hooker Spit (CAN)</div>\n            <h1 class=\"dd-heading\">Krötch Splitter</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Revolt (DE) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/a2037872636_10.webp?v=1724397282\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/5a0c7a7d53204bdc96e5a3e7568b9fca/5a0c7a7d53204bdc96e5a3e7568b9fca.HD-1080p-3.3Mbps-23845665.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Revolt (DE)</div>\n            <h1 class=\"dd-heading\">Out Of Breath</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Out of Nowhere (IRN) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/0030623820_10.webp?v=1724397036\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/251a778a89db4f82bf72f5f2d98bb8c1/251a778a89db4f82bf72f5f2d98bb8c1.HD-1080p-4.8Mbps-15317264.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Out of Nowhere (IRN)</div>\n            <h1 class=\"dd-heading\">Disconnect</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Hooves of Steel (SWE) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/maxresdefault_89f1d67b-c7c7-46ba-ab0e-3ea56876cfd1.webp?v=1724398427\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/01845f7aae1e4ad8a046a41f95bf4b07/01845f7aae1e4ad8a046a41f95bf4b07.HD-1080p-2.5Mbps-23842931.mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Hooves of Steel (SWE)</div>\n            <h1 class=\"dd-heading\">Epiquest</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Steve Dadaian (USA) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/steve-dadaian_c157ac94-826c-4131-966c-a612f7e515f4.webp?v=1724399035\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/3f45206f75db4f85934739f4bae94250/3f45206f75db4f85934739f4bae94250.HD-1080p-2.5Mbps-15317116.mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Steve Dadaian (USA)</div>\n            <h1 class=\"dd-heading\">Soul Connection (feat. Björn Speed Strid)</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- The Order Of The Gray (CAN) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/304044827_418494623722870_6900140340603913830_n.webp?v=1724397133\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/7a78d47a1b2f44bd897b65e3c7278f1b0/7a78d47a1b2f44bd897b65e3c7278f1b0.HD-1080p-2.5Mbps-15316665.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">The Order Of The Gray (CAN)</div>\n            <h1 class=\"dd-heading\">Retrovirus</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- VICTIMS (CZ) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/668539.webp?v=1724397237\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/3d1220611f44466b89ba627242b4d84b/3d1220611f44466b89ba627242b4d84b.HD-1080p-2.5Mbps-15316807.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">VICTIMS (CZ)</div>\n            <h1 class=\"dd-heading\">EVILution</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- New Hate Form (CZ) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/a3768620974_10_83015d07-581c-431f-988a-5d1fae32b5ef.jpg?v=1724397292\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/b83bf0bfed31499695c2c57476fb1f35/b83bf0bfed31499695c2c57476fb1f35.HD-720p-1.6Mbps-15316583.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">New Hate Form (CZ)</div>\n            <h1 class=\"dd-heading\">Stories Repeat</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Diesear (TWN) -->\n    <section class=\"dd-video-with-text dd-text-left\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/0015509172_10.webp?v=1724397033\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/e1f7dcd946f64678b0fef49eb4d3039b/e1f7dcd946f64678b0fef49eb4d3039b.HD-1080p-2.5Mbps-23845349.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Diesear (TWN)</div>\n            <h1 class=\"dd-heading\">Endless Dolour</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n    <!-- Every Hour Kills (CAN) -->\n    <section class=\"dd-video-with-text dd-text-right\">\n        <div class=\"dd-video-container\">\n            <div class=\"dd-video-wrapper\">\n                <video preload=\"metadata\" controls poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/a2741953220_10.webp?v=1724397286\">\n                    <source src=\"https://cdn.shopify.com/videos/c/vp/d263a538827b4d8882df68d9dd0f4c26/d263a538827b4d8882df68d9dd0f4c26.HD-1080p-2.5Mbps-15316539.mp4\" type=\"video/mp4\">\n                    Your browser does not support the video tag.\n                </video>\n            </div>\n        </div>\n        <div class=\"dd-text-container\">\n            <div class=\"dd-subheading\">Every Hour Kills (CAN)</div>\n            <h1 class=\"dd-heading\">Aldebaran</h1>\n            <div class=\"dd-text\"><p>Mixing & Mastering</p></div>\n        </div>\n    </section>\n\n</div> <!-- konec .dd-container -->\n\n{% endif %}", "background_color": "", "text_color": ""}}, "custom_liquid_J8AQJN": {"type": "custom-liquid", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.title == \"HumBeat: Advanced MIDI Drum Humanizer\" %}\n<div class=\"humbeat-guide\">\n  <style>\n      .humbeat-guide {\n          color: #f0f0f0;\n          position: relative;\n          width: 100%;\n          /* P<PERSON>id<PERSON>a mezera dole */\n          margin-bottom: 30px; \n      }\n\n      .humbeat-guide-wrapper {\n          position: relative;\n          width: 100%;\n          background-color: #1a1a2e;\n          /* Zaoblení hlav<PERSON> sekce */\n          border-radius: 24px; \n          overflow: hidden; /* Důležité pro správné zaoble<PERSON>í obsahu */\n      }\n\n      .humbeat-content {\n          max-width: 1800px;\n          padding: 2rem;\n          background-color: #1a1a2e;\n          position: relative;\n          /* Vycentrování obsahu */\n          margin: 0 auto; \n      }\n\n      .humbeat-guide h1 {\n          color: #00a3ff;\n          font-size: 2.5rem;\n          margin-bottom: 1rem;\n          text-align: center;\n          font-weight: 600;\n      }\n\n      .humbeat-guide .subtitle {\n          font-size: 1.1rem;\n          text-align: center;\n          margin-bottom: 3rem;\n          color: #f0f0f0;\n          font-weight: 300;\n      }\n\n      .humbeat-guide .step-container {\n          display: flex;\n          flex-direction: column;\n          gap: 1.5rem;\n          margin-bottom: 2rem;\n      }\n\n      .humbeat-guide .step-card {\n          background-color: #262647;\n          padding: 1.5rem;\n          border-left: 3px solid #00a3ff;\n          /* Zaoblení step karet */\n          border-radius: 24px; \n      }\n\n      .humbeat-guide .step-number {\n          display: inline-block;\n          background-color: #00a3ff;\n          color: white;\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          text-align: center;\n          line-height: 32px;\n          margin-right: 10px;\n          font-weight: bold;\n      }\n\n      .humbeat-guide .step-title {\n          font-size: 1.4rem;\n          color: white;\n          margin-bottom: 1rem;\n          display: flex;\n          align-items: center;\n      }\n\n      .humbeat-guide .feature-grid {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n          margin: 1.5rem 0;\n      }\n\n      .humbeat-guide .feature-card {\n          background-color: #2a2a52;\n          padding: 1.3rem;\n          border-left: 3px solid #00a3ff;\n          /* Zaoblení feature karet */\n          border-radius: 24px; \n      }\n\n      .humbeat-guide .feature-title {\n          font-size: 1.2rem;\n          color: white;\n          margin-bottom: 0.8rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n      }\n\n      .humbeat-guide .feature-icon {\n          font-size: 1.4rem;\n          color: #00a3ff;\n      }\n\n      .humbeat-guide .stats-container {\n          background-color: #262647;\n          padding: 1.3rem;\n          margin-bottom: 1.5rem;\n          border-left: 3px solid #00a3ff;\n          /* Zaoblení stats kontejneru */\n          border-radius: 24px; \n      }\n\n      .humbeat-guide .stats-list {\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n      }\n\n      .humbeat-guide .stats-item {\n          padding: 0.7rem;\n          background-color: #2a2a52;\n          border-left: 2px solid #00a3ff;\n      }\n\n      .humbeat-guide .pro-tips {\n          background-color: #262647;\n          padding: 1.3rem;\n          border-left: 3px solid #00a3ff;\n          /* Zaoblení pro-tips sekce */\n          border-radius: 24px; \n      }\n\n      .humbeat-guide .pro-tips h3 {\n          color: white;\n          margin-bottom: 1rem;\n          font-size: 1.3rem;\n      }\n\n      .humbeat-guide .tips-list {\n          display: flex;\n          flex-direction: column;\n          gap: 0.8rem;\n      }\n\n      .humbeat-guide .tip-item {\n          display: flex;\n          align-items: flex-start;\n          gap: 0.8rem;\n          padding: 0.5rem;\n      }\n\n      .humbeat-guide .tip-icon {\n          color: #00a3ff;\n          font-weight: bold;\n          font-size: 1.1rem;\n      }\n\n      .humbeat-guide ul li {\n          margin-bottom: 0.8rem;\n          list-style-type: none;\n          position: relative;\n          padding-left: 1.2rem;\n      }\n\n      .humbeat-guide ul li::before {\n          content: \"•\";\n          color: #00a3ff;\n          position: absolute;\n          left: 0;\n          font-size: 1.2rem;\n      }\n\n      .humbeat-guide strong {\n          color: #00a3ff;\n          font-weight: 600;\n      }\n\n      @media (max-width: 768px) {\n          .humbeat-content {\n              padding: 1rem;\n          }\n          \n          .humbeat-guide h1 {\n              font-size: 1.8rem;\n          }\n      }\n  </style>\n\n  <div class=\"humbeat-guide-wrapper\">\n      <div class=\"humbeat-content\">\n          <h1>HumBeat – Quick Start Guide</h1>\n          <p class=\"subtitle\">Turn Your MIDI Drums into a Natural Performance in Just a Few Clicks</p>\n\n          <div class=\"step-container\">\n              <div class=\"step-card\">\n                  <h2 class=\"step-title\"><span class=\"step-number\">1</span> Load Your MIDI File</h2>\n                  <ul>\n                      <li>Click \"Load MIDI\" or drag and drop your drum MIDI file into the HumBeat window.</li>\n                      <li>HumBeat supports all standard MIDI drum files (.mid, .midi) from any DAW or drum software.</li>\n                      <li>Once loaded, your MIDI drum track will be analyzed, and the interface will show the number of notes detected.</li>\n                  </ul>\n              </div>\n\n              <div class=\"step-card\">\n                  <h2 class=\"step-title\"><span class=\"step-number\">2</span> Adjust Humanization Settings</h2>\n                  <p>HumBeat provides four core humanization features—customize them to match your desired playing style:</p>\n                  \n                  <div class=\"feature-grid\">\n                      <div class=\"feature-card\">\n                          <h3 class=\"feature-title\"><span class=\"feature-icon\">•</span> Timing Humanization</h3>\n                          <p><strong>Subtle Fluctuations in Timing</strong></p>\n                          <ul>\n                              <li>Adds slight timing variations to notes to remove robotic precision.</li>\n                              <li>Use the \"Timing Variation\" slider to control how much variation is applied (0-20ms).</li>\n                              <li>Professional drummers typically fluctuate within ±5-15ms, so start with moderate settings.</li>\n                          </ul>\n                      </div>\n\n                      <div class=\"feature-card\">\n                          <h3 class=\"feature-title\"><span class=\"feature-icon\">•</span> Velocity Humanization</h3>\n                          <p><strong>Natural Dynamic Variations</strong></p>\n                          <ul>\n                              <li>Adjusts the intensity of each hit, making some softer and others louder for realism.</li>\n                              <li>The \"Velocity Variation\" slider controls the amount of randomness (0-10 velocity points).</li>\n                              <li>Works great for snare dynamics, cymbal accents, and tom fills.</li>\n                          </ul>\n                      </div>\n\n                      <div class=\"feature-card\">\n                          <h3 class=\"feature-title\"><span class=\"feature-icon\">•</span> Ghost Notes</h3>\n                          <p><strong>Enhance Groove & Feel</strong></p>\n                          <ul>\n                              <li>Adds subtle snare ghost notes around main snare hits to create a more natural groove.</li>\n                              <li>Set the \"Probability\" slider (0-100%) to control how often ghost notes appear.</li>\n                              <li>Adjust the \"Velocity\" of ghost notes for realistic volume (15-35 recommended).</li>\n                              <li>If your kit uses multiple snare samples, enable \"Use Additional Snare Notes\" and set the secondary snare note.</li>\n                          </ul>\n                      </div>\n\n                      <div class=\"feature-card\">\n                          <h3 class=\"feature-title\"><span class=\"feature-icon\">•</span> Kick Drum Alternation</h3>\n                          <p><strong>More Realistic Double Bass</strong></p>\n                          <ul>\n                              <li>Automatically alternates left and right kick for fast passages.</li>\n                              <li>Enable \"Alternate Left/Right Kick\" and select the Left Kick Note (default: 35) and Right Kick Note (default: 36).</li>\n                              <li>Choose a speed threshold (e.g., 32nd notes) at which the alternation will kick in.</li>\n                              <li>Adjust the \"Human Factor\" slider to simulate foot fatigue, natural accents, and dominance of the stronger foot.</li>\n                          </ul>\n                      </div>\n                  </div>\n              </div>\n\n              <div class=\"step-card\">\n                  <h2 class=\"step-title\"><span class=\"step-number\">3</span> Preview Changes (Optional)</h2>\n                  <p>Before saving, HumBeat shows real-time statistics on how your settings affect the MIDI:</p>\n                  \n                  <div class=\"stats-container\">\n                      <div class=\"stats-list\">\n                          <div class=\"stats-item\">Timing Randomness – % of notes affected by timing shifts.</div>\n                          <div class=\"stats-item\">Velocity Variation – % of notes affected by velocity changes.</div>\n                          <div class=\"stats-item\">Alternated Kicks – % of kick notes that are alternated.</div>\n                          <div class=\"stats-item\">Added Ghost Notes – % of new ghost notes added.</div>\n                      </div>\n                  </div>\n                  \n                  <p>Use these stats to fine-tune your settings before exporting.</p>\n              </div>\n\n              <div class=\"step-card\">\n                  <h2 class=\"step-title\"><span class=\"step-number\">4</span> Save Your Humanized MIDI</h2>\n                  <ul>\n                      <li>Click \"Save MIDI\", choose a file name, and select a location to save your new MIDI file.</li>\n                      <li>HumBeat keeps the original structure, tempo, and time signatures while applying humanization.</li>\n                      <li>The saved file is compatible with all DAWs, drum VSTs, and MIDI editors.</li>\n                  </ul>\n              </div>\n\n              <div class=\"step-card\">\n                  <h2 class=\"step-title\"><span class=\"step-number\">5</span> Load the Humanized MIDI Into Your DAW</h2>\n                  <ul>\n                      <li>Import the processed MIDI into your DAW or drum software.</li>\n                      <li>Assign it to your favorite drum plugin or sampler (Superior Drummer 3, GetGood Drums, Mixwave, SSD 5.5, etc.).</li>\n                      <li>Fine-tune the performance in your DAW if needed, but most users find no further editing required!</li>\n                  </ul>\n              </div>\n          </div>\n\n          <div class=\"pro-tips\">\n              <h3>Pro Tips for Best Results</h3>\n              <div class=\"tips-list\">\n                  <div class=\"tip-item\">\n                      <span class=\"tip-icon\">→</span>\n                      <p>For a natural feel, start with Timing ±5ms and Velocity ±3-5.</p>\n                  </div>\n                  <div class=\"tip-item\">\n                      <span class=\"tip-icon\">→</span>\n                      <p>Use ghost notes moderately (30-50% probability for rock; 60-80% for funk/R&B).</p>\n                  </div>\n                  <div class=\"tip-item\">\n                      <span class=\"tip-icon\">→</span>\n                      <p>For metal double bass, enable kick alternation and set 32nd note threshold.</p>\n                  </div>\n                  <div class=\"tip-item\">\n                      <span class=\"tip-icon\">→</span>\n                      <p>Combine multiple humanization types for the most realistic results.</p>\n                  </div>\n                  <div class=\"tip-item\">\n                      <span class=\"tip-icon\">→</span>\n                      <p>Use HumBeat on all MIDI drum tracks—even pre-programmed ones to enhance realism!</p>\n                  </div>\n              </div>\n          </div>\n      </div>\n  </div>\n</div>\n{% endif %}", "background_color": "", "text_color": ""}}, "custom_liquid_wLJNxr": {"type": "custom-liquid", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.type == \"Superior Drummer 3 Presets\" %}\n<style>\n.ddstudio-container * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\n.ddstudio-container {\n    margin-bottom: 30px; /* Přidána mezera pod celým blokem */\n}\n\n\n.ddstudio-background-section {\n    line-height: 1.6;\n    color: #000000;\n    position: relative;\n    background: #ffffff;\n    border-radius: 24px; /* <PERSON>de už je 24px, jak je p<PERSON> */\n    overflow: hidden;\n    padding: 3rem 2rem;\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2);\n}\n\n.ddstudio-background-section::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),\n        rgba(0, 0, 0, 0.1) 0%,\n        rgba(0, 0, 0, 0) 55%\n    );\n    opacity: 0;\n    transition: opacity 1s ease;\n    pointer-events: none;\n}\n\n.ddstudio-background-section.glow-active::before {\n    opacity: 1;\n}\n\n.ddstudio-main-wrapper {\n    width: 100%;\n    max-width: 1800px; /* <PERSON><PERSON><PERSON><PERSON> obsahu na 1800px */\n    margin: 0 auto;\n    position: relative;\n}\n\n.ddstudio-header {\n    text-align: center;\n    margin-bottom: 3.5rem;\n    animation: ddstudioFadeInUp 0.6s ease forwards;\n}\n\n.ddstudio-header__title {\n    font-size: 3.75rem;\n    margin-bottom: 1rem;\n    background: #000000;\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    font-weight: 800;\n    text-align: center;\n    letter-spacing: -0.02em;\n    line-height: 1.1;\n}\n\n.ddstudio-header__subtitle {\n    color: #333333;\n    font-size: 1.5rem;\n    text-align: center;\n    font-weight: 500;\n    margin-top: 0.75rem;\n    max-width: 800px;\n    margin-left: auto;\n    margin-right: auto;\n}\n\n.ddstudio-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 1.75rem;\n    width: 100%;\n}\n\n.ddstudio-card {\n    background: rgba(255, 255, 255, 0.7);\n    border-radius: 12px; /* Zde už je 12px, jak je požadováno */\n    overflow: hidden;\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(0, 0, 0, 0.1);\n    position: relative;\n    animation: ddstudioFadeInUp 0.6s ease forwards;\n    animation-fill-mode: both;\n    box-shadow:\n        0 0 0 rgba(255, 255, 255, 0),\n        0 0 0 rgba(0, 0, 0, 0);\n    transition: transform 0.5s ease, box-shadow 0.8s ease;\n}\n\n.ddstudio-card:hover {\n    transform: translateY(-7px);\n    box-shadow:\n        0 20px 40px rgba(0, 0, 0, 0.1),\n        0 0 25px rgba(0, 0, 0, 0.05);\n}\n\n.ddstudio-card::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    background: linear-gradient(180deg,\n        rgba(0, 0, 0, 0.05) 0%,\n        rgba(0, 0, 0, 0) 100%\n    );\n    opacity: 0;\n    transition: opacity 0.8s ease;\n}\n\n.ddstudio-card:hover::before {\n    opacity: 1;\n}\n\n.ddstudio-card__image-wrapper {\n    position: relative;\n    overflow: hidden;\n    border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n    background-color: #f5f5f5;\n    /* Poznámka: Pokud byste chtěli, aby horní rohy obrázku byly explicitně zaoblené\n       (i když `overflow: hidden` na .ddstudio-card by to mělo řešit),\n       mohli byste přidat:\n       border-top-left-radius: 12px;\n       border-top-right-radius: 12px;\n       Ale s `overflow: hidden` na rodiči by to nemělo být nutné. */\n}\n\n.ddstudio-card__image {\n    width: 100%;\n    height: auto;\n    display: block;\n    transition: transform 0.8s ease, filter 0.8s ease;\n    filter: brightness(0.95);\n}\n\n.ddstudio-card:hover .ddstudio-card__image {\n    transform: scale(1.05);\n    filter: brightness(1.05);\n}\n\n.ddstudio-card__content {\n    padding: 1.75rem;\n    text-align: left;\n    background: linear-gradient(180deg,\n        rgba(0, 0, 0, 0.02) 0%,\n        rgba(0, 0, 0, 0) 100%\n    );\n    min-height: 240px;\n    display: flex;\n    flex-direction: column;\n}\n\n.ddstudio-card__title {\n    font-size: 1.6rem;\n    font-weight: 700;\n    margin-bottom: 1.2rem;\n    background: #000000;\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    line-height: 1.2;\n    letter-spacing: -0.01em;\n}\n\n.ddstudio-card__text {\n    font-size: 1.05rem;\n    color: #000000;\n    line-height: 1.7;\n    text-align: left;\n    flex-grow: 1;\n}\n\n@keyframes ddstudioFadeInUp {\n    from {\n        opacity: 0;\n        transform: translateY(20px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n@media (max-width: 1400px) {\n    .ddstudio-grid {\n        grid-template-columns: repeat(2, 1fr);\n    }\n\n    .ddstudio-header__title {\n        font-size: 3rem;\n    }\n}\n\n@media (max-width: 768px) {\n    .ddstudio-grid {\n        grid-template-columns: 1fr;\n    }\n\n    .ddstudio-header__title {\n        font-size: 2.5rem;\n    }\n\n    .ddstudio-background-section {\n        padding: 2rem 1.5rem;\n    }\n}\n\n.ddstudio-card:nth-child(1) { animation-delay: 0.1s; }\n.ddstudio-card:nth-child(2) { animation-delay: 0.2s; }\n.ddstudio-card:nth-child(3) { animation-delay: 0.3s; }\n.ddstudio-card:nth-child(4) { animation-delay: 0.4s; }\n</style>\n\n<div class=\"ddstudio-container\">\n    <div class=\"ddstudio-background-section\">\n        <div class=\"ddstudio-main-wrapper\">\n            <header class=\"ddstudio-header\">\n                <h2 class=\"ddstudio-header__title\">Superior Drummer 3 Presets</h2>\n                <p class=\"ddstudio-header__subtitle\">by Develop Device Studio!</p>\n\n\n            </header>\n\n            <div class=\"ddstudio-grid\">\n                <div class=\"ddstudio-card\">\n\n                    <div class=\"ddstudio-card__image-wrapper\">\n                        <img src=\"https://developdevice.com/cdn/shop/files/180870429_10219415650446377_2484158734110943171_n_7bf2cf5e-5025-40cb-961b-66e1877aecb8.webp?v=1724397064&width=832\" alt=\"Drummer Setup\" class=\"ddstudio-card__image\">\n                    </div>\n                    <div class=\"ddstudio-card__content\">\n                        <h2 class=\"ddstudio-card__title\">Crafted by Jaroslav Holub, for Real Drummers</h2>\n                        <p class=\"ddstudio-card__text\">\n                            As a professional drummer and audio engineer with over a decade in the industry, I've poured my expertise into these Superior Drummer 3 presets. Each sound reflects my hands-on experience both behind the kit and in the studio.\n                        </p>\n                    </div>\n                </div>\n\n                <div class=\"ddstudio-card\">\n                    <div class=\"ddstudio-card__image-wrapper\">\n                        <img src=\"https://developdevice.com/cdn/shop/files/PD_DRUMS.webp?v=1724398797&width=832\" alt=\"Drum Software Interface\" class=\"ddstudio-card__image\">\n                    </div>\n                    <div class=\"ddstudio-card__content\">\n                        <h2 class=\"ddstudio-card__title\">Studio-Grade Sounds, Instantly Playable</h2>\n                        <p class=\"ddstudio-card__text\">\n                            I've personally engineered these presets for immediate professional results. Every drum sound is meticulously crafted to deliver the perfect balance of punch, clarity, and character – right out of the box.\n                        </p>\n                    </div>\n                </div>\n\n                <div class=\"ddstudio-card\">\n                    <div class=\"ddstudio-card__image-wrapper\">\n                        <img src=\"https://developdevice.com/cdn/shop/files/IMG_8397.webp?v=1724398314&width=832\" alt=\"Studio Setup\" class=\"ddstudio-card__image\">\n                    </div>\n                    <div class=\"ddstudio-card__content\">\n                        <h2 class=\"ddstudio-card__title\">Professional Mix Engine, Zero Plugins Needed</h2>\n                        <p class=\"ddstudio-card__text\">\n                            I've leveraged Superior Drummer 3's powerful architecture to deliver fully mixed, production-ready sounds. Every preset is carefully balanced and processed within the plugin itself, streamlining your workflow without sacrificing quality.\n                        </p>\n                    </div>\n                </div>\n\n                <div class=\"ddstudio-card\">\n                    <div class=\"ddstudio-card__image-wrapper\">\n                        <img src=\"https://developdevice.com/cdn/shop/files/IMG_0731_225f7553-9357-431f-9597-97a08c508e37.webp?v=1724398279&width=832\" alt=\"Electronic Drum Kit\" class=\"ddstudio-card__image\">\n                    </div>\n                    <div class=\"ddstudio-card__content\">\n                        <h2 class=\"ddstudio-card__title\">Optimized for Your Electronic Kit</h2>\n                        <p class=\"ddstudio-card__text\">\n                            Drawing from my extensive experience with electronic drums, I've fine-tuned each preset to respond naturally to your playing dynamics. Experience authentic acoustic feel with the convenience and consistency of digital technology.\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', () => {\n    const section = document.querySelector('.ddstudio-background-section');\n\n    if (section) { // Přidána kontrola, zda element existuje\n        section.addEventListener('mouseenter', () => {\n            section.classList.add('glow-active');\n        });\n\n        section.addEventListener('mouseleave', () => {\n            section.classList.remove('glow-active');\n        });\n\n        section.addEventListener('mousemove', (e) => {\n            const rect = section.getBoundingClientRect();\n            const x = ((e.clientX - rect.left) / rect.width) * 100;\n            const y = ((e.clientY - rect.top) / rect.height) * 100;\n\n            section.style.setProperty('--mouse-x', x + '%');\n            section.style.setProperty('--mouse-y', y + '%');\n        });\n    }\n});\n</script>\n{% endif %}", "background_color": "", "text_color": ""}}, "custom_liquid_7qgDRd": {"type": "custom-liquid", "settings": {"full_width": false, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.type == 'Superior Drummer 3 Presets' %}\n<div class=\"sd3-benefits-24\">\n  <style>\n    @keyframes fadeInUp {\n      from {\n        opacity: 0;\n        transform: translateY(20px);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0);\n      }\n    }\n\n    @keyframes pulse {\n      0% {\n        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);\n      }\n      70% {\n        box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);\n      }\n      100% {\n        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);\n      }\n    }\n\n    @keyframes shimmer {\n      0% {\n        background-position: -1000px 0;\n      }\n      100% {\n        background-position: 1000px 0;\n      }\n    }\n\n    .sd3-benefits-24 {\n      padding: 0;\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\n      transition: box-shadow 0.5s ease, transform 0.5s ease, border-color 0.5s ease;\n      position: relative;\n      z-index: 1;\n      margin: 30px 0;\n      overflow: hidden;\n      animation: fadeInUp 0.8s ease-out forwards;\n      border-radius: 24px; /* Zaoblení pro sekci */\n    }\n\n    .sd3-hero-section {\n      position: relative;\n      width: 100%;\n      height: 400px;\n      overflow: hidden;\n      /* Pokud chcete i horní rohy hero sekce zaoblit spolu s rodičem, přidejte: */\n      /* border-top-left-radius: 24px; */\n      /* border-top-right-radius: 24px; */\n    }\n\n    .sd3-hero-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      object-position: center;\n      display: block;\n      transition: transform 0.8s ease;\n    }\n\n    .sd3-benefits-24:hover .sd3-hero-image {\n      transform: scale(1.05);\n    }\n\n    .sd3-gradient-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      height: 150px;\n      background: linear-gradient(180deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 0.9) 80%,\n        rgba(255, 255, 255, 1) 100%\n      );\n    }\n\n    .sd3-content-wrapper {\n      padding: 28px;\n      background: #ffffff;\n      position: relative;\n    }\n\n    .sd3-title-24 {\n      font-size: 28px;\n      font-weight: 700;\n      margin-bottom: 25px;\n      text-align: center;\n      color: #000000;\n      position: relative;\n      display: inline-block;\n      width: 100%;\n    }\n\n    .sd3-title-24::after {\n      content: '';\n      position: absolute;\n      bottom: -10px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 80px;\n      height: 3px;\n      background: #000000;\n    }\n\n    .sd3-grid-24 {\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 24px;\n      opacity: 0;\n      animation: fadeInUp 0.8s ease-out forwards;\n      animation-delay: 0.3s;\n    }\n\n    @media (max-width: 640px) {\n      .sd3-grid-24 {\n        grid-template-columns: 1fr;\n      }\n\n      .sd3-hero-section {\n        height: 300px;\n      }\n    }\n\n    .sd3-item-24 {\n      padding: 24px;\n      background: #ffffff;\n      border: 1px solid rgba(0, 0, 0, 0.08);\n      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n      position: relative;\n      overflow: hidden;\n      opacity: 0;\n      transform: translateY(15px);\n      animation: fadeInUp 0.6s ease-out forwards;\n      border-radius: 24px; /* Zaoblení pro položky sekce */\n    }\n\n    .sd3-item-24:nth-child(1) { animation-delay: 0.4s; }\n    .sd3-item-24:nth-child(2) { animation-delay: 0.5s; }\n    .sd3-item-24:nth-child(3) { animation-delay: 0.6s; }\n    .sd3-item-24:nth-child(4) { animation-delay: 0.7s; }\n\n    .sd3-item-24::before {\n      content: '';\n      position: absolute;\n      top: -5px;\n      left: -5px;\n      right: -5px;\n      bottom: -5px;\n      background: linear-gradient(45deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));\n      z-index: -1;\n      transform: scale(0.98);\n      opacity: 0;\n      transition: all 0.4s ease;\n      /* Pokud chcete i before element zaoblit, přidejte border-radius i sem: */\n      /* border-radius: 28px; /* o něco více než rodič kvůli scale */\n    }\n\n    .sd3-item-24:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);\n      border-color: rgba(0, 0, 0, 0.2);\n    }\n\n    .sd3-item-24:hover::before {\n      opacity: 1;\n      transform: scale(1);\n    }\n\n    .sd3-icon-24 {\n      width: 46px;\n      height: 46px;\n      margin-bottom: 18px;\n      fill: #000000;\n      transition: all 0.3s ease;\n      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\n    }\n\n    .sd3-item-24:hover .sd3-icon-24 {\n      transform: scale(1.1) rotate(5deg);\n      fill: #000000;\n    }\n\n    .sd3-item-title-24 {\n      font-size: 20px;\n      font-weight: 600;\n      margin-bottom: 12px;\n      color: #000000;\n      transition: color 0.3s ease;\n    }\n\n    .sd3-item-24:hover .sd3-item-title-24 {\n      color: #000000;\n    }\n\n    .sd3-text-24 {\n      font-size: 15px;\n      line-height: 1.6;\n      color: #333333;\n    }\n\n    @media (max-width: 768px) {\n      .sd3-content-wrapper {\n        padding: 24px;\n      }\n\n      .sd3-title-24 {\n        font-size: 24px;\n      }\n\n      .sd3-item-24 {\n        padding: 20px;\n      }\n    }\n\n    /* Tato třída sd3-video-container již v původním kódu měla border-radius: 4px; */\n    /* Měním na 24px podle požadavku na \"Sekce\" */\n    .sd3-video-container {\n      width: 100%;\n      max-width: 800px; /* Původní hodnota, pokud máte jinou, upravte */\n      margin: 40px auto 20px;\n      border: 1px solid rgba(0,0,0,0.1);\n      box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n      border-radius: 24px; /* Zaoblení pro video sekci */\n      overflow: hidden;\n      position: relative; /* Přidáno pro správné fungování ::before a video elementu */\n    }\n\n    .sd3-video-container::before {\n      content: '';\n      display: block;\n      padding-top: 56.25%; /* Pro 16:9 poměr stran */\n    }\n\n    .sd3-video-container video {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      display: block;\n      object-fit: cover;\n      /* Není třeba border-radius zde, protože rodič .sd3-video-container má overflow: hidden */\n    }\n\n\n    .sd3-video-container:focus {\n      outline: none;\n    }\n\n    .sd3-video-container::-webkit-media-controls {\n      opacity: 1;\n    }\n\n    .sd3-disclaimer {\n      font-size: 13px;\n      color: #666;\n      margin-top: 16px;\n      text-align: center;\n      font-style: italic;\n      opacity: 0;\n      animation: fadeInUp 0.8s ease-out forwards;\n      animation-delay: 1s;\n    }\n\n    .cta-button {\n      display: block;\n      width: 80%;\n      max-width: 300px;\n      margin: 30px auto 10px;\n      padding: 14px 24px;\n      background: #000000;\n      color: white;\n      text-align: center;\n      font-weight: 600;\n      font-size: 16px;\n      text-decoration: none;\n      transition: all 0.3s ease;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n      opacity: 0;\n      animation: fadeInUp 0.8s ease-out forwards, pulse 2s infinite;\n      animation-delay: 0.9s;\n      position: relative;\n      overflow: hidden;\n      border-radius: 12px; /* Zaoblení pro tlačítko */\n    }\n\n    .cta-button:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);\n      background: #333333;\n    }\n\n    .cta-button::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 0.3) 50%,\n        rgba(255, 255, 255, 0) 100%\n      );\n      animation: shimmer 3s infinite;\n      animation-delay: 1s;\n    }\n\n    .sd3-benefits-24:hover {\n      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\n      transform: translateY(-4px);\n      border-color: rgba(0, 0, 0, 0.2);\n      z-index: 2;\n    }\n\n    /* Styl pro video v kontejneru `div` níže */\n    /* Tento styl je duplicitní s .sd3-video-container video, ale nechávám pro případ, že byste chtěli specifické úpravy */\n    /* video { */\n      /* display: block; */ /* Již definováno výše */\n    /* } */\n\n  </style>\n\n  <div class=\"sd3-hero-section\">\n    <img\n      src=\"https://cdn.shopify.com/s/files/1/2599/2974/files/P5240016.jpg?v=1699165940\"\n      alt=\"Superior Drummer 3 Presets\"\n      class=\"sd3-hero-image\"\n    />\n    <div class=\"sd3-gradient-overlay\"></div>\n  </div>\n\n  <div class=\"sd3-content-wrapper\">\n    <h2 class=\"sd3-title-24\">Superior Drummer 3 Presets Benefits</h2>\n\n    <div class=\"sd3-grid-24\">\n      <div class=\"sd3-item-24\">\n        <svg class=\"sd3-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2C6.48 2 2 6.48 2\n          12s4.48 10 10 10 10-4.48\n          10-10S17.52 2 12 2zm0 3c1.66\n          0 3 1.34 3 3s-1.34 3-3\n          3-3-1.34-3-3 1.34-3\n          3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99\n          4-3.08 6-3.08 1.99 0 5.97 1.09\n          6 3.08-1.29 1.94-3.5 3.22-6 3.22z\"/>\n        </svg>\n        <h3 class=\"sd3-item-title-24\">Created by a Real Drummer</h3>\n        <p class=\"sd3-text-24\">Each preset is crafted by an experienced drummer, capturing realistic dynamics and nuances to ensure an authentic feel and responsive performance in your productions.</p>\n      </div>\n\n      <div class=\"sd3-item-24\">\n        <svg class=\"sd3-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M3 2h18c1.1 0 2 .9 2\n          2v12c0 1.1-.9 2-2 2h-18c-1.1\n          0-2-.9-2-2v-12c0-1.1.9-2\n          2-2zm0 14h18v-12h-18v12zm16-10h-14v2h14v-2zm-14\n          4h14v2h-14v-2zm14 4h-14v2h14v-2z\"/>\n        </svg>\n        <h3 class=\"sd3-item-title-24\">Instant Professional Sound</h3>\n        <p class=\"sd3-text-24\">Achieve professional-sounding drums instantly, thanks to presets that are expertly mixed and balanced, ready for immediate use in your projects.</p>\n      </div>\n\n      <div class=\"sd3-item-24\">\n        <svg class=\"sd3-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 2C6.47\n          2 2 6.47 2 12s4.47 10 10\n          10 10-4.47 10-10S17.53\n          2 12 2zm0 18c-4.41\n          0-8-3.59-8-8s3.59-8\n          8-8 8 3.59 8\n          8-3.59 8-8 8zm3.59-13L12\n          10.59 8.41 7 7 8.41 10.59\n          12 7 15.59 8.41 17 12\n          13.41 15.59 17 17 15.59\n          13.41 12 17 8.41z\"/>\n        </svg>\n        <h3 class=\"sd3-item-title-24\">Zero Additional Processing</h3>\n        <p class=\"sd3-text-24\">All presets are fully mixed and ready to use, utilizing only Superior Drummer 3's internal processing—no external plugins required.</p>\n      </div>\n\n      <div class=\"sd3-item-24\">\n        <svg class=\"sd3-icon-24\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.3,3.7c-0.4-0.4-1-0.4-1.4,0L17,5.6c-0.4,0.4-0.4,1,0,1.4l3,3c0.4,0.4,1,0.4,1.4,0l1.9-1.9c0.4-0.4,0.4-1,0-1.4L20.3,3.7z\n          M3,17v3h3l11-11l-3-3L3,17z\n          M21,19H3v2h18v-2z\"/>\n        </svg>\n        <h3 class=\"sd3-item-title-24\">E-Kit Optimized</h3>\n        <p class=\"sd3-text-24\">Each preset is specifically optimized for electronic drum kits, providing a natural playing feel and seamless digital performance.</p>\n      </div>\n    </div>\n\n    <a href=\"#\" class=\"cta-button\">Get Now</a>\n\n    <!-- Simple video implementation -->\n    <!-- Používám třídu .sd3-video-container pro konzistenci, které jsem přidal border-radius: 24px -->\n    <div class=\"sd3-video-container\" style=\"max-width: 1800px;\"> <!-- Použita třída .sd3-video-container -->\n      <video width=\"100%\" height=\"auto\" controls preload=\"none\" poster=\"https://cdn.shopify.com/s/files/1/2599/2974/files/nahled.jpg?v=1743853624\">\n        <source src=\"https://cdn.shopify.com/videos/c/o/v/415c2532095047278f80c028adaf13f8.mp4\" type=\"video/mp4\">\n        Your browser does not support the video tag.\n      </video>\n    </div>\n\n    <!-- Remove scripts and styles that might be causing issues -->\n    <!-- Tento blok <style> je nyní prázdný, protože jeho obsah byl integrován výše, nebo je již pokryt. -->\n    <!-- Můžete ho odstranit, pokud není potřeba. -->\n    <style>\n      /* Simple video styling */\n      /* video { */ /* Již ošetřeno v .sd3-video-container video */\n        /* display: block; */\n      /* } */\n    </style>\n\n    <p class=\"sd3-disclaimer\">This is not an audio sample of a specific preset. It only demonstrates how it functions with an electronic drum kit.</p>\n  </div>\n</div>\n{% endif %}", "background_color": "", "text_color": ""}}, "custom_liquid_NNUjBU": {"type": "custom-liquid", "settings": {"full_width": true, "remove_horizontal_space": true, "remove_vertical_space": true, "custom_liquid": "{% if product.type == \"Superior Drummer 3 Presets\" %}\n<!-- SD3P Loading Guide - Fixed Version -->\n<div class=\"sd3-guide-container\">\n    <div class=\"sd3-header\">\n        <h1 class=\"sd3-title\"><b>Superior Drummer 3 - Loading SD3P Files</b></h1>\n        <p class=\"sd3-subtitle\">A simple, step-by-step guide to load your drum presets, even if you're a complete beginner</p>\n    </div>\n\n    <div class=\"sd3-tabs\">\n        <button class=\"sd3-tab-btn sd3-active\" data-sd3tab=\"basics\">Basic Loading</button>\n        <button class=\"sd3-tab-btn\" data-sd3tab=\"saving\">Quick Access</button>\n        <button class=\"sd3-tab-btn\" data-sd3tab=\"visualize\">Visual Demo</button>\n    </div>\n\n    <div id=\"sd3-basics\" class=\"sd3-tab-content sd3-active\">\n        <h2>Loading Your First SD3P Preset</h2>\n        <p style=\"margin-bottom: 2rem;\">Follow these simple steps to load drum presets into Superior Drummer 3:</p>\n\n        <ol class=\"sd3-steps\">\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">1</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Open Superior Drummer 3</h3>\n                    <p class=\"sd3-step-desc\">Start by launching Superior Drummer 3. You can open it either as a plugin in your music software (like Logic, Pro Tools, or FL Studio) or as a standalone application from your computer.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">2</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Find the File Menu</h3>\n                    <p class=\"sd3-step-desc\">Look at the top of the Superior Drummer 3 window. You'll see a menu bar with several options. Click on \"File\" - it's usually the first option on the left.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">3</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Select the Open Option</h3>\n                    <p class=\"sd3-step-desc\">After clicking \"File,\" a dropdown menu will appear. Find and click on the \"Open\" option in this menu.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">4</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Find Your SD3P File</h3>\n                    <p class=\"sd3-step-desc\">A file browser window will open. Navigate to the folder where you've saved your SD3P preset file. These are special files made for Superior Drummer 3 that contain drum kit settings.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">5</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Open the Preset</h3>\n                    <p class=\"sd3-step-desc\">When you find your SD3P file, click on it once to select it, then click the \"Open\" button. The preset will now load into Superior Drummer 3, and you're ready to start making music!</p>\n                </div>\n            </li>\n        </ol>\n\n        <div class=\"sd3-tip-box\">\n            <h4 class=\"sd3-tip-title\">\n                <svg class=\"sd3-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z\"/>\n                    <path d=\"M11 11h2v6h-2zm0-4h2v2h-2z\"/>\n                </svg>\n                Beginner's Tip\n            </h4>\n            <p>Can't find your SD3P files? They might be in the \"Downloads\" folder if you just downloaded them, or check the location where you saved them. If you're still having trouble, try searching for \".sd3p\" in your computer's search function.</p>\n        </div>\n    </div>\n\n    <div id=\"sd3-saving\" class=\"sd3-tab-content\">\n        <h2>Saving Presets for Quick Access</h2>\n        <p style=\"margin-bottom: 2rem;\">After loading a preset, save it for easy access in future sessions:</p>\n\n        <ol class=\"sd3-steps\">\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">1</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Save Your Loaded Preset</h3>\n                    <p class=\"sd3-step-desc\">After you've loaded the SD3P file, look at the top right corner of Superior Drummer 3. Find the menu labeled \"Drums and Mixer Presets\" (it might have a gear or dropdown icon). Click on it and select \"Save As...\" from the options.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">2</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Give Your Preset a Name</h3>\n                    <p class=\"sd3-step-desc\">A window will appear asking you to name your preset. Choose a name that will help you remember what this preset sounds like (for example, \"Rock Kit - Bright\" or \"Jazz Brushes\"). Click \"Save\" when you're done.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">3</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Refresh the Preset List</h3>\n                    <p class=\"sd3-step-desc\">Go back to the \"Drums and Mixer Presets\" menu and click on \"Rescan User Presets\". This tells Superior Drummer 3 to look for any new presets you've added.</p>\n                </div>\n            </li>\n            <li class=\"sd3-step\">\n                <div class=\"sd3-step-number\">4</div>\n                <div class=\"sd3-step-details\">\n                    <h3 class=\"sd3-step-title\">Find Your Preset for Future Use</h3>\n                    <p class=\"sd3-step-desc\">From now on, you can quickly access your saved preset by clicking on the \"Drums and Mixer Presets\" menu and looking in the \"User Presets\" section. Your saved preset will be there, ready to use with just one click!</p>\n                </div>\n            </li>\n        </ol>\n\n        <div class=\"sd3-tip-box\">\n            <h4 class=\"sd3-tip-title\">\n                <svg class=\"sd3-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z\"/>\n                    <path d=\"M11 11h2v6h-2zm0-4h2v2h-2z\"/>\n                </svg>\n                Organization Tip\n            </h4>\n            <p>Create a system for naming your presets to stay organized. For example, include the genre, mood, or project name in the preset title (like \"Pop-Bright-ProjectX\"). This will help you quickly find the right drum sound when you have many presets.</p>\n        </div>\n    </div>\n\n    <div id=\"sd3-visualize\" class=\"sd3-tab-content\">\n        <h2>Interactive Visual Guide</h2>\n        <p style=\"margin-bottom: 1rem;\">See the process in action with this interactive demonstration. Click through each step to visualize the loading process:</p>\n\n        <div class=\"sd3-visualizer\">\n            <div class=\"sd3-visualizer-screen\">\n                <div class=\"sd3-visualizer-ui\">\n                    <div class=\"sd3-visualizer-topbar\">\n                        <div class=\"sd3-visualizer-logo\">Superior Drummer 3</div>\n                        <div class=\"sd3-visualizer-menu\">\n                            <button class=\"sd3-visualizer-btn sd3-active\">File</button>\n                            <button class=\"sd3-visualizer-btn\">Edit</button>\n                            <button class=\"sd3-visualizer-btn\">View</button>\n                            <button class=\"sd3-visualizer-btn\">Help</button>\n                        </div>\n                    </div>\n                    <div class=\"sd3-visualizer-content sd3-step-1\">\n                        <div class=\"sd3-visualizer-step sd3-visualizer-step-1\">\n                            <div class=\"sd3-visualizer-step-icon\">1</div>\n                            <h3 class=\"sd3-visualizer-step-title\">Launch Superior Drummer 3</h3>\n                            <p class=\"sd3-visualizer-step-desc\">Open the application in your DAW or as a standalone program</p>\n                            <button class=\"sd3-visualizer-btn sd3-next-step\" data-sd3step=\"2\">Next Step</button>\n                        </div>\n                        <div class=\"sd3-visualizer-step sd3-visualizer-step-2\">\n                            <div class=\"sd3-visualizer-step-icon\">2</div>\n                            <h3 class=\"sd3-visualizer-step-title\">Click on \"File\" Menu</h3>\n                            <p class=\"sd3-visualizer-step-desc\">Locate and click on the File option in the top menu bar</p>\n                            <div class=\"sd3-visualizer-btn-group\">\n                                <button class=\"sd3-visualizer-btn sd3-prev-step\" data-sd3step=\"1\">Previous</button>\n                                <button class=\"sd3-visualizer-btn sd3-next-step\" data-sd3step=\"3\">Next Step</button>\n                            </div>\n                        </div>\n                        <div class=\"sd3-visualizer-step sd3-visualizer-step-3\">\n                            <div class=\"sd3-visualizer-step-icon\">3</div>\n                            <h3 class=\"sd3-visualizer-step-title\">Select \"Open\" Option</h3>\n                            <p class=\"sd3-visualizer-step-desc\">From the dropdown menu, click on the Open option</p>\n                            <div class=\"sd3-visualizer-btn-group\">\n                                <button class=\"sd3-visualizer-btn sd3-prev-step\" data-sd3step=\"2\">Previous</button>\n                                <button class=\"sd3-visualizer-btn sd3-next-step\" data-sd3step=\"4\">Next Step</button>\n                            </div>\n                        </div>\n                        <div class=\"sd3-visualizer-step sd3-visualizer-step-4\">\n                            <div class=\"sd3-visualizer-step-icon\">4</div>\n                            <h3 class=\"sd3-visualizer-step-title\">Browse to SD3P Location</h3>\n                            <p class=\"sd3-visualizer-step-desc\">Navigate through your folders to find your SD3P preset file</p>\n                            <div class=\"sd3-visualizer-btn-group\">\n                                <button class=\"sd3-visualizer-btn sd3-prev-step\" data-sd3step=\"3\">Previous</button>\n                                <button class=\"sd3-visualizer-btn sd3-next-step\" data-sd3step=\"5\">Next Step</button>\n                            </div>\n                        </div>\n                        <div class=\"sd3-visualizer-step sd3-visualizer-step-5\">\n                            <div class=\"sd3-visualizer-step-icon\">5</div>\n                            <h3 class=\"sd3-visualizer-step-title\">Open the Preset File</h3>\n                            <p class=\"sd3-visualizer-step-desc\">Select your SD3P file and click Open to load it into Superior Drummer 3</p>\n                            <div class=\"sd3-visualizer-btn-group\">\n                                <button class=\"sd3-visualizer-btn sd3-prev-step\" data-sd3step=\"4\">Previous</button>\n                                <button class=\"sd3-visualizer-btn sd3-restart-demo\" data-sd3step=\"1\">Restart Demo</button>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"sd3-visualizer-progress\">\n                        <div class=\"sd3-visualizer-progress-dot sd3-active\" data-sd3step=\"1\"></div>\n                        <div class=\"sd3-visualizer-progress-dot\" data-sd3step=\"2\"></div>\n                        <div class=\"sd3-visualizer-progress-dot\" data-sd3step=\"3\"></div>\n                        <div class=\"sd3-visualizer-progress-dot\" data-sd3step=\"4\"></div>\n                        <div class=\"sd3-visualizer-progress-dot\" data-sd3step=\"5\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"sd3-faq\">\n        <h2 class=\"sd3-faq-title\"><b>Frequently Asked Questions</b></h2>\n\n        <div class=\"sd3-faq-item\">\n            <div class=\"sd3-faq-question\">\n                What is an SD3P file?\n                <div class=\"sd3-faq-toggle\">\n                    <svg class=\"sd3-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M6 9l6 6 6-6\"/>\n                    </svg>\n                </div>\n            </div>\n            <div class=\"sd3-faq-answer\">\n                <p>An SD3P file is a preset file format used by Superior Drummer 3. These files contain all the settings for a complete drum kit, including which drum samples are used, how they're mixed, what effects are applied, and more. Think of it as a \"snapshot\" of a perfectly set-up drum kit that you can instantly load.</p>\n            </div>\n        </div>\n\n        <div class=\"sd3-faq-item\">\n            <div class=\"sd3-faq-question\">\n                Where can I find SD3P files?\n                <div class=\"sd3-faq-toggle\">\n                    <svg class=\"sd3-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M6 9l6 6 6-6\"/>\n                    </svg>\n                </div>\n            </div>\n            <div class=\"sd3-faq-answer\">\n                <p>SD3P files can come from several sources:</p>\n                <ul style=\"margin-top: 0.5rem; margin-left: 1.5rem;\">\n                    <li>Included with Superior Drummer 3 and its expansion packs</li>\n                    <li>Downloaded from Toontrack's website (some are free, others are paid)</li>\n                    <li>Created by other users and shared online</li>\n                    <li>Made by yourself when you save your own drum setups</li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"sd3-faq-item\">\n            <div class=\"sd3-faq-question\">\n                Can I modify an SD3P preset after loading it?\n                <div class=\"sd3-faq-toggle\">\n                    <svg class=\"sd3-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M6 9l6 6 6-6\"/>\n                    </svg>\n                </div>\n            </div>\n            <div class=\"sd3-faq-answer\">\n                <p>Yes! Loading an SD3P preset is just a starting point. After loading it, you can adjust any part of the drum kit to your liking. You can change the drum sounds, adjust the mix, add or remove effects, and make any other changes you want. When you're happy with your modifications, you can save it as a new preset for future use.</p>\n            </div>\n        </div>\n\n        <div class=\"sd3-faq-item\">\n            <div class=\"sd3-faq-question\">\n                What if my SD3P file won't load properly?\n                <div class=\"sd3-faq-toggle\">\n                    <svg class=\"sd3-icon\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\">\n                        <path fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" d=\"M6 9l6 6 6-6\"/>\n                    </svg>\n                </div>\n            </div>\n            <div class=\"sd3-faq-answer\">\n                <p>If you're having trouble loading an SD3P file, try these troubleshooting steps:</p>\n                <ul style=\"margin-top: 0.5rem; margin-left: 1.5rem;\">\n                    <li>Make sure you have the correct expansion pack installed that the preset requires</li>\n                    <li>Check if your Superior Drummer 3 is updated to the latest version</li>\n                    <li>Try restarting Superior Drummer 3 and your DAW</li>\n                    <li>Verify the file isn't corrupted by trying to load a different SD3P file</li>\n                    <li>Check Toontrack's support forum for specific issues related to the preset you're trying to use</li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</div>\n\n<style>\n/* Superior Drummer 3 Guide - Namespaced Styles */\n.sd3-guide-container {\n    max-width: 1800px;\n    margin: 2rem auto;\n    padding: 2rem 1.5rem;\n}\n\n.sd3-header {\n    margin-bottom: 3rem;\n    text-align: center;\n}\n\n.sd3-title {\n    font-size: 2.5rem;\n    color: #fff; \n    margin-bottom: 1rem;\n    position: relative;\n    display: inline-block;\n}\n\n.sd3-title::after {\n    content: '';\n    position: absolute;\n    height: 4px;\n    width: 80px;\n    background: #FFFFFF; \n    bottom: -10px;\n    left: 50%;\n    transform: translateX(-50%);\n}\n\n.sd3-subtitle {\n    font-size: 1.2rem;\n    color: #fff; \n    max-width: 700px;\n    margin: 1.5rem auto 0;\n}\n\n.sd3-tabs {\n    display: flex;\n    gap: 1rem;\n    margin-bottom: 2rem;\n    justify-content: center;\n    flex-wrap: wrap;\n}\n\n.sd3-tab-btn {\n    padding: 0.75rem 1.5rem;\n    border: none;\n    background-color: white; \n    color: #000000;\n    font-weight: 600;\n    cursor: pointer;\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.04);\n    border: 1px solid #f4f4f4;\n    transition: all 0.3s ease;\n    border-radius: 12px; /* ADDED */\n}\n\n.sd3-tab-btn.sd3-active {\n    background-color: #000000; \n    color: white;\n}\n\n.sd3-tab-btn:hover:not(.sd3-active) {\n    background-color: #f1f5f9;\n}\n\n.sd3-tab-content {\n    display: none;\n    background-color: white; \n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.04);\n    border: 1px solid #f4f4f4;\n    padding: 2rem;\n    margin-bottom: 2rem;\n    border-radius: 24px; /* ADDED */\n    overflow: hidden; /* ADDED to contain children */\n}\n\n.sd3-tab-content.sd3-active {\n    display: block;\n    animation: sd3FadeIn 0.5s ease;\n}\n\n.sd3-tab-content h2 {\n    color: #000000; \n}\n.sd3-tab-content p {\n    color: #333333; \n}\n\n@keyframes sd3FadeIn {\n    from { opacity: 0; transform: translateY(10px); }\n    to { opacity: 1; transform: translateY(0); }\n}\n\n.sd3-steps {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n}\n\n.sd3-step {\n    display: flex;\n    margin-bottom: 1.5rem;\n    padding: 1.5rem;\n    background-color: #f8fafc; \n    transition: transform 0.3s ease, box-shadow 0.3s ease;\n    position: relative;\n    overflow: hidden; /* Already present, good for rounded corners */\n    border-radius: 24px; /* ADDED */\n}\n\n.sd3-step:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);\n}\n\n.sd3-step::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background-color: #000000; \n}\n\n.sd3-step-number {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    background-color: #000000; \n    color: white;\n    font-weight: bold;\n    margin-right: 1.5rem;\n    flex-shrink: 0;\n    font-size: 1.2rem;\n    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);\n    border-radius: 24px; /* ADDED - or 12px if it should look more like a button/icon */\n                         /* Using 24px to match other \"section\" like elements visually */\n}\n\n.sd3-step-details {\n    flex: 1;\n}\n\n.sd3-step-title {\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: #000000; \n    margin-bottom: 0.5rem;\n    margin-top: 0;\n}\n\n.sd3-step-desc {\n    color: #000000; \n    margin: 0;\n}\n\n.sd3-tip-box {\n    background-color: #f8f8f8; \n    border-left: 4px solid #f4f4f4; /* Note: border-left might look slightly odd with full border-radius */\n                                      /* Consider removing border-left or adjusting design if it looks strange */\n    padding: 1.5rem;\n    margin-top: 2rem;\n    border-radius: 24px; /* ADDED */\n    overflow: hidden; /* ADDED */\n}\n/* If border-left + border-radius is an issue, an alternative is a ::before pseudo-element for the left bar */\n/* For now, let's see how it looks */\n\n\n.sd3-tip-title {\n    display: flex;\n    align-items: center;\n    font-weight: 600;\n    color: #000000; \n    margin-bottom: 0.75rem;\n    margin-top: 0;\n}\n.sd3-tip-box p {\n    color: #333333; \n}\n\n.sd3-icon {\n    margin-right: 0.5rem;\n    fill: #000000; \n}\n\n.sd3-faq {\n    margin-top: 4rem;\n}\n\n.sd3-faq-title {\n    font-size: 1.75rem;\n    color: #FFFFFF; \n    margin-bottom: 1.5rem;\n    text-align: center;\n}\n\n.sd3-faq-item {\n    margin-bottom: 1rem;\n    border: 1px solid #f4f4f4; \n    overflow: hidden; /* Already present, good for rounded corners */\n    border-radius: 24px; /* ADDED */\n}\n\n.sd3-faq-question {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 1.25rem;\n    background-color: white; \n    cursor: pointer;\n    font-weight: 600;\n    color: #000000;\n    transition: background-color 0.3s ease;\n    /* Note: if .sd3-faq-item has border-radius, this direct child might not need it if bg is same */\n    /* But if bg differs or for active state, it's good to have control */\n}\n\n.sd3-faq-question:hover {\n    background-color: #f8fafc;\n}\n\n.sd3-faq-toggle svg {\n    transition: transform 0.3s ease;\n}\n\n.sd3-faq-answer {\n    padding: 0 1.25rem;\n    max-height: 0;\n    overflow: hidden;\n    transition: all 0.3s ease;\n    background-color: #fdfdfd; \n}\n.sd3-faq-answer p, .sd3-faq-answer ul {\n    color: #333333; \n}\n\n\n.sd3-faq-item.sd3-active .sd3-faq-question {\n    background-color: #f1f1f1; \n    /* If .sd3-faq-item has 24px, the top part of question should ideally match */\n    /* This can be tricky if only one child is visible at a time */\n    /* For simplicity, .sd3-faq-item gets the radius, and its children fit within it */\n}\n\n.sd3-faq-item.sd3-active .sd3-faq-answer {\n    padding: 1.25rem;\n    max-height: 500px; \n}\n\n.sd3-faq-item.sd3-active .sd3-faq-toggle svg {\n    transform: rotate(180deg);\n}\n\n.sd3-visualizer {\n    margin: 2rem 0;\n    position: relative;\n    height: 350px;\n    overflow: hidden; /* Already present */\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.04);\n    border: 1px solid #444; \n    border-radius: 24px; /* ADDED */\n}\n\n.sd3-visualizer-screen {\n    background-color: #000000; \n    height: 100%;\n    width: 100%;\n    position: relative;\n    overflow: hidden; /* ADDED */\n    border-radius: 24px; /* ADDED (or inherit from parent if it clips) */\n                         /* Explicitly adding it here for clarity */\n}\n\n.sd3-visualizer-ui {\n    padding: 1.5rem;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.sd3-visualizer-topbar {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 1rem;\n}\n\n.sd3-visualizer-logo {\n    color: white; \n    font-weight: 700;\n    font-size: 1.25rem;\n}\n\n.sd3-visualizer-menu {\n    display: flex;\n    gap: 1rem;\n}\n\n.sd3-visualizer-btn {\n    background-color: rgba(255, 255, 255, 0.1); \n    color: white; \n    border: none;\n    padding: 0.5rem 1rem;\n    cursor: pointer;\n    transition: background-color 0.3s ease;\n    border-radius: 12px; /* ADDED */\n}\n\n.sd3-visualizer-btn:hover, .sd3-visualizer-btn.sd3-active {\n    background-color: #ffffff; \n    color: #000000; \n}\n\n.sd3-visualizer-content {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-direction: column;\n}\n\n.sd3-visualizer-step {\n    display: none;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    color: white; \n    max-width: 500px;\n}\n\n.sd3-visualizer-content.sd3-step-1 .sd3-visualizer-step-1,\n.sd3-visualizer-content.sd3-step-2 .sd3-visualizer-step-2,\n.sd3-visualizer-content.sd3-step-3 .sd3-visualizer-step-3,\n.sd3-visualizer-content.sd3-step-4 .sd3-visualizer-step-4,\n.sd3-visualizer-content.sd3-step-5 .sd3-visualizer-step-5 {\n    display: flex;\n}\n\n.sd3-visualizer-step-icon {\n    margin-bottom: 1rem;\n    width: 64px;\n    height: 64px;\n    background-color: rgba(255, 255, 255, 0.1); \n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.5rem;\n    border-radius: 24px; /* ADDED (will make it very rounded, almost circular) */\n}\n\n.sd3-visualizer-step-title {\n    font-size: 1.5rem;\n    margin-bottom: 0.75rem;\n    margin-top: 0;\n}\n\n.sd3-visualizer-step-desc {\n    color: rgba(255, 255, 255, 0.7); \n    margin-bottom: 1.5rem;\n    margin-top: 0;\n}\n\n.sd3-visualizer-btn-group {\n    display: flex;\n    gap: 0.5rem;\n}\n\n.sd3-visualizer-progress {\n    display: flex;\n    gap: 0.5rem;\n    margin-top: 1.5rem;\n    justify-content: center;\n}\n\n.sd3-visualizer-progress-dot {\n    width: 12px;\n    height: 12px;\n    background-color: rgba(255, 255, 255, 0.3); \n    cursor: pointer;\n    transition: all 0.3s ease;\n    border-radius: 50%; /* Make dots circular */\n}\n\n.sd3-visualizer-progress-dot.sd3-active {\n    background-color: #ffffff; \n    transform: scale(1.2);\n}\n\n@media (max-width: 768px) {\n    .sd3-title {\n        font-size: 2rem;\n    }\n\n    .sd3-subtitle {\n        font-size: 1rem;\n    }\n\n    .sd3-visualizer {\n        height: 300px;\n    }\n\n    .sd3-step {\n        flex-direction: column;\n    }\n\n    .sd3-step-number {\n        margin-right: 0;\n        margin-bottom: 1rem;\n    }\n}\n</style>\n\n<script>\n// Wait for DOM to be fully loaded\ndocument.addEventListener('DOMContentLoaded', function() {\n  // Tab functionality\n  var tabButtons = document.querySelectorAll('.sd3-tab-btn');\n  tabButtons.forEach(function(button) {\n    button.addEventListener('click', function() {\n      // Get tab ID\n      var tabId = this.getAttribute('data-sd3tab');\n\n      // Remove active class from all buttons and content\n      document.querySelectorAll('.sd3-tab-btn').forEach(function(btn) {\n        btn.classList.remove('sd3-active');\n      });\n      document.querySelectorAll('.sd3-tab-content').forEach(function(content) {\n        content.classList.remove('sd3-active');\n      });\n\n      // Add active class to current button and content\n      this.classList.add('sd3-active');\n      document.getElementById('sd3-' + tabId).classList.add('sd3-active');\n    });\n  });\n\n  // FAQ accordion functionality\n  var faqItems = document.querySelectorAll('.sd3-faq-item');\n  faqItems.forEach(function(item) {\n    var question = item.querySelector('.sd3-faq-question');\n    if (question) {\n      question.addEventListener('click', function() {\n        // Toggle active class\n        item.classList.toggle('sd3-active');\n      });\n    }\n  });\n\n  // Visualizer step navigation\n  var stepButtons = document.querySelectorAll('.sd3-next-step, .sd3-prev-step, .sd3-restart-demo');\n  var progressDots = document.querySelectorAll('.sd3-visualizer-progress-dot');\n\n  // Function to navigate to a specific step\n  function goToStep(stepNumber) {\n    // Update visualizer content class\n    var visualizerContent = document.querySelector('.sd3-visualizer-content');\n    if (visualizerContent) {\n      visualizerContent.className = 'sd3-visualizer-content sd3-step-' + stepNumber;\n    }\n\n    // Update active progress dot\n    progressDots.forEach(function(dot) {\n      if (dot.getAttribute('data-sd3step') === stepNumber) {\n        dot.classList.add('sd3-active');\n      } else {\n        dot.classList.remove('sd3-active');\n      }\n    });\n  }\n\n  // Add click events to step buttons\n  stepButtons.forEach(function(button) {\n    button.addEventListener('click', function() {\n      var step = this.getAttribute('data-sd3step');\n      if (step) {\n        goToStep(step);\n      }\n    });\n  });\n\n  // Add click events to progress dots\n  progressDots.forEach(function(dot) {\n    dot.addEventListener('click', function() {\n      var step = this.getAttribute('data-sd3step');\n      if (step) {\n        goToStep(step);\n      }\n    });\n  });\n});\n</script>\n{% endif %}", "background_color": "", "text_color": ""}}, "testimonials_qrfWQB": {"type": "testimonials", "blocks": {"testimonial_L6PV4R": {"type": "testimonial", "repeater": "{{ product.metafields.custom.artist_testimonials.value }}", "settings": {"avatar": "{{ block.repeater.artist_photo.value }}", "rating": 5, "show_rating": true, "author": "{{ block.repeater.band_profession.value }}", "heading": "{{ block.repeater.artist_name.value }}", "content": "<p>{{ block.repeater.testimonial_text | metafield_tag }}</p>"}}}, "block_order": ["testimonial_L6PV4R"], "settings": {"full_width": true, "carousel": false, "full_height": true, "subheading": "", "heading": "What Artists Say About My Products", "heading_size": "h2", "content": "", "text_alignment": "text-center", "background_color": "", "text_color": "", "heading_color": "", "testimonial_background_color": "#ffffff", "testimonial_text_color": "#1d1d1d", "testimonial_heading_color": "#1d1d1d", "rating_star_color": "", "heading_html_tag": "p"}}, "related-products": {"type": "related-products", "settings": {"full_width": true, "carousel": true, "products_count": 8, "grid_columns": 4, "grid_columns_mobile": "2", "subheading": "", "heading": "You may also like", "heading_size": "h2", "content": "", "text_alignment": "text-center", "background_color": "", "text_color": "", "heading_color": "", "heading_html_tag": "p"}}, "17474710138b0e56ba": {"type": "apps", "blocks": {"judge_me_reviews_review_widget_qEVLga": {"type": "shopify://apps/judge-me-reviews/blocks/review_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8", "settings": {}}}, "block_order": ["judge_me_reviews_review_widget_qEVLga"], "settings": {"full_width": true, "remove_horizontal_space": false, "remove_vertical_space": false, "background_color": "", "text_color": ""}}}, "order": ["main", "main-details", "custom_liquid_KMaFaw", "custom_liquid_hNBzAn", "custom_liquid_YgtwTk", "custom_liquid_wKND9g", "custom_liquid_J8AQJN", "custom_liquid_wLJNxr", "custom_liquid_7qgDRd", "custom_liquid_NNUjBU", "testimonials_qrfWQB", "related-products", "17474710138b0e56ba"]}